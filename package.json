{"name": "xticket", "version": "2.0.20", "version-live": "2.0.19", "private": true, "appName": "XWORK", "moduleName": "Ticket", "featureName": "XticketPlugin", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint ./src", "test": "jest", "pod-install": "cd ios && pod install", "start": "STANDALONE=1 react-native webpack-start --port 9000", "start9000": "STANDALONE=1 react-native webpack-start --port 9000", "build-bundle": "react-native webpack-bundle --platform ios --entry-file index.js --dev=true", "build-bundle-ios": "react-native webpack-bundle --platform ios --entry-file index.js --dev=false", "build-bundle-android": "react-native webpack-bundle --platform android --entry-file index.js --dev=false", "prepare": "husky install"}, "dependencies": {"@bam.tech/react-native-image-resizer": "3.0.5", "@mwg-kits/common": "^0.0.4", "@mwg-kits/components": "0.1.22", "@mwg-kits/core": "^0.1.18", "@mwg-sdk/styles": "^1.0.5", "@react-native-async-storage/async-storage": "1.17.10", "@react-native-camera-roll/camera-roll": "7.5.2", "@react-native-clipboard/clipboard": "1.14.0", "@react-native-community/masked-view": "0.1.11", "@react-native-community/netinfo": "9.3.8", "@react-navigation/native": "5.x", "@react-navigation/native-stack": "6.9.9", "@react-navigation/stack": "5.x", "@rneui/base": "4.0.0-rc.6", "@rneui/themed": "4.0.0-rc.6", "base-64": "0.1.0", "crypto-js": "^4.2.0", "eventemitter3": "5.0.0", "events": "3.3.0", "moment": "2.23.0", "react": "18.2.0", "react-native": "0.73.6", "react-native-blob-util": "^0.19.8", "react-native-calendar-picker": "7.1.4", "react-native-camera": "git+https://sourceapp.tgdd.vn/appplugins/react-native-camera.git#dev.tan", "react-native-config": "1.5.1", "react-native-countdown-circle-timer": "^3.2.1", "react-native-document-picker": "8.1.2", "react-native-fast-image": "8.6.3", "react-native-gesture-handler": "2.9.0", "react-native-image-crop-picker": "0.40.0", "react-native-image-zoom-viewer": "3.0.1", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-modal": "13.0.1", "react-native-parsed-text": "0.0.22", "react-native-progress": "5.0.0", "react-native-reanimated": "2.17.0", "react-native-reanimated-carousel": "3.5.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.4.1", "react-native-safe-area-view": "1.1.1", "react-native-screens": "3.18.2", "react-native-svg": "https://sourceapp.tgdd.vn/appplugins/react-native-svg.git#develop", "react-native-tags": "2.2.1", "react-native-toast-message": "2.1.5", "react-native-typing-animation": "0.1.7", "react-native-vector-icons": "9.2.0", "react-redux": "8.0.4", "redux": "4.2.0", "redux-logger": "3.0.6", "redux-micro-frontend": "1.3.0", "redux-thunk": "2.4.1", "ts-loader": "9.4.2"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/eslint-parser": "^7.21.3", "@babel/plugin-proposal-decorators": "^7.13.15", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.12.5", "@callstack/repack": "^3.1.1", "@ken/eslint-config-javascript-standard-reactnative": "git+https://sourceapp.tgdd.vn/appplugins/eslint-config-javascript-standard-reactnative.git", "@react-native-community/eslint-config": "^3.0.2", "@tsconfig/react-native": "^2.0.2", "@types/crypto-js": "^4", "@types/jest": "^28.1.2", "@types/react": "~17.0.21", "@types/react-native": "0.70.0", "@types/react-native-typing-animation": "^0.1.5", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^26.6.3", "babel-loader": "^9.1.2", "babel-plugin-import-graphql": "^2.8.1", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "commitlint": "^17.0.2", "compression-webpack-plugin": "^10.0.0", "del-cli": "^5.0.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-native": "^4.0.0", "husky": ">=6", "jest": "^28.1.1", "lint-staged": ">=10", "metro-react-native-babel-preset": "0.72.3", "prettier": "^2.0.5", "react-test-renderer": "18.1.0", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "typescript": "^4.5.2", "webpack": "^5.75.0"}, "jest": {"preset": "react-native"}, "lint-staged": {"*.js": "eslint --cache --fix ./src", "*.{js,css,md}": "prettier --write ./src"}, "packageManager": "yarn@4.1.1"}