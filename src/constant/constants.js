const { translate } = global.props.getTranslateConfig();

export const listVAT = [
    {
        id: 0,
        name: translate('before_vat')
    },
    {
        id: 1,
        name: translate('after_vat')
    }
];
export const listHeader = [
    {
        title: translate('information'),
        id: 0
    },
    {
        title: translate('comment'),
        id: 1
    },
    {
        title: translate('work_need'),
        id: 2
    }
];
export const dropdownUploadFile = [
    {
        name: translate('upload_video'),
        id: 0
    },
    {
        name: translate('upload_file'),
        id: 2
    }
];
export const dropdownTakePic = [
    {
        name: translate('upload_video'),
        id: 0
    },
    {
        name: translate('takepic'),
        id: 2
    }
];
