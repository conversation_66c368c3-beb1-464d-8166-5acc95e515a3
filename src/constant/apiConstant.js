import Config from 'react-native-config';

export const WS_URL = Config.HOST_TICKET + Config.TICKET_SERVICE;
export const HOST_FEEDBACK = Config.HOST_FEEDBACK;
export const TICKET_URL = `${WS_URL}/api/ticket/`;
export const SUGGET_CMT_URL = `${WS_URL}/api/`;
export const ECONTRACT_URL = Config.MWG_CONTRACT_SERVICE;
export const WS_URL_IMAGE = 'https://wsticket.tgdd.vn/user-service';
export const baseAvatarURI = WS_URL_IMAGE + '/api/user/profile/image/';
export const WS_URL_MWG_TICKET = Config.HOST_TICKET + Config.MWG_TICKET_SERVICE;
export const WS_QUICK_TICKET = `${WS_URL_MWG_TICKET}/api/ticketsp/`;
export const API_GET_HASHTAG = `${WS_QUICK_TICKET}hashtag/list`;
export const API_COPY_TO_GROUP = `${WS_QUICK_TICKET}copytogroup`;
export const MEDIA_LINK = Config.HOST_IMAGE + 'mwg-app-media-service';
export const HOST_MEDIA_FILE = MEDIA_LINK + '/api/media/file/';

///API GROUP TICKET
export const API_GET_LIST_TICKET = `${TICKET_URL}relate/list`;
export const API_GET_MY_GROUP_TICKET = `${TICKET_URL}v2/supportservice/list/assigned/search`;
export const API_GET_DETAIL_GROUP = `${TICKET_URL}supportservice/detail`;
export const API_CREATE_GROUP_TICKET = `${TICKET_URL}v3/supportservice/create`;
export const API_GET_LIST_MEMBER_GROUP = `${TICKET_URL}v4/user/list/byjoined`;
export const API_REMOVE_MEMBER_GROUP = `${TICKET_URL}supportservice/member/v2/remove`;
export const API_GET_LIST_SERVICE_TICKET = `${TICKET_URL}list/service`;
export const API_UPDATE_GROUP_TICKET = `${TICKET_URL}v3/supportservice/update`;
export const API_GROUP_ROLE = `${TICKET_URL}list/grouprole`;
export const API_ADD_MEMBER_GROUP = `${TICKET_URL}supportservice/member/invite`;
export const API_UPDATE_STATUS_TICKET = `${TICKET_URL}updatestatus`;
export const API_GET_ALL_STATUS_TICKET = `${TICKET_URL}status/listv2`;
export const API_FILTER_TICKET = `${TICKET_URL}v2/list/bysupportservice`;
export const API_CHANGE_ROLE = `${TICKET_URL}supportservice/member/changerole`;
export const API_GET_TEMPLATE_LIST = `${TICKET_URL}tasktemplate/list`;
export const API_CREATE_WORK = `${TICKET_URL}task/multy/update`;
export const API_REMOVE_FILE = `${TICKET_URL}file/remove`;
export const API_SET_PRIORITY = `${TICKET_URL}supportservice/pin`;
export const API_TROUBLE_STATLISTICAL = `${TICKET_URL}trouble/statistical/list`;

///API TICKET
export const API_GET_DETAIL_TICKET = `${TICKET_URL}v2/relink/detail`;
export const API_UPDATE_TASK = `${TICKET_URL}v2/task/update;`;
export const API_CREATE_TICKET = `${TICKET_URL}/create`;
export const API_GET_LIST_PRIORITY = `${TICKET_URL}/priority/list`;
export const API_GET_LIST_LOCATIONGEO = `${TICKET_URL}locationgeo/list`;
export const API_GET_LIST_FILE_TICKET = `${TICKET_URL}v2/file/list`;
export const API_GET_LIST_ACTIVITY_HISTORY = `${TICKET_URL}history/list`;
export const API_APPROVE_TICKET = `${TICKET_URL}approve`;

export const GET_APPROVE_TYPE_LIST = `${TICKET_URL}approvetype/list`;
export const GET_SUGGEST_COMMENT = `${SUGGET_CMT_URL}suggestcomment/list`;
export const CREATE_UPDATE_SUGGEST_COMMENT = `${SUGGET_CMT_URL}suggestcomment/createupdate`;
export const DELETE_SUGGEST_COMMENT = `${SUGGET_CMT_URL}suggestcomment/changestatus`;

/////////
export const API_GET_STREAM_TOKEN = `${TICKET_URL}file/streamtoken`;
export const API_GET_PROVINCE = `${WS_URL}/location/province/list`;
export const API_GET_DISTRICT = `${WS_URL}/location/district/list`;
export const API_GET_WARD = `${WS_URL}/location/ward/list`;
export const API_UPLOAD_FILE_GROUND = `${TICKET_URL}ground/uploadfile`;
export const API_CREATE_TASK = `${TICKET_URL}task/multy/v3/update`;
export const API_GET_MANAGER = `${TICKET_URL}ground/supervisor/getuserinfo`; //managers or supervisor
export const API_REMOVE_FILE_TASK = `${TICKET_URL}task/file/remove`;
///API COMMNEMT
export const API_LIST_COMMENT = `${TICKET_URL}comment/getlist`;
export const SEND_COMMENT = `${TICKET_URL}v2/comment/create`;
export const API_SEARCH_USER = `${TICKET_URL}list/user`;
export const API_IMAGE_COMMENT = `${TICKET_URL}v2/file/upload`;
export const API_GET_SLA = `${TICKET_URL}sla/detail`;
export const API_GET_LIST_TASK = `${TICKET_URL}v2/task/list`;
export const API_SEARCH_USER_NEW = `${TICKET_URL}user/search`;
export const API_REMOVE_COMMENT = `${TICKET_URL}comment/remove`;
export const SEEN_COMMENT = `${TICKET_URL}comment/seen/update`;
export const API_NEW_FEED = `${TICKET_URL}relate/list`;
export const API_SEARCH_USER_MWG = `${TICKET_URL}list/usermwg`;
export const API_RATING_USER = `${TICKET_URL}ratinguser`;
export const API_RATING_USER_GETINFO = `${TICKET_URL}ratingusergetinfo`;
export const API_GET_CANCEL_REASON = `${TICKET_URL}mbbank/getcancelreason`;
export const API_GET_BANK = `${TICKET_URL}mbbank/getbanks`;
export const API_CANCEL_REQUEST = `${TICKET_URL}mbbank/cancelrequestapprove`;
export const API_CONFIRM_BOT_COMMENT = `${TICKET_URL}bot/confirmcomment`;
