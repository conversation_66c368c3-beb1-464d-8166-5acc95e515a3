const { helper } = require('@mwg-kits/common');

const sliceString = (data) => {
    if (helper.isString(data) && data == '0') {
        return ['0'];
    } // this line is used for formatOnChange
    const newData = data?.toString();
    const result = [];
    if (helper.isString(newData) && newData != '0') {
        const len = newData.length;
        const mod = Math.floor(len / 3);
        for (let index = mod; index >= 0; index--) {
            const begin = -(index + 1) * 3;
            const end = len - index * 3;
            const subString3 = newData?.slice(begin, end);
            if (subString3) {
                result.push(subString3);
            }
        }
    }
    return result;
};
const maskString = (result) => {
    let dataFormat = '';
    if (result.length != 0) {
        dataFormat = result[0];
        for (let i = 1; i < result.length; i++) {
            dataFormat += `.${result[i]}`;
        }
    }
    return dataFormat;
};

export const formatVND = (stringMoney) => {
    console.log(stringMoney, '12321321321321');
    let temp;
    if (helper.isString(stringMoney)) {
        temp = stringMoney.replace(/[,.]/g, '');
    } else {
        // temp = stringMoney.toString().replace(/[,.]/g, '');;
    }
    if (temp.includes('.')) {
        const numberPart = temp.substring(0, temp.indexOf('.'));
        return (
            maskString(sliceString(numberPart)) +
            temp.substring(temp.indexOf('.'))
        );
    } else {
        return maskString(sliceString(temp));
    }
};
