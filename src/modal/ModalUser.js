import React, { Component } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Image,
    FlatList
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { connect } from 'react-redux';
import AntDesign from 'react-native-vector-icons/AntDesign';
import moment from 'moment';
import { CountdownCircleTimer } from 'react-native-countdown-circle-timer';
import { CONST_API } from '../constant';
class ModalUser extends Component {
    constructor(props) {
        super(props);
        this.state = {
            heightSheet: 173
        };
        this.bottomSheetModalRef = React.createRef(null);
    }
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
    };

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
        if (this.props.listUser) {
            if (this.props.listUser?.length > 1) {
                this.setState({ heightSheet: 365 });
            } else {
                this.setState({ heightSheet: 173 });
            }
        }
    }
    componentDidUpdate(prevProps) {
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
        if (this.props.listUser !== prevProps.listUser && this.props.listUser) {
            if (this.props.listUser?.length > 1) {
                this.setState({ heightSheet: 365 });
            } else {
                this.setState({ heightSheet: 173 });
            }
        }
    }

    renderHeader = () => {
        const { onPressDimiss, title } = this.props;

        return (
            <View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        height: 40
                    }}>
                    <TouchableOpacity
                        hitSlop={{
                            top: 5,
                            left: 5,
                            bottom: 5,
                            right: 5
                        }}
                        style={{ flexDirection: 'row' }}
                        onPress={() => {
                            this.bottomSheetModalRef.current?.close();
                            onPressDimiss();
                        }}>
                        <AntDesign
                            name="close"
                            size={20}
                            style={{
                                marginRight: Mixins.scale(12)
                            }}
                        />
                        <MyText
                            text={title}
                            numberOfLines={1}
                            typeFont="semiBold"
                            addSize={3}
                            style={{
                                color: Colors.BLACK
                            }}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    onPressLinkXfone = (item, typeCall) => {
        const { xworkData, detailTicket, onPressCall } = this.props;

        if (detailTicket) {
            const { fullProfile } = xworkData;
            let usernameProfile = fullProfile.username;
            let fullUserCall = `${fullProfile.lastName} ${fullProfile.firstName}`;
            let idTicket = detailTicket?.ticket.id;
            let timeStime = new Date().getTime();
            let usernameAssignee = item.userName;
            let fullUserAssignee = `${item.userName} - ${item.userLastName} ${item?.userFirstName}`;
            let imgUserAssign = `${item?.userImage}`;
            let imgUserCall = `${fullProfile.image}`;
            // let link = `xfone://xwork??video_call??${usernameProfile}??${fullUserCall}??${usernameAssignee}??${fullUserAssignee}??${timeStime}??${imgUserCall}??${imgUserAssign}??${idTicket}`;
            if (detailTicket?.ticket?.videoCall) {
                if (onPressCall) {
                    let data = {};
                    if (typeCall === 'video') {
                        const videoCallData = {
                            from: 'xwork',
                            module: 'xticket',
                            callerID: usernameProfile,
                            callerFullname: fullUserCall,
                            calleeID: usernameAssignee,
                            calleeFullname: fullUserAssignee,
                            timestamp: timeStime,
                            isStartCall: true,
                            callerAvatar: imgUserCall,
                            calleeAvatar: imgUserAssign,
                            ticketID: idTicket
                        };
                        data = videoCallData;
                    } else {
                        const infoCallApps = {
                            numberPhone: item.phoneNumber,
                            fullname: fullUserAssignee,
                            image: imgUserAssign
                        };
                        data = infoCallApps;
                    }
                    onPressCall(data, typeCall);
                }
            }
        }
    };

    _renderCountdownCall = (item) => {
        const { endCallTime } = this.props;
        let countdownTime = 0;
        if (endCallTime !== 0) {
            countdownTime = Math.round(
                (10000 - (new Date().valueOf() - endCallTime)) / 1000
            );
        }
        return (
            <View>
                {countdownTime < 10 && countdownTime >= 0 ? (
                    <View
                        style={{
                            marginHorizontal: Mixins.scale(24)
                        }}>
                        <CountdownCircleTimer
                            isPlaying
                            duration={countdownTime}
                            colors={['#2DCCA7', '#FFFFFF']}
                            colorsTime={[countdownTime, 0]}
                            size={Mixins.scale(30)}
                            trailColor="#FFFFFF"
                            strokeWidth={2}>
                            {({ remainingTime }) => {
                                return remainingTime > 0 ? (
                                    <MyText text={remainingTime} />
                                ) : (
                                    <TouchableOpacity
                                        onPress={() =>
                                            this.onPressLinkXfone(item, 'audio')
                                        }
                                        style={{
                                            justifyContent: 'center'
                                        }}>
                                        <Image
                                            source={{
                                                uri: 'ic_audio_call'
                                            }}
                                            style={Mixins.scaleImage(24, 24)}
                                            resizeMode="contain"
                                        />
                                    </TouchableOpacity>
                                );
                            }}
                        </CountdownCircleTimer>
                    </View>
                ) : (
                    <TouchableOpacity
                        onPress={() => this.onPressLinkXfone(item, 'audio')}
                        style={{
                            justifyContent: 'center',
                            marginHorizontal: Mixins.scale(24)
                        }}>
                        <Image
                            source={{
                                uri: 'ic_audio_call'
                            }}
                            style={Mixins.scaleImage(24, 24)}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                )}
            </View>
        );
    };

    renderContent = () => {
        const { detailTicket } = this.props;
        return (
            <FlatList
                style={{
                    height: '100%',
                    width: '100%',
                    marginTop: Mixins.scale(16)
                }}
                contentContainerStyle={{ paddingBottom: Mixins.scale(80) }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                data={this.props.listUser}
                renderItem={({ item }) => {
                    const fullUsername = `${item?.userName}-${item?.userLastName} ${item?.userFirstName}`;
                    const checkUserAdmin = item.userName.includes('internal');
                    return (
                        <View>
                            {!detailTicket?.ticket?.videoCall ? (
                                <TouchableOpacity
                                    onPress={() => this.onPressLinkXfone(item)}
                                    activeOpacity={1}
                                    style={{
                                        height: Mixins.scale(40),
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        marginBottom: Mixins.scale(16),
                                        width: '100%',
                                        justifyContent: 'space-between'
                                    }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        <Image
                                            style={{
                                                height: Mixins.scale(32),
                                                width: Mixins.scale(32),
                                                borderRadius: Mixins.scale(16),
                                                marginRight: Mixins.scale(4)
                                            }}
                                            source={{
                                                uri: `${CONST_API.baseAvatarURI}${item?.userImage}`
                                            }}
                                        />
                                        <MyText
                                            numberOfLines={1}
                                            text={
                                                checkUserAdmin
                                                    ? 'Admin'
                                                    : fullUsername
                                            }
                                            // addSize={1}
                                            style={{ color: Colors.BLACK }}
                                        />
                                    </View>

                                    <MyText
                                        numberOfLines={1}
                                        text={this.seenTimeLongToString(
                                            item.seenTimeLong
                                        )}
                                        // addSize={1}
                                        style={{ color: Colors.BLACK }}
                                    />
                                </TouchableOpacity>
                            ) : (
                                <View
                                    style={{
                                        height: Mixins.scale(45),
                                        flexDirection: 'row',
                                        marginBottom: Mixins.scale(16),
                                        width: '100%',
                                        justifyContent: 'space-between'
                                    }}>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        <Image
                                            style={{
                                                height: Mixins.scale(32),
                                                width: Mixins.scale(32),
                                                borderRadius: Mixins.scale(16),
                                                marginRight: Mixins.scale(4)
                                            }}
                                            source={{
                                                uri: `${CONST_API.baseAvatarURI}${item?.userImage}`
                                            }}
                                        />
                                        <View>
                                            <MyText
                                                numberOfLines={1}
                                                text={
                                                    checkUserAdmin
                                                        ? 'Admin'
                                                        : fullUsername
                                                }
                                                style={{ color: Colors.BLACK }}
                                            />
                                        </View>
                                    </View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        {/* temporarily hide audio call*/}
                                        {/* {this._renderCountdownCall(item)} */}
                                        <TouchableOpacity
                                            onPress={() =>
                                                this.onPressLinkXfone(
                                                    item,
                                                    'video'
                                                )
                                            }>
                                            <Image
                                                source={{
                                                    uri: 'ic_video_call'
                                                }}
                                                style={Mixins.scaleImage(
                                                    24,
                                                    24
                                                )}
                                                resizeMode="contain"
                                            />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            )}
                        </View>
                    );
                }}
                bounces
            />
        );
    };
    seenTimeLongToString = (time) => {
        return moment(time).format('DD/MM - HH:mm');
    };
    render() {
        const { onPressDimiss, xworkData } = this.props;
        const { BottomSheetGorhom } = xworkData;
        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        onPressDimiss();
                    }
                }}
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        {...props}
                        onPress={onPressDimiss}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                enablePanDownToClose={true}
                style={styles.ctn}>
                <View
                    style={{
                        paddingHorizontal: 16
                    }}>
                    {this.renderHeader()}
                    {this.renderContent()}
                </View>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData
    };
};

const mapDispatchToProps = () => {
    return {};
};
export default connect(mapStateToProps, mapDispatchToProps)(ModalUser);
const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    },
    ctn: {
        flex: 1,
        justifyContent: 'flex-end'
    }
});
