import React, { Component } from 'react';

import {
    StyleSheet,
    View,
    Image,
    TouchableOpacity,
    Dimensions
} from 'react-native';
import Modal from 'react-native-modal';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
const deviceWidth = Dimensions.get('window').width;
const deviceHeight = Dimensions.get('window').height;
const { translate } = global.props.getTranslateConfig();

class ModalNotifi extends Component {
    constructor(props) {
        super(props);
        this.resolvePromise = null;
        this.state = {
            isVisible: false,
            childrenComponent: null
        };
    }

    renderChildern = () => {
        return (
            <View
                style={{
                    backgroundColor: Colors.WHITE,
                    borderRadius: 16,
                    padding: 16,
                    marginHorizontal: 16
                }}>
                {this.renderContent()}
            </View>
        );
    };
    renderContent = () => {
        const {
            title,
            titleStyle,
            showIconClose,
            content,
            onBackdropPress,
            onComfirm
        } = this.props;
        return (
            <View style={{}}>
                {title && (
                    <MyText
                        text={title || ''}
                        addSize={3}
                        style={[
                            {
                                fontWeight: '500',
                                textAlign: showIconClose ? 'left' : 'center'
                            },
                            titleStyle
                        ]}
                    />
                )}
                <MyText text={content || ''} style={{ marginTop: 12 }} />
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginTop: 16
                    }}>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={onBackdropPress}
                        style={styles.btnConfirm}>
                        <MyText
                            addSize={2}
                            style={{ color: Colors.DARK_BLUE_60 }}
                            typeFont={'medium'}
                            text={translate('close')}
                        />
                    </TouchableOpacity>
                    <View style={{ width: Mixins.scale(8) }}></View>
                    <TouchableOpacity
                        activeOpacity={0.8}
                        onPress={() => {
                            onBackdropPress();
                            onComfirm();
                        }}
                        style={styles.btnCancel}>
                        <MyText
                            addSize={2}
                            style={{ color: Colors.WHITE }}
                            typeFont={'medium'}
                            text={translate('transfer')}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    render() {
        const { visible, onBackdropPress } = this.props;
        return (
            <Modal
                onBackdropPress={() => {
                    onBackdropPress();
                }}
                transparent
                useNativeDriver
                statusBarTranslucent
                supportedOrientations={['portrait']}
                hideModalContentWhileAnimating
                animationIn={'fadeIn'}
                animationOut={'fadeOut'}
                animationInTiming={100}
                animationOutTiming={1}
                backdropOpacity={0.8}
                visible={visible}
                deviceWidth={deviceWidth}
                deviceHeight={deviceHeight}
                onBackButtonPress={this.closeModal}
                style={{
                    margin: 0,
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.2)'
                }}>
                <View style={styles.container}>{this.renderChildern()}</View>
            </Modal>
        );
    }
}

export default ModalNotifi;

const styles = StyleSheet.create({
    btnCancel: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: 16,
        flex: 1,
        height: Mixins.scale(48),
        justifyContent: 'center'
    },
    btnConfirm: {
        alignItems: 'center',
        borderColor: Colors.DARK_BLUE_60,
        borderRadius: 16,
        borderWidth: 1,
        flex: 1,
        height: Mixins.scale(48),
        justifyContent: 'center'
    },
    container: {
        flex: 1,
        justifyContent: 'center'
    },
    ctn: {
        flex: 1
    },
    imgTitle: {
        height: 24,
        width: 24
    }
});
