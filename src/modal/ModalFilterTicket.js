import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Image,
    Platform
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import { BackHandler } from 'react-native';
import {
    RenderPlaceholderMember,
    RenderPlaceholderStatus,
    RenderPlaceholderStore,
    RenderSelectUsers,
    RenderStatus,
    RenderStore,
    RenderTime
} from './ComponentModal';
import { ModalListStore } from './ModalListStore';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
const { translate } = global.props.getTranslateConfig();
import * as _actionTicket from '../Tickets/Ticket/action';
import * as _actionHome from '../Tickets/action';
import ModalAddFollowers from './ModalAddFollowers';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ModalFilterTicket extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataFilter: {
                selectedStatus: null,
                selectedMember: null,
                selectedMemberCreated: null,
                selectedMemberFollower: null,
                storeSelected: [],
                startDate: '',
                endDate: '',
                textValue: '',
                selectedFilterFromDay: true,
                supportServiceName: ''
            },
            chooseDate: '',
            isShowCalender: false,
            showModalStore: false,
            dataLengthGeoLocation: 20,
            modalTitle: '',
            showModal: false,
            listSearchUser: [],
            snapPoints: '85%'
        };
        this.bottomSheetModalRef = React.createRef(null);
    }
    initData = () => {
        if (this.props.dataFilter) {
            this.setState({ dataFilter: this.props.dataFilter });
        }
    };

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
        this.initData();

        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
    }
    componentWillUnmount() {
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.handleBackPress
        );
    }
    handlePreventBackAndroid = () => {
        this.props?.onPressDimiss?.();
        this.bottomSheetModalRef.current?.close();
    };
    handleBackPress = () => {
        if (this.props.isVisible && Platform.OS === 'android') {
            this.handlePreventBackAndroid();
            return true;
        }
    };

    componentDidUpdate(prevProps) {
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
    }
    onDateChange(date) {
        this.setState({
            showCalendarFrom: date
        });
    }
    clearState = () => {
        this.setState({
            dataFilter: {
                selectedStatus: null,
                selectedMember: null,
                selectedMemberCreated: null,
                storeSelected: [],
                startDate: '',
                endDate: '',
                textValue: ''
            }
        });
        this.props.saveDataFilter(null);
    };
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
    };
    applyDataFilter = () => {
        const {
            onPressDimiss,
            handleFilter,
            saveDataFilter,
            detailGroupTicket
        } = this.props;
        const {
            selectedMember,
            selectedMemberCreated,
            selectedStatus,
            selectedMemberFollower,
            endDate,
            startDate,
            textValue,
            storeSelected,
            selectedFilterFromDay
        } = this.state.dataFilter;
        let data = {
            selectedStatus: selectedStatus,
            selectedMember: selectedMember,
            selectedMemberCreated: selectedMemberCreated,
            storeSelected: storeSelected,
            selectedMemberFollower: selectedMemberFollower,
            startDate: startDate,
            endDate: endDate,
            textValue: textValue,
            selectedFilterFromDay:
                startDate.length > 0 && endDate.length > 0 ? false : true,
            supportServiceName: detailGroupTicket.data.serviceName
        };
        console.log('applyDataFilterapplyDataFilter', data);
        handleFilter(data);
        saveDataFilter(this.state.dataFilter);
        onPressDimiss();
        this.bottomSheetModalRef.current?.close();
    };
    renderBottomButton = () => {
        const { onPressDimiss, handleRefreshData } = this.props;
        const {
            selectedMember,
            selectedMemberCreated,
            selectedStatus,
            selectedMemberFollower,
            endDate,
            storeSelected,
            textValue
        } = this.state.dataFilter;
        const checkApplyButton =
            !helper.IsValidateObject(selectedMember) &&
            !helper.IsValidateObject(selectedMemberCreated) &&
            // storeSelected?.length === 0 &&
            !helper.IsValidateObject(selectedStatus) &&
            !helper.IsValidateObject(selectedMemberFollower) &&
            textValue?.length === 0 &&
            endDate?.length === 0;

        return (
            <View style={styles.btnBottom}>
                <TouchableOpacity
                    hitSlop={{ top: 20, left: 20, right: 20, bottom: 20 }}
                    onPress={() => {
                        handleRefreshData();
                        this.bottomSheetModalRef.current?.close();
                        this.clearState();
                        onPressDimiss();
                    }}
                    style={styles.btnDelete}>
                    <MyText
                        style={{ color: Colors.GRAYF8 }}
                        addSize={2}
                        text={translate('delete')}
                    />
                </TouchableOpacity>
                <View
                    style={{
                        opacity: checkApplyButton ? 0.5 : 1,
                        flex: 1,
                        height: Mixins.scale(56),
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginLeft: Mixins.scale(6),
                        backgroundColor: Colors.DARK_BLUE_60,
                        borderRadius: 12
                    }}>
                    <TouchableOpacity
                        hitSlop={{ top: 20, left: 20, right: 20, bottom: 20 }}
                        onPress={this.applyDataFilter}
                        disabled={checkApplyButton}
                        style={{
                            height: 56,
                            width: '100%',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <MyText
                            style={{ color: Colors.WHITE }}
                            addSize={2}
                            text={translate('apply')}
                            typeFont="medium"
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    renderHeader = () => {
        const { onPressDimiss } = this.props;
        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: Mixins.scale(12)
                }}>
                <TouchableOpacity
                    hitSlop={{
                        top: 5,
                        left: 5,
                        bottom: 5,
                        right: 5
                    }}
                    style={{ flexDirection: 'row' }}
                    onPress={() => {
                        onPressDimiss();
                        this.bottomSheetModalRef.current?.close();
                    }}>
                    <AntDesign
                        name="close"
                        size={20}
                        style={{
                            marginRight: Mixins.scale(16)
                        }}
                    />
                </TouchableOpacity>
                <MyText
                    text={translate('create_filter')}
                    numberOfLines={1}
                    addSize={3}
                    style={{
                        marginLeft: 12,
                        color: Colors.BLACK
                    }}
                />
            </View>
        );
    };
    handleDimissModalCalendar = () => {
        this.setState({
            isShowCalender: false
        });
    };
    loadMoreLocation = (text, refresh = true) => {
        const { dataLengthGeoLocation } = this.state;
        if (refresh) {
            const data = {
                supportServiceId: -1,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 20,
                    search: text || ''
                },
                requestId: ''
            };
            this.props.actionTicket.getListLocationgeo(data);
            this.setState({
                dataLengthGeoLocation: 20
            });
        } else {
            const data = {
                supportServiceId: -1,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: dataLengthGeoLocation + 10,
                    search: text || ''
                },
                requestId: ''
            };
            this.props.actionTicket.getListLocationgeo(data);
            this.setState({
                dataLengthGeoLocation: dataLengthGeoLocation + 10
            });
        }
    };
    onChangeSelectedDate = (startDate, endDate) => {
        if (startDate && endDate) {
            this.setState((prevState) => ({
                isShowCalender: false,
                dataFilter: {
                    ...prevState.dataFilter,
                    endDate: endDate,
                    startDate: startDate
                }
            }));
        }
    };
    onChangeStatus = (item) => {
        this.setState((prevState) => ({
            dataFilter: {
                ...prevState.dataFilter,
                selectedStatus: item
            }
        }));
    };
    handleAddStore = (listStore = []) => {
        this.setState((prevState) => ({
            dataFilter: { ...prevState.dataFilter, storeSelected: listStore },
            showModalStore: false,
            heightSheet: '90%'
        }));
    };

    renderSelect = (title, list) => {
        const { selectedMember } = this.state.dataFilter;
        const { modalAddReceiver } = this.state;
        const { xworkData } = this.props;
        const { GestureHandler } = xworkData;
        return (
            <View>
                <MyText
                    text={title}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{ marginVertical: 4, color: Colors.BLACK }}
                />
                <TouchableOpacity
                    onPress={() => {
                        this.setState({
                            showModal: true,
                            modalTitle: title,
                            groupUser: list
                            // snapPoints: '5%'
                        });
                    }}
                    style={styles.viewButtonDropDown}>
                    {!list || list?.length === 0 ? (
                        <MyText
                            numberOfLines={1}
                            text={translate('members')}
                            style={{
                                color: Colors.GRAYF9,
                                fontSize: 14
                            }}
                        />
                    ) : (
                        <GestureHandler.ScrollView
                            keyboardShouldPersistTaps="handled"
                            horizontal
                            contentContainerStyle={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginRight: Mixins.scale(4)
                            }}>
                            {list?.map((element, index) => {
                                return (
                                    <View
                                        key={index}
                                        style={{
                                            width: Mixins.scale(100),
                                            borderWidth: 0.5,
                                            height: Mixins.scale(35),
                                            marginRight: Mixins.scale(4),
                                            borderRadius: 12,
                                            backgroundColor:
                                                Colors.DARK_BLUE_10,
                                            borderColor: Colors.DARK_BLUE_10,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <MyText
                                            numberOfLines={1}
                                            text={`${element?.user?.username}`}
                                            style={{
                                                color: Colors.DARK_BLUE_40
                                            }}
                                        />
                                    </View>
                                );
                            })}
                        </GestureHandler.ScrollView>
                    )}
                </TouchableOpacity>
            </View>
        );
    };

    handleSearchUser = async (txt) => {
        const { supportServiceId, listMemberGroup } = this.props;
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 100,
                search: txt
            },
            requestId: '',
            supportServiceId: listMemberGroup.groupId
        };
        console.log(
            'handleSearchUserhandleSearchUser',
            listMemberGroup.groupId
        );
        await this.props.actionHome.getListMemberGroup(body);
    };

    actionSaveData = (data) => {
        const { modalTitle } = this.state;
        console.log('datadatadata', data);
        if (modalTitle === translate('receiver')) {
            this.setState((prevState) => ({
                showModal: false,
                dataFilter: {
                    ...prevState.dataFilter,
                    selectedMember: data
                },
                snapPoints: '85%'
            }));
        }
        if (modalTitle === translate('watcher')) {
            this.setState((prevState) => ({
                showModal: false,
                dataFilter: {
                    ...prevState.dataFilter,
                    selectedMemberFollower: data
                },
                snapPoints: '85%'
            }));
        }
        if (modalTitle === translate('ticket_creator')) {
            this.setState((prevState) => ({
                showModal: false,
                dataFilter: {
                    ...prevState.dataFilter,
                    selectedMemberCreated: data
                },
                snapPoints: '85%'
            }));
        }
    };

    render() {
        const {
            onPressDimiss,
            listStatusTicket,
            listMember,
            xworkData,
            listMemberGroup,
            detailGroupTicket
        } = this.props;
        const {
            selectedStatus,
            selectedMember,
            selectedMemberCreated,
            selectedMemberFollower,
            endDate,
            startDate
        } = this.state.dataFilter;
        const { modalAddReceiver, showModal, snapPoints } = this.state;
        console.log(
            'listMemberlistMember',
            startDate.length > 0 && endDate.length > 0 ? false : true
        );

        return (
            <xworkData.BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[snapPoints]}
                onChange={(index) => {
                    if (index === -1) {
                        onPressDimiss();
                    }
                }}
                backdropComponent={(props) => (
                    <xworkData.BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        {...props}
                        onPress={onPressDimiss}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                android_keyboardInputMode="adjustResize"
                enablePanDownToClose={true}
                style={styles.ctn}>
                <xworkData.GestureHandler.NativeViewGestureHandler
                    disallowInterruption={true}>
                    <View style={{ flex: 1 }}>
                        {this.renderHeader()}
                        <View
                            style={{
                                height: 1,
                                backgroundColor: Colors.GRAYF5,
                                width: '100%',
                                marginVertical: Mixins.scale(20)
                            }}
                        />
                        <xworkData.GestureHandler.ScrollView
                            contentContainerStyle={{
                                paddingBottom: Mixins.scale(20)
                            }}
                            showsVerticalScrollIndicator={false}
                            style={{
                                paddingHorizontal: Mixins.scale(12)
                            }}>
                            <MyText
                                text={translate('filter_keyword')}
                                numberOfLines={1}
                                addSize={2}
                                typeFont="medium"
                                style={{
                                    marginVertical: 4,
                                    color: Colors.BLACK
                                }}
                            />
                            <View style={styles.txtInput}>
                                <xworkData.BottomSheetGorhom.BottomSheetTextInput
                                    defaultValue={
                                        this.state.dataFilter.textValue
                                    }
                                    textAlign="left"
                                    onChangeText={(text) => {
                                        this.setState((prevState) => ({
                                            dataFilter: {
                                                ...prevState.dataFilter,
                                                textValue: text
                                            }
                                        }));
                                    }}
                                    onBlur={() => {}}
                                    style={{
                                        flex: 1,
                                        height: Mixins.scale(44)
                                    }}
                                    placeholder={translate('enter_keyword')}
                                    placeholderTextColor={Colors.GRAYF9}
                                    placeholderStyle={{ fontSize: 14 }}
                                />
                                {this.state.dataFilter?.textValue &&
                                    this.state.dataFilter?.textValue?.length >
                                        0 && (
                                        <TouchableOpacity
                                            hitSlop={{
                                                top: 5,
                                                right: 5,
                                                left: 5,
                                                bottom: 5
                                            }}
                                            style={{
                                                height: 20,
                                                width: 20
                                            }}
                                            onPress={() => {
                                                this.setState((prevState) => ({
                                                    dataFilter: {
                                                        ...prevState.dataFilter,
                                                        textValue: ''
                                                    }
                                                }));
                                            }}>
                                            <Image
                                                resizeMode="contain"
                                                source={{
                                                    uri: 'ic_error_ticket'
                                                }}
                                                style={{
                                                    height: Mixins.scale(20),
                                                    width: Mixins.scale(20)
                                                }}
                                            />
                                        </TouchableOpacity>
                                    )}
                            </View>

                            <RenderStatus
                                titleHeader={translate('job_status')}
                                data={listStatusTicket}
                                value={selectedStatus}
                                onChange={(item) => this.onChangeStatus(item)}
                                renderPlaceholder={() => {
                                    return (
                                        <RenderPlaceholderStatus
                                            selectedStatus={selectedStatus}
                                            listStatusTicket={listStatusTicket}
                                            xworkData={xworkData}
                                        />
                                    );
                                }}
                                xworkData={xworkData}
                            />
                            {this.renderSelect(
                                translate('receiver'),
                                selectedMember
                            )}
                            {this.renderSelect(
                                translate('watcher'),
                                selectedMemberFollower
                            )}
                            {this.renderSelect(
                                translate('ticket_creator'),
                                selectedMemberCreated
                            )}
                            {/* <RenderSelectUsers
                                titleHeader={translate('watcher')}
                                data={listMember}
                                dropdownPosition={'top'}
                                value={selectedMemberFollower}
                                onChange={(item) => {
                                    this.setState((prevState) => ({
                                        dataFilter: {
                                            ...prevState.dataFilter,
                                            selectedMemberFollower: item
                                        }
                                    }));
                                }}
                                renderPlaceholder={() => {
                                    return (
                                        <RenderPlaceholderMember
                                            placeholder={translate('watcher')}
                                            selectedMember={
                                                selectedMemberFollower
                                            }
                                        />
                                    );
                                }}
                                xworkData={xworkData}
                            /> */}
                            {/* <RenderSelectUsers
                                titleHeader={translate('ticket_creator')}
                                data={listMember}
                                dropdownPosition={'top'}
                                value={selectedMemberCreated}
                                onChange={(item) => {
                                    this.setState((prevState) => ({
                                        dataFilter: {
                                            ...prevState.dataFilter,
                                            selectedMemberCreated: item
                                        }
                                    }));
                                }}
                                renderPlaceholder={() => {
                                    return (
                                        <RenderPlaceholderMember
                                            selectedMember={
                                                selectedMemberCreated
                                            }
                                            placeholder={translate(
                                                'ticket_creator'
                                            )}
                                        />
                                    );
                                }}
                                xworkData={xworkData}
                            /> */}

                            <RenderStore
                                titleHeader={translate('choose_supermarket')}
                                onPress={() => {
                                    this.setState({ showModalStore: true });
                                }}
                                renderPlaceholder={() => {
                                    return (
                                        <RenderPlaceholderStore
                                            storeSelected={
                                                this.state.dataFilter
                                                    .storeSelected
                                            }
                                            listStatusTicket={
                                                this.props.loadMoreLocation
                                                    ?.data
                                            }
                                            xworkData={this.props.xworkData}
                                        />
                                    );
                                }}
                                xworkData={this.props.xworkData}
                            />

                            <RenderTime
                                startDate={startDate}
                                endDate={endDate}
                                onPressShowModal={(item) => {
                                    this.setState({
                                        chooseDate: item,
                                        isShowCalender: true
                                    });
                                }}
                            />
                            {this.state.showModalStore && (
                                <ModalListStore
                                    isVisible={this.state.showModalStore}
                                    titleModal={translate('choose_supermarket')}
                                    onPressDimiss={() => {
                                        this.setState({
                                            showModalStore: false,
                                            heightSheet: '90%'
                                        });
                                    }}
                                    handleLoadMoreUser={this.loadMoreLocation}
                                    handleSearchStore={this.loadMoreLocation}
                                    listStore={this.props.listLocationGeo.data}
                                    storeSelected={
                                        this.state.dataFilter.storeSelected
                                    }
                                    xworkData={this.props.xworkData}
                                    handleAddStore={this.handleAddStore}
                                />
                            )}
                            <xworkData.component.ModalCalendarNew
                                isVisible={this.state.isShowCalender}
                                endDate={this.state.dataFilter.endDate}
                                chooseDate={this.state.chooseDate}
                                startDate={this.state.dataFilter.startDate}
                                onPressDimiss={this.handleDimissModalCalendar}
                                onChangeSelectedDate={this.onChangeSelectedDate}
                            />
                        </xworkData.GestureHandler.ScrollView>
                        {this.renderBottomButton()}
                        {showModal && (
                            <ModalAddFollowers
                                xworkData={this.props.xworkData}
                                isVisible={showModal}
                                titleModal={this.state.modalTitle}
                                onPressDimiss={() => {
                                    this.setState({
                                        showModal: false,
                                        snapPoints: '85%'
                                    });
                                }}
                                handleSearchUser={this.handleSearchUser}
                                onPressSave={this.actionSaveData}
                                groupUserFollowers={this.state.groupUser}
                                listUser={listMember}
                                allUser={false}
                                // handleLoadMoreUser={this.handleLoadMoreUser}
                            />
                        )}
                    </View>
                </xworkData.GestureHandler.NativeViewGestureHandler>
            </xworkData.BottomSheetGorhom.BottomSheetModal>
        );
    }
}
const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listTicket: state.ticketReducer.listTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket
    };
};
const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ModalFilterTicket);
const styles = StyleSheet.create({
    btnBottom: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginHorizontal: Mixins.scale(12),
        marginTop: Mixins.scale(8),
        paddingBottom: Mixins.scale(24)
    },
    btnDelete: {
        alignItems: 'center',
        borderColor: Colors.GRAYF8,
        borderRadius: 12,
        borderWidth: 0.5,
        flex: 1,
        height: Mixins.scale(56),
        justifyContent: 'center',
        marginRight: Mixins.scale(6)
    },

    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },

    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    },
    ctn: {
        flex: 1
    },

    txtInput: {
        alignItems: 'center',
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        flexDirection: 'row',
        height: Mixins.scale(44),
        marginBottom: 16,
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12)
    },
    viewButtonDropDown: {
        backgroundColor: Colors.WHITE,
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        height: Mixins.scale(44),
        justifyContent: 'center',
        marginVertical: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        width: '100%'
    }
});
