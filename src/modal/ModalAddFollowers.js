import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    Dimensions
} from 'react-native';
import { ThemeXwork } from '@mwg-sdk/styles';
import { CONST_API } from '../constant';
const { translate } = global.props.getTranslateConfig();

class ModalAddFollowers extends Component {
    constructor(props) {
        super(props);
        this.state = {
            txtSearch: '',
            groupUserFollowers: this.props.groupUserFollowers || [],
            listUser: this.props.listUser,
            keyBoardShow: false,
            heightKeyboard: 0,
            heightSheet: '70%',
            allUser: false
        };
        this.isDone = false;
        this.bottomSheetModalRef = React.createRef(null);
        this.timeOut = -1;
    }

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
        this.keyboardDidShowListener = Keyboard.addListener(
            'keyboardDidShow',
            this.keyboardDidShow
        );
        this.keyboardDidHideListener = Keyboard.addListener(
            'keyboardDidHide',
            this.keyboardDidHide
        );
        this.setState({
            listUser: this.props.listUser,
            txtSearch: '',
            groupUserFollowers: this.props.groupUserFollowers || []
        });
    }
    async componentDidUpdate(prevProps, prevState) {
        if (this.props.listUser !== prevProps.listUser) {
            this.setState({
                listUser: this.props.listUser
            });
        }
        if (this.props.groupUserFollowers !== prevProps.groupUserFollowers) {
            this.setState({
                groupUserFollowers: this.props.groupUserFollowers || []
            });
        }
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
        if (
            this.state.groupUserFollowers !== prevState.groupUserFollowers &&
            this.state.groupUserFollowers &&
            this.state.groupUserFollowers.length > 0
        ) {
            if (this.state.keyBoardShow) {
                this.setState({ heightSheet: '90%' });
            } else {
                this.setState({ heightSheet: '70%' });
            }
        } else if (
            this.state.groupUserFollowers !== prevState.groupUserFollowers &&
            this.state.groupUserFollowers &&
            this.state.groupUserFollowers.length === 0
        ) {
            this.setState({ heightSheet: '70%' });
        }
    }
    keyboardDidHide = () => {
        if (
            this.state.groupUserFollowers &&
            this.state.groupUserFollowers.length > 0
        ) {
            this.setState({ heightSheet: '70%' });
        } else {
            this.setState({ heightSheet: '70%' });
        }
        this.setState({ keyBoardShow: false });
    };
    keyboardDidShow = () => {
        if (
            this.state.groupUserFollowers &&
            this.state.groupUserFollowers.length > 0
        ) {
            this.setState({ heightSheet: '90%' });
        } else {
            this.setState({ heightSheet: '80%' });
        }
        this.setState({
            keyBoardShow: true
        });
    };

    componentWillUnmount() {
        this.keyboardDidShowListener?.remove();
        this.keyboardDidHideListener?.remove();
    }

    renderSearch = () => {
        const { xworkData } = this.props;
        return (
            <View style={styles.input}>
                <AntDesign
                    size={20}
                    name="search1"
                    color={ThemeXwork.neutrals.$200}
                    style={{ marginHorizontal: Mixins.scale(5) }}
                />
                <xworkData.BottomSheetGorhom.BottomSheetTextInput
                    defaultValue={this.state.txtSearch}
                    onChangeText={this.searchUser}
                    style={{ flex: 1 }}
                    placeholder="Tìm kiếm"
                    keyboardType="default"
                    placeholderTextColor={ThemeXwork.neutrals.$700}
                    onBlur={() => {
                        if (!this.isDone) {
                            this.bottomSheetModalRef.current?.snapToIndex(0);
                        }
                    }}
                />
                {this.state.txtSearch && this.state.txtSearch.length > 0 && (
                    <TouchableOpacity
                        hitSlop={{
                            top: 5,
                            right: 5,
                            left: 5,
                            bottom: 5
                        }}
                        style={{ height: 20, width: 20 }}
                        onPress={() => {
                            this.clearText();
                        }}>
                        <Image
                            resizeMode="contain"
                            source={{ uri: 'ic_error_ticket' }}
                            style={{
                                height: Mixins.scale(20),
                                width: Mixins.scale(20)
                            }}
                        />
                    </TouchableOpacity>
                )}
            </View>
        );
    };
    clearText = (text = '') => {
        const { handleSearchUser } = this.props;
        this.setState({ txtSearch: text });
        handleSearchUser(text);
    };

    searchUser = (text) => {
        const { handleSearchUser } = this.props;
        this.setState(
            {
                txtSearch: text
            },
            async () => {
                await handleSearchUser(text, this.props.allUser);
                this.setState({ listUser: this.props.listUser });
            }
        );
    };
    handleLoadMore = async () => {
        const { handleLoadMoreUser } = this.props;
        await handleLoadMoreUser(this.state.txtSearch, this.props.allUser);
        this.setState({ listUser: this.props.listUser });
    };
    clearState = () => {
        this.setState({
            txtSearch: '',
            groupUserFollowers: this.props.groupUserFollowers
        });
        this.props.handleSearchUser('');
    };
    onPressRemoveMember = (item) => {
        const findItem = this.state.groupUserFollowers.filter((ele) => {
            if (helper.IsValidateObject(item.user)) {
                if (helper.IsValidateObject(ele.user)) {
                    return item.user.id !== ele.user.id;
                } else {
                    return item.user.id !== ele.userId;
                }
            } else {
                if (helper.IsValidateObject(ele.user)) {
                    return item.userId !== ele.user.id;
                } else {
                    return item.userId !== ele.userId;
                }
            }
        });
        this.setState({
            groupUserFollowers: findItem
        });
    };
    renderUsersSelected = () => {
        const { groupUserFollowers } = this.state;
        const { xworkData } = this.props;
        if (helper.IsEmptyArray(groupUserFollowers)) {
            return null;
        }
        return (
            <View style={{ height: 80 }}>
                <xworkData.GestureHandler.ScrollView
                    keyboardShouldPersistTaps="handled"
                    horizontal
                    showsHorizontalScrollIndicator={false}>
                    {groupUserFollowers?.map((element, index) => {
                        return (
                            <View
                                key={index?.toString()}
                                onPress={() => {}}
                                style={{
                                    width: Mixins.scale(100),
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <View>
                                    <Image
                                        style={{
                                            height: Mixins.scale(44),
                                            width: Mixins.scale(44),
                                            borderRadius: Mixins.scale(22)
                                        }}
                                        source={{
                                            uri: helper.IsValidateObject(
                                                element.user
                                            )
                                                ? `${CONST_API.baseAvatarURI}${element?.user?.profile?.image}`
                                                : `${CONST_API.baseAvatarURI}${element?.userImage}`
                                        }}
                                    />
                                    <TouchableOpacity
                                        hitSlop={{
                                            top: 10,
                                            bottom: 10,
                                            right: 10,
                                            left: 10
                                        }}
                                        onPress={() => {
                                            this.onPressRemoveMember(
                                                element,
                                                index
                                            );
                                        }}
                                        style={{
                                            position: 'absolute',
                                            bottom: -5,
                                            right: -5,
                                            height: Mixins.scale(20),
                                            width: Mixins.scale(20),
                                            borderRadius: Mixins.scale(10),
                                            backgroundColor: Colors.WHITE
                                        }}>
                                        <Image
                                            style={{
                                                height: Mixins.scale(20),
                                                width: Mixins.scale(20)
                                            }}
                                            source={{ uri: 'ic_remove' }}
                                        />
                                    </TouchableOpacity>
                                </View>

                                <MyText
                                    numberOfLines={1}
                                    text={
                                        helper.IsValidateObject(element.user)
                                            ? `${element?.user?.username} - ${element?.user?.profile?.lastName} ${element?.user?.profile?.firstName}`
                                            : `${element?.userName} - ${element?.userLastName} ${element?.userFirstName}`
                                    }
                                    style={{
                                        color: Colors.BLACK,
                                        marginTop: Mixins.scale(8)
                                    }}
                                />
                            </View>
                        );
                    })}
                </xworkData.GestureHandler.ScrollView>
            </View>
        );
    };
    handlePushUser = (item) => {
        const { groupUserFollowers } = this.state;
        let newDataUser = [...groupUserFollowers, item];
        this.setState({
            groupUserFollowers: newDataUser
        });
    };
    renderListUserSearch = () => {
        const { listUser } = this.state;
        const { xworkData } = this.props;
        return (
            <xworkData.GestureHandler.FlatList
                contentContainerStyle={{
                    paddingBottom: 70
                }}
                style={{
                    height: Mixins.scale(350)
                }}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
                data={listUser}
                renderItem={({ item, index }) => {
                    const exist = this.state.groupUserFollowers?.findIndex(
                        (ele) => {
                            if (helper.IsValidateObject(ele.user)) {
                                return ele?.user?.id === item?.user?.id;
                            } else {
                                return ele?.userId === item?.user?.id;
                            }
                        }
                    );
                    let checkItem = exist !== -1;
                    return (
                        <TouchableOpacity
                            // disabled={checkItem}
                            activeOpacity={checkItem ? 0.5 : 1}
                            onPress={() => {
                                if (!checkItem) {
                                    this.handlePushUser(item);
                                }
                            }}
                            key={`${new Date().getTime() + index}`}
                            style={[
                                styles.viewTouch,
                                {
                                    opacity: checkItem ? 0.5 : 1
                                }
                            ]}>
                            {checkItem ? (
                                <Image
                                    style={{
                                        height: Mixins.scale(24),
                                        width: Mixins.scale(24),
                                        marginRight: Mixins.scale(8)
                                    }}
                                    source={{ uri: 'ic_check_user' }}
                                />
                            ) : (
                                <Image
                                    style={{
                                        height: Mixins.scale(24),
                                        width: Mixins.scale(24),
                                        marginRight: Mixins.scale(8),
                                        tintColor: ThemeXwork.neutrals.$800
                                    }}
                                    resizeMode="contain"
                                    source={{ uri: 'ic_check' }}
                                />
                            )}
                            <Image
                                style={{
                                    height: Mixins.scale(28),
                                    width: Mixins.scale(28),
                                    borderRadius: Mixins.scale(14),
                                    marginRight: Mixins.scale(8)
                                }}
                                source={{
                                    uri: `${CONST_API.baseAvatarURI}${item?.user?.profile?.image}`
                                }}
                            />
                            <MyText
                                numberOfLines={1}
                                text={`${item?.user?.username} - ${item?.user?.profile?.lastName} ${item?.user?.profile?.firstName}`}
                                addSize={1}
                                style={{ color: Colors.BLACK }}
                            />
                        </TouchableOpacity>
                    );
                }}
                onEndReached={this.handleLoadMore}
                scrollEventThrottle={10}
                onEndReachedThreshold={0.1}
                bounces
            />
        );
    };

    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
        this.isDone = false;
    };
    render() {
        const {
            onPressDimiss,
            titleModal,
            onPressSave,
            xworkData,
            handleSearchUser
        } = this.props;
        const deviceHeight = Dimensions.get('window').height;

        const { BottomSheetGorhom, GestureHandler } = xworkData;
        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        this.clearState();
                        onPressDimiss();
                    }
                }}
                keyboardBlurBehavior="restore"
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        onPress={() => {
                            this.clearState();
                            onPressDimiss();
                        }}
                        {...props}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                android_keyboardInputMode="adjustResize"
                enablePanDownToClose={true}
                style={{}}>
                <GestureHandler.NativeViewGestureHandler
                    disallowInterruption={true}>
                    <View style={{ flex: 1, paddingBottom: 20 }}>
                        <TouchableOpacity
                            style={{
                                paddingHorizontal: 16,
                                marginTop: 12,
                                flex: 1
                            }}
                            hitSlop={{
                                bottom:
                                    global.props.insets.bottom +
                                    deviceHeight * 0.5
                            }}
                            activeOpacity={1}
                            onPress={() => {
                                Keyboard.dismiss();
                                this.bottomSheetModalRef.current?.snapToIndex(
                                    0
                                );
                            }}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    height: Mixins.scale(40)
                                }}>
                                <TouchableOpacity
                                    hitSlop={{
                                        top: 5,
                                        left: 5,
                                        bottom: 5,
                                        right: 5
                                    }}
                                    onPress={() => {
                                        this.isDone = true;
                                        this.clearState();
                                        onPressDimiss();
                                        this.bottomSheetModalRef.current?.close();
                                    }}>
                                    <AntDesign
                                        name="close"
                                        size={20}
                                        style={{
                                            marginRight: Mixins.scale(16)
                                        }}
                                    />
                                </TouchableOpacity>
                                <MyText
                                    text={titleModal}
                                    numberOfLines={1}
                                    addSize={2}
                                    style={{
                                        flex: 1,
                                        color: ThemeXwork.neutrals.$200
                                    }}
                                />
                                {
                                    <TouchableOpacity
                                        onPress={() => {
                                            this.isDone = true;
                                            this.clearState();
                                            onPressDimiss();
                                            onPressSave(
                                                this.state.groupUserFollowers
                                            );
                                            this.bottomSheetModalRef.current?.close();
                                        }}
                                        style={{
                                            height: Mixins.scale(32),
                                            width: Mixins.scale(60),
                                            borderRadius: Mixins.scale(12),
                                            backgroundColor:
                                                ThemeXwork.primary.$500,
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}>
                                        <MyText
                                            text={translate('done')}
                                            style={{ color: Colors.WHITE }}
                                            numberOfLines={1}
                                            addSize={1}
                                        />
                                    </TouchableOpacity>
                                }
                            </View>

                            <View
                                style={{
                                    height: 1,
                                    backgroundColor: ThemeXwork.neutrals.$950,
                                    width: '100%',
                                    marginVertical: Mixins.scale(12)
                                }}
                            />
                            {this.renderUsersSelected()}
                            {this.renderSearch()}
                            {this.props.allUser && (
                                <TouchableOpacity
                                    onPress={() => {
                                        handleSearchUser(
                                            '',
                                            !this.props.allUser
                                        );
                                    }}
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        marginTop: 12,
                                        height: Mixins.scale(40)
                                    }}>
                                    <View
                                        style={{
                                            height: 20,
                                            width: 20,
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            borderWidth: 1.5,
                                            borderColor: this.props.allUser
                                                ? Colors.BLUE_EB
                                                : Colors.GRAY_BODERRADIUS,
                                            borderRadius: 4,
                                            backgroundColor: this.props.allUser
                                                ? Colors.BLUE_EB
                                                : Colors.WHITE,
                                            marginRight: 8
                                        }}>
                                        {this.props.allUser && (
                                            <Image
                                                source={{
                                                    uri: 'ic_check_white'
                                                }}
                                                style={{
                                                    height: 16,
                                                    width: 13,
                                                    tintColor: Colors.WHITE
                                                }}
                                                resizeMode="contain"
                                            />
                                        )}
                                    </View>
                                    <MyText
                                        text={translate('alluser')}
                                        numberOfLines={1}
                                        addSize={2}
                                        style={{
                                            flex: 1,
                                            color: ThemeXwork.neutrals.$200
                                        }}
                                    />
                                </TouchableOpacity>
                            )}
                            {this.renderListUserSearch()}
                        </TouchableOpacity>
                    </View>
                </GestureHandler.NativeViewGestureHandler>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}

export default ModalAddFollowers;
const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: ThemeXwork.neutrals.$200,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingBottom: Mixins.scale(4),
        paddingTop: Mixins.scale(12)
    },
    container: {
        flex: 1
    },
    input: {
        alignItems: 'center',
        backgroundColor: ThemeXwork.neutrals.$950,
        borderRadius: Mixins.scale(12),
        flexDirection: 'row',
        height: Mixins.scale(56),
        paddingRight: 16
    },

    viewTouch: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(40),
        marginTop: Mixins.scale(12)
    }
});
