import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    BackHandler,
    Platform,
    Dimensions,
    Image,
    TextInput
} from 'react-native';

import { Mixins, XworkColor } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { formatVND } from '../../constant/validate';
import BaseBottomSheet from '../../components/BaseBottomSheet';
const { translate } = global.props.getTranslateConfig();

class ModalPayment extends Component {
    constructor(props) {
        super(props);
        this.state = {
            totalamount: '',
            msgError: ''
        };
    }

    // componentDidMount() {
    //     if (this.props.isVisible) {
    //         this.openModal();
    //     }
    //     this.setState({
    //         totalamount: ''
    //     });
    //     BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
    // }

    componentWillUnmount() {
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.handleBackPress
        );
    }

    handleBackPress = () => {
        if (this.props.isVisible && Platform.OS === 'android') {
            this.props?.onPressDimiss();
            return true;
        }
    };

    onPressCloseModal = () => {
        this.props.onPressDimiss();
    };

    handleApprove = async () => {
        const { totalAmountTicket, onPressDimiss } = this.props;
        let msg = '';
        if (!totalAmountTicket) {
            msg = translate('modal_amount_paid');
        }

        if (this.state.totalamount?.length === 0) {
            msg = translate('enter_amount');
        }
        if (msg.length > 0) {
            return this.setState(
                {
                    msgError: msg
                },
                () => {
                    setTimeout(() => {
                        this.setState({
                            msgError: ''
                        });
                    }, 2000);
                }
            );
        }
        const moneyTicket = totalAmountTicket.replace(/[,.]/g, '');
        const stringMoney = this.state.totalamount.replace(/[,.]/g, '');
        if (parseInt(moneyTicket) === parseInt(stringMoney)) {
            await onPressDimiss();
            global.props.showLoader();
            return setTimeout(() => {
                this.props.onPressConfirm();
                global.props.hideLoader();
            }, 1000);
        } else {
            return this.setState(
                {
                    msgError: translate('check_total_amount')
                },
                () => {
                    setTimeout(() => {
                        this.setState({
                            msgError: false
                        });
                    }, 2000);
                }
            );
        }
    };

    renderContent = () => {
        const { totalamount } = this.state;
        const { content } = this.props;
        return (
            <View>
                <View style={{ marginTop: Mixins.scale(24) }}>
                    <MyText
                        text={content}
                        addSize={2}
                        style={{ color: XworkColor.NEUTRALS_1 }}
                    />
                    <View
                        style={[
                            styles.styleRow,
                            { marginTop: Mixins.scale(24) }
                        ]}>
                        <View
                            style={{
                                marginRight: Mixins.scale(8)
                            }}>
                            <MyText
                                text={`${translate('confirm_amount')}:`}
                                addSize={2}
                                style={{ color: XworkColor.NEUTRALS_3 }}
                            />
                        </View>
                        <View
                            style={[
                                styles.styleRow,
                                {
                                    borderRadius: 12,
                                    borderWidth: 1,
                                    borderColor: '#CBD2D9',
                                    height: Mixins.scale(56),
                                    flex: 1
                                }
                            ]}>
                            <TextInput
                                value={formatVND(totalamount)}
                                textAlign={'right'}
                                style={{
                                    width: '90%',
                                    fontSize: 17,
                                    fontWeight: 'bold'
                                }}
                                placeholder="0"
                                placeholderTextColor={XworkColor.NEUTRALS_3}
                                keyboardType="numeric"
                                onChangeText={(text) => {
                                    this.setState({
                                        totalamount: text
                                    });
                                }}
                            />
                            <MyText
                                text={` đ`}
                                typeFont={'bold'}
                                style={{
                                    color: XworkColor.NEUTRALS_3,
                                    textDecorationLine: 'underline'
                                }}
                            />
                        </View>
                    </View>
                </View>
                <View style={{ marginTop: 16 }}>
                    {this.state.msgError?.length > 0 && (
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center'
                            }}>
                            {/* warning */}
                            <Image
                                source={{ uri: 'warning' }}
                                resizeMode="contain"
                                style={{
                                    height: Mixins.scale(14),
                                    width: Mixins.scale(14),
                                    marginRight: Mixins.scale(8)
                                }}
                            />
                            <MyText
                                text={this.state.msgError}
                                addSize={0}
                                style={{ color: XworkColor.RED }}
                            />
                        </View>
                    )}
                </View>
            </View>
        );
    };

    renderButton = () => {
        return (
            <View
                style={{
                    height: Mixins.scale(56),
                    marginTop: Mixins.scale(16),
                    flexDirection: 'row',
                    justifyContent: 'space-between'
                }}>
                <TouchableOpacity
                    onPress={() => {
                        this.onPressCloseModal();
                    }}
                    style={[
                        styles.buttonGetTicket,
                        {
                            borderColor: XworkColor.DARK_BLUE_60,
                            borderWidth: 0.5,
                            marginRight: Mixins.scale(16),
                            backgroundColor: XworkColor.WHITE
                        }
                    ]}>
                    <MyText
                        text={translate('back')}
                        addSize={2}
                        style={{ color: XworkColor.DARK_BLUE_60 }}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={styles.buttonGetTicket}
                    onPress={() => {
                        this.handleApprove();
                    }}>
                    <MyText
                        text={translate('confirm')}
                        addSize={2}
                        style={{ color: XworkColor.WHITE }}
                        typeFont="semiBold"
                    />
                </TouchableOpacity>
            </View>
        );
    };

    render() {
        return (
            <BaseBottomSheet
                isModalVisible={this.props.isVisible}
                toggleModal={this.props.onPressDimiss}
                isShowHeader
                nameTitle={this.props.titleHeader}
                maxHeight={true}>
                <View
                    style={{
                        marginBottom: Mixins.scale(16),
                        paddingHorizontal: 16
                    }}>
                    <TouchableOpacity
                        activeOpacity={1}
                        onPress={() => {
                            Keyboard.dismiss();
                        }}>
                        {this.renderContent()}
                        {this.renderButton()}
                    </TouchableOpacity>
                </View>
            </BaseBottomSheet>
        );
    }
}

export default ModalPayment;

const styles = StyleSheet.create({
    buttonGetTicket: {
        alignItems: 'center',
        backgroundColor: XworkColor.DARK_BLUE_60,
        borderColor: XworkColor.DARK_BLUE_60,
        borderRadius: 20,
        borderWidth: 0.5,
        flex: 1,
        justifyContent: 'center',
        paddingVertical: Mixins.scale(16)

        // width: '60%'
    },

    closeLine: {
        backgroundColor: XworkColor.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    },
    container: { flex: 1 },

    styleRow: {
        alignItems: 'center',
        flexDirection: 'row'
    }
});
