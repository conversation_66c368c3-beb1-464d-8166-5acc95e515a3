// @ts-ignore
import { Mixins, XworkColor } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';

interface IBottomSheetHeaderProps {
    onClosePress?: () => void;
    titleModal?: string;
    renderRightComponent?: () => React.ReactNode;
}
const BottomSheetHeader = React.memo((props: IBottomSheetHeaderProps) => {
    const {
        onClosePress,
        titleModal = '',
        renderRightComponent = null
    } = props;

    return (
        <React.Fragment>
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    height: Mixins.scale(40),
                    marginBottom: Mixins.scale(24)
                }}>
                <TouchableOpacity
                    hitSlop={{
                        top: 10,
                        left: 10,
                        bottom: 10,
                        right: 10
                    }}
                    activeOpacity={1}
                    onPress={onClosePress}>
                    <AntDesign
                        name="close"
                        size={20}
                        style={{
                            marginRight: Mixins.scale(16)
                        }}
                    />
                </TouchableOpacity>
                <MyText
                    text={titleModal}
                    numberOfLines={1}
                    addSize={2}
                    style={{
                        flex: 1,
                        color: XworkColor.BLACK_HEADER_TITLE
                    }}
                />
                <React.Fragment>
                    {renderRightComponent && renderRightComponent()}
                </React.Fragment>
            </View>
        </React.Fragment>
    );
});

export default BottomSheetHeader;
