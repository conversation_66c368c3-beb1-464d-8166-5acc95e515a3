// @ts-ignore
import { XworkColor } from '@mwg-sdk/styles';
import React from 'react';
import { StyleSheet, View } from 'react-native';

const CustomHandler = React.memo((props: any) => {
    return (
        <View style={styles.closeLineContainer}>
            <View style={styles.closeLine} />
        </View>
    );
});
const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: XworkColor.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    }
});

export default CustomHandler;
