// @ts-ignore
import React from 'react';
import CustomHandler from './CustomHandler';
import { useSelector } from 'react-redux';

interface IBottomSheetWrapperProps {
    children?: any;
    onChange?: (index: number) => void;
    onPressBackdrop?: () => void;
    [key: string]: any;
}
const BottomSheetWrapper = React.memo(
    React.forwardRef((props: IBottomSheetWrapperProps, ref: any) => {
        const xworkData = useSelector(
            (state: any) => state.groupTicketReducer.xworkData
        );
        const { BottomSheetGorhom, GestureHandler } = xworkData;
        const {
            children = null,
            onChange,
            onPressBackdrop,
            snap,
            ...rest
        } = props;
        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={ref}
                index={0}
                // keyboardBehavior="fillParent"
                snapPoints={snap ?? ['60%']}
                onChange={onChange}
                backdropComponent={(btsprops: any) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        {...btsprops}
                        onPress={onPressBackdrop}
                    />
                )}
                android_keyboardInputMode="adjustResize"
                handleComponent={() => <CustomHandler />}
                enablePanDownToClose={true}
                style={{ flex: 1 }}
                {...rest}>
                {children && children}
            </BottomSheetGorhom.BottomSheetModal>
        );
    })
);

export default BottomSheetWrapper;
