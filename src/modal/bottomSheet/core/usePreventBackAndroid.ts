import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';
import { BackHandler } from 'react-native';

const usePreventBackAndroid = (
    isVisible: boolean,
    actionPreventback: () => void
) => {
    useFocusEffect(
        useCallback(() => {
            const onBackPress = () => {
                if (isVisible) {
                    if (typeof actionPreventback === 'function') {
                        actionPreventback();
                    }
                    return true;
                }
            };
            BackHandler.addEventListener('hardwareBackPress', onBackPress);
            return () =>
                BackHandler.removeEventListener(
                    'hardwareBackPress',
                    onBackPress
                );
        }, [isVisible])
    );
};

export default usePreventBackAndroid;
