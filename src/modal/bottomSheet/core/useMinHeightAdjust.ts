import { useEffect, useState } from 'react';
import { Keyboard } from 'react-native';

const useMinHeightAdjust = (
    height: number
): { minHeightAdjust: string | number } => {
    const [minHeightAdjust, setMinHeightAdjust] = useState<string | number>(
        'auto'
    );

    useEffect(() => {
        const keyboardDidShowListener = Keyboard.addListener(
            'keyboardDidShow',
            () => {
                setMinHeightAdjust(height);
            }
        );

        const keyboardDidHideListener = Keyboard.addListener(
            'keyboardDidHide',
            () => {
                setMinHeightAdjust('auto');
            }
        );

        return () => {
            keyboardDidShowListener.remove();
            keyboardDidHideListener.remove();
        };
    }, []);

    return { minHeightAdjust };
};

export default useMinHeightAdjust;
