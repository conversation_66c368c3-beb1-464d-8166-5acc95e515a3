import { Mixins, XworkColor } from '@mwg-sdk/styles';
import { FastImage, MyText } from '@mwg-kits/components';
import React, {
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    ScrollView,
    Image
} from 'react-native';
import BottomSheetHeader from './core/BottomSheetHeader';
import BottomSheetWrapper from './core/BottomSheetWrapper';
import { connect, useSelector } from 'react-redux';
const { translate } = (global as any).props.getTranslateConfig();
import { formatVND } from '../../constant/validate';
import { convertUTCDateToLocalDate } from '../../Tickets/Ticket/EditTicket';
import moment from 'moment';
import { constants } from '../../constant';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import { helper } from '@mwg-kits/common';
import ImagePicker from 'react-native-image-crop-picker';

interface DataContextProps<T> {
    setTextContentCT: React.Dispatch<React.SetStateAction<string>>;
    textContentCT: string;
}
const defaultSetTextContentCT: React.Dispatch<
    React.SetStateAction<string>
> = () => {};

const DataContext = React.createContext<DataContextProps<any>>({
    setTextContentCT: defaultSetTextContentCT,
    textContentCT: ''
});
export interface ISafeContentProps {
    setTextContentCT: any;
    textContentCT: any;
    BottomSheetGorhom: any;
    onPressConfirm: (data: object) => void;
}
const SafeContent = React.memo((props: ISafeContentProps) => {
    const { setTextContentCT, BottomSheetGorhom } = props;

    const [text, setText] = useState('');
    return (
        <React.Fragment>
            <BottomSheetGorhom.BottomSheetTextInput
                placeholder={`${translate('enter_reason')}`}
                multiline={true}
                style={{
                    textAlignVertical: 'top',
                    height: Mixins.scale(100),
                    borderColor: '#CBD2D9',
                    borderWidth: 1,
                    paddingHorizontal: 16,
                    paddingTop: 16,
                    borderRadius: 12,
                    marginTop: Mixins.scale(16),
                    fontSize: 15
                }}
                autoFocus
                placeholderTextColor={XworkColor.K_616E7C}
                value={text}
                underlineColorAndroid="transparent"
                onChangeText={(text) => {
                    setText(text);
                    setTextContentCT(text);
                }}
            />
        </React.Fragment>
    );
});
interface IModalSearchProps {
    isVisible: boolean;
    assessSelected: any;
    onPressDimiss: () => void;
    onPressConfirm: (data: object) => void;
}
const ModalInside = React.memo((props: IModalSearchProps, ref: any) => {
    const xworkData = useSelector(
        (state: any) => state.groupTicketReducer.xworkData
    );
    const [chooseVAT, setChooseVAT] = useState(null);
    const { BottomSheetGorhom } = xworkData;

    const { isVisible, onPressDimiss, onPressConfirm } = props;
    const { setTextContentCT, textContentCT } =
        useContext<DataContextProps<any>>(DataContext);

    const bottomSheetModalRef = useRef(null);

    const [listImage, setListImage] = useState([]);

    const [messageError, setMessageError] = useState('');
    const [heightSheet, setHeightSheet] = useState(Mixins.scale(550));
    const [price, setPrice] = useState('');

    const presentBTSByRef = useCallback(() => {
        bottomSheetModalRef?.current?.present();
    }, []);
    const [isShowCalender, setIsShowCalender] = useState(false);
    const [currentDate, setCurrentDate] = useState(null);

    const closeBTSByRef = useCallback(() => {
        bottomSheetModalRef.current?.close();
    }, []);

    const handleSnapToIndex = useCallback((snapIndex: number = 0) => {
        bottomSheetModalRef.current?.snapToIndex(snapIndex);
    }, []);

    useEffect(() => {
        if (isVisible) {
            presentBTSByRef();
        }
    }, [isVisible]);
    const selectedStartDate = convertUTCDateToLocalDate(new Date(currentDate));
    const RenderInputPrice = () => {
        return (
            <View
                style={[styles.styleRow, { marginVertical: Mixins.scale(16) }]}>
                <View style={{ width: '35%', marginRight: Mixins.scale(8) }}>
                    <MyText
                        text={`${translate('recommend_price')} `}
                        style={{
                            color: XworkColor.BLACK
                        }}></MyText>
                </View>
                <View
                    style={[
                        styles.styleRow,
                        {
                            borderRadius: 12,
                            borderWidth: 1,
                            borderColor: '#CBD2D9',
                            height: Mixins.scale(48),
                            paddingHorizontal: Mixins.scale(8),
                            flex: 1
                        }
                    ]}>
                    <BottomSheetGorhom.BottomSheetTextInput
                        value={formatVND(price)}
                        textAlign={'right'}
                        style={{
                            fontSize: 15,
                            flex: 1
                        }}
                        placeholder=""
                        placeholderTextColor={XworkColor.NEUTRALS_3}
                        keyboardType="numeric"
                        onChangeText={(text) => {
                            setPrice(text);
                        }}
                    />
                    {price.length > 0 && (
                        <MyText
                            text={` đ`}
                            style={{
                                color: XworkColor.NEUTRALS_3,
                                textDecorationLine: 'underline'
                            }}
                        />
                    )}
                </View>
            </View>
        );
    };
    const onShowPicker = async () => {
        try {
            await ImagePicker.openPicker({
                mediaType: 'photo',
                maxFiles: 5,
                multiple: true,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                maximumVideoDuration: 60000,
                compressVideoPreset: 'HighestQuality'
            }).then(async (image) => {
                setListImage([...image, ...listImage]);
            });
        } catch (e) {
            console.log(e, '12312312321');
        }
    };
    const RenderListImage = () => {
        return (
            <View style={[styles.styleRow]}>
                <View
                    style={{
                        flexDirection: 'row',
                        flex: 1,
                        justifyContent: 'space-between'
                    }}>
                    <TouchableOpacity
                        onPress={() => onShowPicker()}
                        style={[styles.touchIcon, { marginRight: 16 }]}>
                        <Image
                            style={styles.imgCamera}
                            source={{ uri: 'ic_image' }}
                        />
                    </TouchableOpacity>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        style={{}}>
                        {listImage &&
                            listImage.map((item, index) => {
                                return (
                                    <TouchableOpacity
                                        onPress={() => {}}
                                        key={`${new Date().getTime() + index}`}
                                        style={styles.vImage}>
                                        <FastImage
                                            source={{
                                                uri: item.path
                                            }}
                                            resizeMode="cover"
                                            style={styles.image}
                                        />

                                        <TouchableOpacity
                                            hitSlop={{
                                                top: 5,
                                                left: 5,
                                                bottom: 5,
                                                right: 5
                                            }}
                                            style={{
                                                position: 'absolute',
                                                right: Mixins.scale(5),
                                                top: Mixins.scale(0)
                                            }}
                                            onPress={() => {
                                                onRemoveImages(item);
                                            }}>
                                            <FontAwesome5
                                                solid
                                                color={'red'}
                                                name="times"
                                                size={16}
                                            />
                                        </TouchableOpacity>
                                    </TouchableOpacity>
                                );
                            })}
                    </ScrollView>
                </View>
            </View>
        );
    };
    const RenderRadioBox = () => {
        return (
            <View style={[styles.styleRow, { marginTop: Mixins.scale(16) }]}>
                <View style={{ width: '35%', marginRight: Mixins.scale(8) }}>
                    <MyText
                        text={`${translate('recommended_price_type')} `}
                        style={{
                            color: XworkColor.BLACK
                        }}></MyText>
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        flex: 1,
                        justifyContent: 'space-between'
                    }}>
                    {constants.listVAT.map((item, index) => {
                        let checkItem = item.id === chooseVAT?.id;
                        return (
                            <TouchableOpacity
                                onPress={() => setChooseVAT(item)}
                                style={{
                                    flexDirection: 'row'
                                }}>
                                <View
                                    style={[
                                        styles.radio,
                                        {
                                            borderColor: checkItem
                                                ? XworkColor.BLUE_PRIMARY
                                                : XworkColor.GRAY_PLACEHODER
                                        }
                                    ]}>
                                    {checkItem && (
                                        <View style={styles.radioAct} />
                                    )}
                                </View>
                                <MyText
                                    text={item.name}
                                    addSize={1}
                                    style={{
                                        color: XworkColor.BLACK
                                    }}></MyText>
                            </TouchableOpacity>
                        );
                    })}
                </View>
            </View>
        );
    };

    useEffect(() => {
        if (messageError && messageError.length > 0) {
            setTimeout(() => {
                setMessageError('');
            }, 1500);
        }
    }, [messageError]);
    const disabledButton = () => {
        if (textContentCT?.length === 0) {
            return true;
        }
        if (textContentCT?.length > 0) {
            if (price?.length === 0 && currentDate) {
                return true;
            } else if (price?.length === 0 && !currentDate) {
                return false;
            } else if (price?.length > 0 && !currentDate) {
                return true;
            } else if (price?.length > 0 && currentDate && !chooseVAT) {
                return true;
            }
        } else {
            return false;
        }
    };
    const renderBTSFooter = () => {
        return (
            <TouchableOpacity
                onPress={handleSendReason}
                disabled={disabledButton()}
                style={{
                    height: Mixins.scale(56),
                    width: '100%',
                    backgroundColor: disabledButton()
                        ? XworkColor.WHITE_E4E7EB
                        : XworkColor.BLUE_PRIMARY,
                    borderRadius: 16,
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: Mixins.scale(16)
                }}>
                <MyText
                    text={translate('confirm')}
                    typeFont="semiBold"
                    addSize={2}
                    style={{
                        color: XworkColor.WHITE
                    }}
                />
            </TouchableOpacity>
        );
    };
    const handleSendReason = async () => {
        const stringMoney = price.replace(/[.]/g, '');
        setTextContentCT('');
        bottomSheetModalRef.current?.close();
        await onPressDimiss();
        let textContext = textContentCT?.length === 0 ? '' : textContentCT;
        return setTimeout(() => {
            const data = {
                listAddImages: listImage,
                reasonCancel: textContext,
                pricePurchasingBHX: price?.length > 0 ? stringMoney || 0 : null,
                appliedDateOfBHX: currentDate
                    ? new Date(currentDate).getTime()
                    : null,
                isBeforeVat: chooseVAT
                    ? chooseVAT?.id === 0
                        ? true
                        : false
                    : null
            };

            onPressConfirm(data);
        }, 750);
    };
    let minDate = new Date(new Date().getTime() + 86400000);
    console.log(minDate, '12313131312312321312321');
    const onRemoveImages = (item) => {
        const itemWillDelete = listImage.find((deleteItem) => {
            return deleteItem === item;
        });
        const newsArr = listImage.filter((_item) => {
            return _item !== itemWillDelete;
        });
        setListImage(newsArr);
    };
    return (
        <BottomSheetWrapper
            enablePanDownToClose={true}
            android_keyboardInputMode="adjustResize"
            keyboardBlurBehavior="restore"
            ref={(ref: any) => (bottomSheetModalRef.current = ref)}
            onPressBackdrop={() => {
                onPressDimiss();
                handleSnapToIndex(0);
            }}
            snap={[heightSheet]}
            onChange={(index: number) => {
                if (index === -1) {
                    onPressDimiss();
                    handleSnapToIndex(0);
                }
            }}>
            <View
                style={{
                    paddingHorizontal: Mixins.scale(16),
                    flex: 1
                }}>
                <TouchableOpacity
                    style={{ flex: 1 }}
                    hitSlop={{
                        bottom: (global as any).props.insets.bottom + 50
                    }}
                    activeOpacity={1}
                    onPress={() => {
                        Keyboard.dismiss();
                        handleSnapToIndex(0);
                    }}>
                    <BottomSheetHeader
                        titleModal={translate('enter_price_rejection')}
                        onClosePress={onPressDimiss}
                    />
                    <MyText
                        text={`${translate('specific_resson')} `}
                        addSize={1}
                        style={{
                            color: XworkColor.BLACK
                        }}>
                        <MyText
                            text="* "
                            addSize={2}
                            style={{ color: XworkColor.COLOR_RED }}
                        />
                    </MyText>
                    <SafeContent
                        setTextContentCT={setTextContentCT}
                        textContentCT={textContentCT}
                        BottomSheetGorhom={BottomSheetGorhom}
                    />
                    <View
                        style={[
                            styles.styleRow,
                            { marginTop: Mixins.scale(16) }
                        ]}>
                        <View
                            style={{
                                marginRight: Mixins.scale(8),
                                width: '35%'
                            }}>
                            <MyText
                                text={`${translate('desired_date')} `}
                                style={{
                                    color: XworkColor.BLACK
                                }}></MyText>
                        </View>
                        <TouchableOpacity
                            onPress={() => {
                                setIsShowCalender(true);
                            }}
                            style={{
                                borderRadius: 12,
                                paddingHorizontal: Mixins.scale(8),
                                borderWidth: 1,
                                borderColor: '#CBD2D9',
                                height: Mixins.scale(48),
                                justifyContent: 'center',
                                alignItems: 'flex-end',
                                flex: 1
                            }}>
                            <MyText
                                text={
                                    currentDate
                                        ? `${moment(currentDate).format(
                                              'DD/MM/YYYY'
                                          )}`
                                        : ''
                                }
                                style={{
                                    color: XworkColor.BLACK
                                }}
                            />
                        </TouchableOpacity>
                    </View>

                    {RenderRadioBox()}
                    {RenderInputPrice()}
                    {RenderListImage()}
                    {renderBTSFooter()}
                </TouchableOpacity>
            </View>
            {isShowCalender && (
                <xworkData.component.ModalCalendarNew
                    startDate={selectedStartDate}
                    isVisible={isShowCalender}
                    onPressDimiss={() => {
                        setIsShowCalender(false);
                    }}
                    minDate={minDate}
                    onChangeSelectedDate={(startDate: any) => {
                        setCurrentDate(startDate);
                        setIsShowCalender(false);
                    }}
                    singleChoose={true}
                />
            )}
        </BottomSheetWrapper>
    );
});

const DataProvider = ({ children }: { children: any }) => {
    const [textContentCT, setTextContentCT] = useState<string>('');

    return (
        <DataContext.Provider
            value={{
                setTextContentCT,
                textContentCT
            }}>
            {children}
        </DataContext.Provider>
    );
};

const ModalAssess = React.memo((props: any) => {
    return (
        <DataProvider>
            <ModalInside {...props} />
        </DataProvider>
    );
});

const mapStateToProps = function (state: any) {
    return {
        xworkData: state.groupTicketReducer.xworkData
    };
};
const mapDispatchToProps = () => {
    return {};
};
export default connect(mapStateToProps, mapDispatchToProps)(ModalAssess);
const styles = StyleSheet.create({
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    imgCamera: {
        height: Mixins.scale(21),
        width: Mixins.scale(21)
    },
    touchIcon: {
        alignItems: 'center',
        backgroundColor: XworkColor.GRAY_NEUTRALS,
        borderRadius: Mixins.scale(22),
        height: Mixins.scale(44),
        justifyContent: 'center',
        width: Mixins.scale(44)
    },
    radio: {
        alignItems: 'center',
        borderRadius: Mixins.scale(20 / 2),
        borderWidth: Mixins.scale(1),
        height: Mixins.scale(20),
        justifyContent: 'center',
        marginRight: Mixins.scale(8),
        width: Mixins.scale(20)
    },
    radioAct: {
        backgroundColor: XworkColor.DARK_BLUE_60,
        borderRadius: Mixins.scale(10 / 2),
        height: Mixins.scale(10),
        width: Mixins.scale(10)
    },
    image: {
        borderRadius: Mixins.scale(10),
        height: Mixins.scale(50),
        width: Mixins.scale(50)
    },
    vImage: {
        alignItems: 'center',
        borderRadius: 10,
        height: Mixins.scale(51),
        justifyContent: 'center',
        marginRight: Mixins.scale(7),
        width: Mixins.scale(51)
    }
});
