import React, { Component } from 'react';
import { View, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { connect } from 'react-redux';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { ScrollView } from 'react-native-gesture-handler';
const { translate } = global.props.getTranslateConfig();

class ModalDetailRating extends Component {
    constructor(props) {
        super(props);
        this.state = {
            heightSheet: 420
        };
        this.bottomSheetModalRef = React.createRef(null);
    }
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
    };

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
        if (!this.props.data?.isSatisfy) {
            this.setState({ heightSheet: 420 });
        } else {
            this.setState({ heightSheet: 250 });
        }
    }
    componentDidUpdate(prevProps) {
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
        if (this.props.data !== prevProps.data && this.props.listUser) {
            if (!this.props.data?.isSatisfy) {
                this.setState({ heightSheet: 420 });
            } else {
                this.setState({ heightSheet: 173 });
            }
        }
    }

    renderHeader = () => {
        const { onPressDimiss, title } = this.props;

        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    height: 40
                }}>
                <TouchableOpacity
                    hitSlop={{
                        top: 5,
                        left: 5,
                        bottom: 5,
                        right: 5
                    }}
                    style={{ flexDirection: 'row' }}
                    onPress={() => {
                        this.bottomSheetModalRef.current?.close();
                        onPressDimiss();
                    }}>
                    <AntDesign
                        name="close"
                        size={20}
                        style={{
                            marginRight: Mixins.scale(12)
                        }}
                    />
                    <MyText
                        text={title}
                        numberOfLines={1}
                        typeFont="semiBold"
                        addSize={3}
                        style={{
                            color: Colors.BLACK
                        }}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    renderContent = () => {
        const { data, imageAssets } = this.props;
        return (
            <View
                style={{
                    marginTop: 8,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                <Image
                    style={{
                        height: 72,
                        width: 72
                    }}
                    source={
                        data?.isSatisfy
                            ? imageAssets?.emoji
                            : imageAssets?.emoji_1
                    }
                />
                <MyText
                    addSize={2}
                    text={
                        data?.isSatisfy
                            ? translate('very_satisfied')
                            : translate('dissatisfaction')
                    }
                    style={{ marginTop: 16 }}
                />
                {!data?.isSatisfy && (
                    <View
                        style={{
                            textAlignVertical: 'top',
                            height: 150,
                            width: '100%',
                            borderColor: Colors.GRAY_NEUTRALS,
                            borderWidth: 2,
                            paddingHorizontal: 16,
                            paddingTop: 16,
                            borderRadius: 12,
                            marginTop: 16
                        }}>
                        <ScrollView
                            style={{ flex: 1 }}
                            showsHorizontalScrollIndicator={false}
                            showsVerticalScrollIndicator={false}
                            contentContainerStyle={{ paddingBottom: 40 }}>
                            <MyText
                                text={`${translate('contribute_reason')}: ${
                                    data?.reason
                                }`}
                                style={{ marginTop: 8 }}
                            />
                        </ScrollView>
                    </View>
                )}
            </View>
        );
    };

    render() {
        const { onPressDimiss, xworkData } = this.props;
        const { BottomSheetGorhom } = xworkData;
        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        onPressDimiss();
                    }
                }}
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        {...props}
                        onPress={onPressDimiss}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                enablePanDownToClose={true}
                style={styles.ctn}>
                <xworkData.GestureHandler.NativeViewGestureHandler>
                    <View
                        style={{
                            paddingHorizontal: 16
                        }}>
                        {this.renderHeader()}
                        {this.renderContent()}
                    </View>
                </xworkData.GestureHandler.NativeViewGestureHandler>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData
    };
};

const mapDispatchToProps = () => {
    return {};
};
export default connect(mapStateToProps, mapDispatchToProps)(ModalDetailRating);
const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    },
    ctn: {
        flex: 1,
        justifyContent: 'flex-end'
    }
});
