import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import { View, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { connect } from 'react-redux';
const { translate } = global.props.getTranslateConfig();
class ModalRequirements extends Component {
    constructor(props) {
        super(props);
        this.state = {
            itemSelected: null || this.props.itemSelected,
            heightKeyboard: 0,
            heightSheet: '55%',
            bankSelected: null,
            showListBank: false
        };
        this.isDone = false;
        this.bottomSheetModalRef = React.createRef(null);
        this.timeOut = -1;
    }

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
    }
    async componentDidUpdate(prevProps, prevState) {
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
        if (
            this.state.itemSelected !== prevState.itemSelected &&
            this.state.itemSelected
        ) {
            if (this.state.itemSelected.cancelReason === 'WRONGBANK') {
                this.setState({ heightSheet: '80%' });
            } else {
                this.setState({ heightSheet: '55%' });
            }
        }
    }

    componentWillUnmount() {}

    clearText = (text = '') => {
        const { handleSearchStore } = this.props;
        this.setState({ txtSearch: text });
        handleSearchStore(text);
    };

    clearState = () => {
        this.setState({
            storeSelected: []
        });
    };

    handleSelected = (item) => {
        if (item?.cancelReason === 'WRONGBANK') {
            this.setState({
                showListBank: true
            });
        }

        if (item?.cancelReason === 'WRONGBANK') {
            this.setState({ heightSheet: '80%' });
        } else {
            this.setState({ heightSheet: '55%' });
        }
        this.setState({
            itemSelected: item
        });
    };
    renderListItem = () => {
        const { xworkData, data, listBank } = this.props;
        const { itemSelected, bankSelected, showListBank } = this.state;
        const { GestureHandler } = xworkData;
        console.log(listBank, 'listBanklistBanklistBank');

        return (
            <GestureHandler.FlatList
                contentContainerStyle={{ paddingBottom: 70 }}
                style={{ flex: 1 }}
                keyboardShouldPersistTaps="handled"
                data={data}
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={10}
                onEndReachedThreshold={0.1}
                bounces
                renderItem={({ item }) => {
                    let checkItem =
                        itemSelected?.cancelReason === item?.cancelReason;
                    return (
                        <>
                            <TouchableOpacity
                                key={item.id}
                                onPress={() => {
                                    this.handleSelected(item);
                                }}
                                style={styles.viewTouch}>
                                <View
                                    style={[
                                        styles.radio,
                                        {
                                            borderColor: checkItem
                                                ? Colors.BLUE_PRIMARY
                                                : Colors.GRAY_PLACEHODER
                                        }
                                    ]}>
                                    {checkItem && (
                                        <View style={styles.radioAct} />
                                    )}
                                </View>
                                <MyText
                                    style={{
                                        flex: 1,
                                        color: Colors.BLACK_HEADER_TITLE
                                    }}
                                    addSize={2}
                                    text={`${item.cancelReasonName}`}
                                    numberOfLines={2}
                                />
                            </TouchableOpacity>
                            {itemSelected?.cancelReason === 'WRONGBANK' &&
                                item?.cancelReason === 'WRONGBANK' && (
                                    <View style={{ marginTop: 16 }}>
                                        <MyText
                                            style={{
                                                color: Colors.BLACK_HEADER_TITLE
                                            }}
                                            addSize={2}
                                            text={`${translate(
                                                'choose_the_right_bank'
                                            )}:`}
                                            typeFont={'bold'}
                                        />
                                        <TouchableOpacity
                                            onPress={() => {
                                                if (showListBank) {
                                                    this.setState({
                                                        showListBank: false,
                                                        heightSheet: '55%'
                                                    });
                                                } else {
                                                    this.setState({
                                                        showListBank: true,
                                                        heightSheet: '80%'
                                                    });
                                                }
                                            }}
                                            style={styles.viewButtonDropDown}>
                                            <View style={{ flex: 1 }}>
                                                <MyText
                                                    style={{
                                                        color: bankSelected
                                                            ? Colors.BLACK
                                                            : '#616E7C'
                                                    }}
                                                    addSize={2}
                                                    numberOfLines={1}
                                                    text={
                                                        bankSelected
                                                            ? bankSelected?.bankName
                                                            : `${translate(
                                                                  'choose_bank'
                                                              )}...`
                                                    }
                                                />
                                            </View>
                                            <AntDesign
                                                name={
                                                    showListBank ? 'up' : 'down'
                                                }
                                                size={14}
                                                color={'#616E7C'}
                                            />
                                        </TouchableOpacity>
                                        {showListBank && (
                                            <GestureHandler.FlatList
                                                contentContainerStyle={{
                                                    paddingBottom: 70
                                                }}
                                                keyboardShouldPersistTaps="handled"
                                                data={listBank?.data}
                                                style={{
                                                    backgroundColor:
                                                        Colors.WHITE,
                                                    paddingHorizontal:
                                                        Mixins.scale(16),
                                                    height: Mixins.scale(220),
                                                    borderRadius:
                                                        Mixins.scale(16),
                                                    borderColor: Colors.GRAYF4,
                                                    borderWidth: 1,
                                                    shadowColor: '#000',
                                                    shadowOffset: {
                                                        width: 0,
                                                        height: 4
                                                    },
                                                    shadowOpacity: 0.3,
                                                    shadowRadius: 4.65,

                                                    elevation: 8
                                                }}
                                                showsVerticalScrollIndicator={
                                                    false
                                                }
                                                renderItem={({ item }) => {
                                                    return (
                                                        <TouchableOpacity
                                                            key={item.companyId}
                                                            onPress={() => {
                                                                this.setState({
                                                                    showListBank: false,
                                                                    bankSelected:
                                                                        item,
                                                                    heightSheet:
                                                                        '55%'
                                                                });
                                                            }}
                                                            style={
                                                                styles.viewTouch
                                                            }>
                                                            <MyText
                                                                style={{
                                                                    flex: 1,
                                                                    color: Colors.BLACK_HEADER_TITLE
                                                                }}
                                                                addSize={2}
                                                                text={`${item.bankName}`}
                                                                numberOfLines={
                                                                    2
                                                                }
                                                            />
                                                        </TouchableOpacity>
                                                    );
                                                }}
                                            />
                                        )}
                                    </View>
                                )}
                        </>
                    );
                }}
            />
        );
    };
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
        this.isDone = false;
    };
    renderButton = () => {
        const { onPressDimiss, onPressConfirm } = this.props;
        const { bankSelected, itemSelected } = this.state;
        let disableButton =
            (itemSelected?.cancelReason === 'WRONGBANK' && !bankSelected) ||
            !itemSelected;
        return (
            <View
                style={{
                    height: Mixins.scale(56),
                    marginTop: Mixins.scale(16),
                    marginBottom: Mixins.scale(16),
                    flexDirection: 'row',
                    justifyContent: 'space-between'
                }}>
                <TouchableOpacity
                    onPress={onPressDimiss}
                    style={[
                        styles.buttonGetTicket,
                        {
                            borderColor: Colors.DARK_BLUE_60,
                            borderWidth: 0.5,
                            marginRight: Mixins.scale(16),
                            backgroundColor: Colors.WHITE
                        }
                    ]}>
                    <MyText
                        text={translate('back')}
                        addSize={2}
                        style={{ color: Colors.DARK_BLUE_60 }}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.buttonGetTicket,
                        {
                            opacity: disableButton ? 0.5 : 1
                        }
                    ]}
                    disabled={disableButton}
                    onPress={() => {
                        onPressDimiss();
                        onPressConfirm(itemSelected, bankSelected);
                    }}>
                    <MyText
                        text={translate('confirm')}
                        addSize={2}
                        style={{ color: Colors.WHITE }}
                        typeFont="semiBold"
                    />
                </TouchableOpacity>
            </View>
        );
    };
    render() {
        const { onPressDimiss, titleModal, handleAddStore, xworkData } =
            this.props;
        const deviceHeight = Dimensions.get('window').height;
        const { GestureHandler, BottomSheetGorhom } = xworkData;

        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        onPressDimiss();
                    }
                }}
                keyboardBlurBehavior="restore"
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        onPress={() => {
                            this.clearState();
                            onPressDimiss();
                        }}
                        {...props}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                android_keyboardInputMode="adjustResize"
                enablePanDownToClose={true}>
                <GestureHandler.NativeViewGestureHandler
                    disallowInterruption={true}>
                    <View
                        style={{
                            flex: 1,
                            paddingBottom: 20,
                            paddingHorizontal: Mixins.scale(16)
                        }}>
                        <BottomSheetGorhom.BottomSheetView
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                height: Mixins.scale(40),
                                justifyContent: 'space-between'
                            }}>
                            <TouchableOpacity
                                hitSlop={{
                                    top: 5,
                                    left: 5,
                                    bottom: 5,
                                    right: 5
                                }}
                                onPress={() => {
                                    this.bottomSheetModalRef.current?.close();
                                    onPressDimiss();
                                    this.clearState();
                                    this.isDone = true;
                                }}>
                                <AntDesign
                                    name="close"
                                    size={20}
                                    style={{
                                        marginRight: Mixins.scale(16)
                                    }}
                                />
                            </TouchableOpacity>
                            <MyText
                                text={titleModal}
                                numberOfLines={1}
                                addSize={2}
                                style={{ flex: 1 }}
                            />
                        </BottomSheetGorhom.BottomSheetView>
                        {this.renderListItem()}
                        {this.renderButton()}
                    </View>
                </GestureHandler.NativeViewGestureHandler>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}
const mapStateToProps = function (state) {
    return {
        listBank: state.detailTicketReducer.listBank
    };
};
const mapDispatchToProps = (dispatch) => {};
export default connect(mapStateToProps, mapDispatchToProps)(ModalRequirements);

const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingBottom: Mixins.scale(4),
        paddingTop: Mixins.scale(12)
    },

    radio: {
        alignItems: 'center',
        borderRadius: Mixins.scale(20 / 2),
        borderWidth: Mixins.scale(1),
        height: Mixins.scale(20),
        justifyContent: 'center',
        marginRight: Mixins.scale(16),
        width: Mixins.scale(20)
    },
    radioAct: {
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(10 / 2),
        height: Mixins.scale(10),
        width: Mixins.scale(10)
    },
    viewTouch: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(50)
    },
    buttonGetTicket: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderColor: Colors.DARK_BLUE_60,
        borderRadius: 20,
        borderWidth: 0.5,
        flex: 1,
        justifyContent: 'center',
        paddingVertical: Mixins.scale(16)
    },
    viewButtonDropDown: {
        borderRadius: Mixins.scale(16),
        borderColor: Colors.GRAYF4,
        borderWidth: 1,
        height: Mixins.scale(56),
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        justifyContent: 'center',
        flexDirection: 'row',
        alignItems: 'center'
    }
});
