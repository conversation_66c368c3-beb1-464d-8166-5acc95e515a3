import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    Platform,
    Image
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import moment from 'moment/moment';
import { BackHandler } from 'react-native';
const { translate } = global.props.getTranslateConfig();
import {
    RenderPlaceholderStatus,
    RenderStatus,
    CheckBox,
    RenderTime,
    RenderStore,
    RenderPlaceholderStore,
    RenderPlaceholderGroup
} from './ComponentModal';
import { ModalListStore } from '.';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import * as _actionTicket from '../Tickets/Ticket/action';
import * as _actionHome from '../Tickets/action';

class ModalFilterNewFeed extends Component {
    constructor(props) {
        super(props);
        this.state = {
            dataFilter: {
                selectedStatus: null,
                selectedMember: [],
                selectedMemberCreated: null,
                storeSelected: [],
                startDate: '',
                endDate: '',
                textValue: '',
                isCheckTicketMyCreate: false,
                isCheckTicketMyFollow: false,
                isCheckTicketMyAssign: false,
                isCheckTicketSample: false,
                groupSelected: [],
                arrayAssigneeUserId: [],
                arrayCreateUserId: [],
                arrayWatcherId: [],
                selectedAssigneeUserId: [],
                selectedCreateUserId: [],
                selectedWatcherId: []
            },
            showModalStore: false,
            isShowCalender: false,
            chooseDate: '',
            showModalUser: false,
            heightSheet: '90%',
            dataLengthGeoLocation: 20,
            showModalGroup: false,
            groupDisplayLength: 10,
            memberData: { title: '' }
        };
        this.bottomSheetModalRef = React.createRef(null);
    }

    initData = () => {
        if (this.props.dataFilter) {
            this.setState({ dataFilter: this.props.dataFilter });
        }
    };

    componentDidMount() {
        if (this.props.isVisible) {
            this.openModal();
        }
        this.initData();
        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
    }
    componentWillUnmount() {
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.handleBackPress
        );
    }
    handlePreventBackAndroid = () => {
        this.props?.onPressDimiss?.();
        this.bottomSheetModalRef.current?.close();
    };
    handleBackPress = () => {
        if (this.props.isVisible && Platform.OS === 'android') {
            this.handlePreventBackAndroid();
            return true;
        }
    };
    componentDidUpdate(prevProps) {
        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
    }
    onDateChange(date) {
        this.setState({
            showCalendarFrom: date
        });
    }
    clearState = () => {
        this.setState({
            dataFilter: {
                selectedStatus: null,
                selectedMember: [],
                selectedMemberCreated: null,
                storeSelected: [],
                startDate: '',
                endDate: '',
                textValue: '',
                isCheckTicketMyCreate: false,
                isCheckTicketMyFollow: false,
                isCheckTicketMyAssign: false,
                isCheckTicketSample: false,
                groupSelected: [],
                arrayAssigneeUserId: [],
                arrayCreateUserId: [],
                arrayWatcherId: []
            }
        });
        this.props.saveDataFilter(null);
    };
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
    };
    onPressApplyFilter = () => {
        const { handleFilter, onPressDimiss, saveDataFilter } = this.props;
        saveDataFilter(this.state.dataFilter);
        handleFilter(this.state.dataFilter);
        onPressDimiss();

        this.bottomSheetModalRef.current?.close();
    };
    renderBottomButton = () => {
        const { onPressDimiss, handleRefreshData } = this.props;
        const {
            isCheckTicketSample,
            isCheckTicketMyCreate,
            isCheckTicketMyFollow,
            isCheckTicketMyAssign
        } = this.state.dataFilter;

        const checkApplyButton =
            this.state.dataFilter.selectedMember?.length === 0 &&
            !helper.IsValidateObject(this.state.dataFilter.selectedStatus) &&
            this.state.dataFilter.endDate?.length === 0 &&
            this.state.dataFilter.textValue?.length === 0 &&
            !isCheckTicketMyAssign &&
            !isCheckTicketSample &&
            !isCheckTicketMyCreate &&
            !isCheckTicketMyFollow;
        return (
            <View style={styles.viewBottomButton}>
                <TouchableOpacity
                    onPress={() => {
                        handleRefreshData();
                        this.bottomSheetModalRef.current?.close();
                        this.clearState();
                        onPressDimiss();
                    }}
                    style={{
                        flex: 1,
                        height: Mixins.scale(56),
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderColor: Colors.GRAYF8,
                        borderWidth: 0.5,
                        borderRadius: 12,
                        marginRight: Mixins.scale(6)
                    }}>
                    <MyText
                        style={{ color: Colors.GRAYF8 }}
                        addSize={2}
                        text={translate('delete')}
                    />
                </TouchableOpacity>
                <View
                    style={[
                        styles.btnApply,
                        { opacity: checkApplyButton ? 0.5 : 1 }
                    ]}>
                    <TouchableOpacity
                        onPress={this.onPressApplyFilter}
                        disabled={checkApplyButton}
                        style={{
                            borderRadius: 12,
                            width: '100%',
                            height: Mixins.scale(56),
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <MyText
                            style={{ color: Colors.WHITE }}
                            addSize={2}
                            text={translate('apply')}
                            typeFont="medium"
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    renderHeader = () => {
        const { onPressDimiss } = this.props;
        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center'
                }}>
                <TouchableOpacity
                    hitSlop={{
                        top: 5,
                        left: 5,
                        bottom: 5,
                        right: 5
                    }}
                    style={{ flexDirection: 'row' }}
                    onPress={() => {
                        onPressDimiss();
                        this.bottomSheetModalRef.current?.close();
                    }}>
                    <AntDesign
                        name="close"
                        size={20}
                        style={{
                            marginRight: Mixins.scale(16)
                        }}
                    />
                </TouchableOpacity>
                <MyText
                    text={translate('create_filter')}
                    numberOfLines={1}
                    addSize={3}
                    style={{
                        marginLeft: 12,
                        color: Colors.BLACK
                    }}
                />
            </View>
        );
    };
    handleDimissModalCalendar = () => {
        this.setState({
            isShowCalender: false
        });
    };

    onChangeStatus = (item) => {
        this.setState((prevState) => ({
            dataFilter: {
                ...prevState.dataFilter,
                selectedStatus: item
            }
        }));
    };
    renderStatus = () => {
        const { selectedStatus } = this.state.dataFilter;
        const { listStatusTicket } = this.props;
        return (
            <RenderStatus
                titleHeader={translate('job_status')}
                data={listStatusTicket}
                value={selectedStatus}
                onChange={(item) => this.onChangeStatus(item)}
                renderPlaceholder={() => {
                    return (
                        <RenderPlaceholderStatus
                            selectedStatus={selectedStatus}
                            listStatusTicket={listStatusTicket}
                            xworkData={this.props.xworkData}
                        />
                    );
                }}
                xworkData={this.props.xworkData}
            />
        );
    };
    renderKeyFilter = () => {
        const { xworkData } = this.props;
        const { BottomSheetGorhom } = xworkData;
        return (
            <View style={{ marginBottom: Mixins.scale(16) }}>
                <MyText
                    text={translate('filter_keyword')}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{ marginVertical: 4, color: Colors.BLACK }}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderColor: Colors.GRAYF6,
                        borderRadius: Mixins.scale(12),
                        borderWidth: 0.5,
                        height: Mixins.scale(44),
                        marginTop: Mixins.scale(12),
                        paddingHorizontal: Mixins.scale(12)
                    }}>
                    <BottomSheetGorhom.BottomSheetTextInput
                        defaultValue={this.state.dataFilter.textValue}
                        textAlign="left"
                        onChangeText={(text) => {
                            this.setState((prevState) => ({
                                dataFilter: {
                                    ...prevState.dataFilter,
                                    textValue: text
                                }
                            }));
                        }}
                        onBlur={() => {}}
                        style={{
                            flex: 1,
                            height: Mixins.scale(44)
                        }}
                        placeholder={translate('enter_keyword')}
                        placeholderTextColor={Colors.GRAYF9}
                        placeholderStyle={{ fontSize: 14 }}
                    />
                    {this.state.dataFilter?.textValue &&
                        this.state.dataFilter?.textValue?.length > 0 && (
                            <TouchableOpacity
                                hitSlop={{
                                    top: 5,
                                    right: 5,
                                    left: 5,
                                    bottom: 5
                                }}
                                style={{
                                    height: 20,
                                    width: 20
                                }}
                                onPress={() => {
                                    this.setState((prevState) => ({
                                        dataFilter: {
                                            ...prevState.dataFilter,
                                            textValue: ''
                                        }
                                    }));
                                }}>
                                <Image
                                    resizeMode="contain"
                                    source={{ uri: 'ic_error_ticket' }}
                                    style={{
                                        height: Mixins.scale(20),
                                        width: Mixins.scale(20)
                                    }}
                                />
                            </TouchableOpacity>
                        )}
                </View>
                <MyText
                    text={translate('filter_by')}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        marginTop: Mixins.scale(16),
                        marginBottom: Mixins.scale(4),
                        color: Colors.BLACK
                    }}
                />
                <View style={styles.rowCheckBox}>
                    <CheckBox
                        boxType="square"
                        style={styles.viewCheckBox}
                        value={this.state.dataFilter.isCheckTicketMyCreate}
                        onValueChange={() => {
                            this.setState((prevState) => ({
                                dataFilter: {
                                    ...prevState.dataFilter,
                                    isCheckTicketMyCreate:
                                        !prevState.dataFilter
                                            .isCheckTicketMyCreate
                                }
                            }));
                        }}
                    />
                    <MyText
                        text={translate('ticket_me_create')}
                        numberOfLines={1}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                </View>
                <View style={styles.rowCheckBox}>
                    <CheckBox
                        boxType="square"
                        style={styles.viewCheckBox}
                        onCheckColor={Colors.DARK_BLUE_60}
                        tintColor={Colors.GRAYF6}
                        tintColors={{
                            true: Colors.DARK_BLUE_60,
                            false: Colors.GRAYF6
                        }}
                        value={this.state.dataFilter.isCheckTicketMyFollow}
                        onValueChange={() => {
                            this.setState((prevState) => ({
                                dataFilter: {
                                    ...prevState.dataFilter,
                                    isCheckTicketMyFollow:
                                        !prevState.dataFilter
                                            .isCheckTicketMyFollow
                                }
                            }));
                        }}
                    />
                    <MyText
                        text={translate('ticket_me_follow')}
                        numberOfLines={1}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                </View>
                <View style={styles.rowCheckBox}>
                    <CheckBox
                        boxType="square"
                        style={styles.viewCheckBox}
                        onCheckColor={Colors.DARK_BLUE_60}
                        tintColor={Colors.GRAYF6}
                        tintColors={{
                            true: Colors.DARK_BLUE_60,
                            false: Colors.GRAYF6
                        }}
                        value={this.state.dataFilter.isCheckTicketMyAssign}
                        onValueChange={() => {
                            this.setState((prevState) => ({
                                dataFilter: {
                                    ...prevState.dataFilter,
                                    isCheckTicketMyAssign:
                                        !prevState.dataFilter
                                            .isCheckTicketMyAssign
                                }
                            }));
                        }}
                    />
                    <MyText
                        text={translate('ticket_me_assgined')}
                        numberOfLines={1}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                </View>
                <View style={styles.rowCheckBox}>
                    <CheckBox
                        boxType="square"
                        style={styles.viewCheckBox}
                        onCheckColor={Colors.DARK_BLUE_60}
                        tintColor={Colors.GRAYF6}
                        tintColors={{
                            true: Colors.DARK_BLUE_60,
                            false: Colors.GRAYF6
                        }}
                        value={this.state.dataFilter.isCheckTicketSample}
                        onValueChange={() => {
                            this.setState((prevState) => ({
                                dataFilter: {
                                    ...prevState.dataFilter,
                                    isCheckTicketSample:
                                        !prevState.dataFilter
                                            .isCheckTicketSample
                                }
                            }));
                        }}
                    />
                    <MyText
                        text={translate('sample_ticket')}
                        numberOfLines={1}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                </View>
            </View>
        );
    };
    formatTimeMoment = (value) => {
        return moment(value).format('DD/MM/YYYY');
    };

    handleAddMember = (listMember = []) => {
        const { memberData } = this.state;
        if (memberData.id === 1) {
            this.setState((prevState) => ({
                dataFilter: {
                    ...prevState.dataFilter,
                    arrayCreateUserId: listMember,
                    selectedCreateUserId: listMember
                },

                showModalUser: false,
                heightSheet: '90%'
            }));
        }
        if (memberData.id === 2) {
            this.setState((prevState) => ({
                dataFilter: {
                    ...prevState.dataFilter,
                    arrayAssigneeUserId: listMember,
                    selectedAssigneeUserId: listMember
                },
                showModalUser: false,
                heightSheet: '90%'
            }));
        }
        if (memberData.id === 3) {
            this.setState((prevState) => ({
                dataFilter: {
                    ...prevState.dataFilter,
                    arrayWatcherId: listMember,
                    selectedWatcherId: listMember
                },

                showModalUser: false,
                heightSheet: '90%'
            }));
        }
    };
    handleAddStore = (listStore = []) => {
        this.setState((prevState) => ({
            dataFilter: { ...prevState.dataFilter, storeSelected: listStore },
            showModalStore: false,
            heightSheet: '90%'
        }));
    };
    handleAddGroup = (listGroup = []) => {
        this.setState((prevState) => ({
            dataFilter: { ...prevState.dataFilter, groupSelected: listGroup },
            showModalGroup: false,
            heightSheet: '90%'
        }));
    };
    handleSheetUserShow = (data) => {
        this.setState({
            showModalUser: true,
            memberData: data
        });
    };
    renderCreateUser = (data) => {
        const { xworkData } = this.props;
        const { GestureHandler } = xworkData;
        const { dataFilter } = this.state;
        return (
            <View>
                <MyText
                    text={data.title}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{ marginVertical: 4, color: Colors.BLACK }}
                />
                <TouchableOpacity
                    onPress={() => {
                        this.handleSheetUserShow(data);
                    }}
                    style={styles.viewButtonDropDown}>
                    {!dataFilter.arrayCreateUserId ||
                    dataFilter.arrayCreateUserId?.length === 0 ? (
                        <MyText
                            numberOfLines={1}
                            text={data.title}
                            style={{
                                color: Colors.GRAYF9,
                                fontSize: 14
                            }}
                        />
                    ) : (
                        <GestureHandler.ScrollView
                            keyboardShouldPersistTaps="handled"
                            horizontal
                            contentContainerStyle={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginRight: Mixins.scale(4)
                            }}>
                            {dataFilter.arrayCreateUserId?.map(
                                (element, index) => {
                                    return (
                                        <View
                                            key={index}
                                            style={{
                                                width: Mixins.scale(100),
                                                borderWidth: 0.5,
                                                height: Mixins.scale(35),
                                                marginRight: Mixins.scale(4),
                                                borderRadius: 12,
                                                backgroundColor:
                                                    Colors.DARK_BLUE_10,
                                                borderColor:
                                                    Colors.DARK_BLUE_10,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <MyText
                                                numberOfLines={1}
                                                text={`${element?.user?.username}`}
                                                style={{
                                                    color: Colors.DARK_BLUE_40
                                                }}
                                            />
                                        </View>
                                    );
                                }
                            )}
                        </GestureHandler.ScrollView>
                    )}
                </TouchableOpacity>
            </View>
        );
    };
    renderAssigneeUser = (data) => {
        const { xworkData } = this.props;
        const { GestureHandler } = xworkData;
        const { dataFilter } = this.state;
        return (
            <View>
                <MyText
                    text={data.title}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{ marginVertical: 4, color: Colors.BLACK }}
                />
                <TouchableOpacity
                    onPress={() => {
                        this.handleSheetUserShow(data);
                    }}
                    style={styles.viewButtonDropDown}>
                    {!dataFilter.arrayAssigneeUserId ||
                    dataFilter.arrayAssigneeUserId?.length === 0 ? (
                        <MyText
                            numberOfLines={1}
                            text={data.title}
                            style={{
                                color: Colors.GRAYF9,
                                fontSize: 14
                            }}
                        />
                    ) : (
                        <GestureHandler.ScrollView
                            keyboardShouldPersistTaps="handled"
                            horizontal
                            contentContainerStyle={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginRight: Mixins.scale(4)
                            }}>
                            {dataFilter.arrayAssigneeUserId?.map(
                                (element, index) => {
                                    return (
                                        <View
                                            key={index}
                                            style={{
                                                width: Mixins.scale(100),
                                                borderWidth: 0.5,
                                                height: Mixins.scale(35),
                                                marginRight: Mixins.scale(4),
                                                borderRadius: 12,
                                                backgroundColor:
                                                    Colors.DARK_BLUE_10,
                                                borderColor:
                                                    Colors.DARK_BLUE_10,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <MyText
                                                numberOfLines={1}
                                                text={`${element?.user?.username}`}
                                                style={{
                                                    color: Colors.DARK_BLUE_40
                                                }}
                                            />
                                        </View>
                                    );
                                }
                            )}
                        </GestureHandler.ScrollView>
                    )}
                </TouchableOpacity>
            </View>
        );
    };
    renderWatcherUser = (data) => {
        const { xworkData } = this.props;
        const { GestureHandler } = xworkData;
        const { dataFilter } = this.state;
        return (
            <View>
                <MyText
                    text={data.title}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{ marginVertical: 4, color: Colors.BLACK }}
                />
                <TouchableOpacity
                    onPress={() => {
                        this.handleSheetUserShow(data);
                    }}
                    style={styles.viewButtonDropDown}>
                    {!dataFilter.arrayWatcherId ||
                    dataFilter.arrayWatcherId?.length === 0 ? (
                        <MyText
                            numberOfLines={1}
                            text={data.title}
                            style={{
                                color: Colors.GRAYF9,
                                fontSize: 14
                            }}
                        />
                    ) : (
                        <GestureHandler.ScrollView
                            keyboardShouldPersistTaps="handled"
                            horizontal
                            contentContainerStyle={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginRight: Mixins.scale(4)
                            }}>
                            {dataFilter.arrayWatcherId?.map(
                                (element, index) => {
                                    return (
                                        <View
                                            key={index}
                                            style={{
                                                width: Mixins.scale(100),
                                                borderWidth: 0.5,
                                                height: Mixins.scale(35),
                                                marginRight: Mixins.scale(4),
                                                borderRadius: 12,
                                                backgroundColor:
                                                    Colors.DARK_BLUE_10,
                                                borderColor:
                                                    Colors.DARK_BLUE_10,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <MyText
                                                numberOfLines={1}
                                                text={`${element?.user?.username}`}
                                                style={{
                                                    color: Colors.DARK_BLUE_40
                                                }}
                                            />
                                        </View>
                                    );
                                }
                            )}
                        </GestureHandler.ScrollView>
                    )}
                </TouchableOpacity>
            </View>
        );
    };
    onChangeSelectedDate = (startDate, endDate) => {
        if (startDate && endDate) {
            this.setState((prevState) => ({
                isShowCalender: false,
                dataFilter: {
                    ...prevState.dataFilter,
                    endDate: endDate,
                    startDate: startDate
                }
            }));
        }
    };
    loadMoreLocation = (text, refresh = true) => {
        const { dataLengthGeoLocation } = this.state;
        if (refresh) {
            const data = {
                supportServiceId: -1,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 20,
                    search: text || ''
                },
                requestId: ''
            };
            this.props.actionTicket.getListLocationgeo(data);
            this.setState({
                dataLengthGeoLocation: 20
            });
        } else {
            const data = {
                supportServiceId: -1,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: dataLengthGeoLocation + 10,
                    search: text || ''
                },
                requestId: ''
            };
            this.props.actionTicket.getListLocationgeo(data);
            this.setState({
                dataLengthGeoLocation: dataLengthGeoLocation + 10
            });
        }
    };

    loadMoreGroup = (text, refresh = true) => {
        console.log('loadMoreGrouploadMoreGroup');
        const { groupDisplayLength } = this.state;
        if (refresh) {
            const data = {
                owner: -1,
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: ''
            };
            this.props.actionHome.getListGroupTicket(data);
            this.setState({
                groupDisplayLength: 10
            });
        } else {
            const data = {
                owner: -1,
                iDisplayStart: 0,
                iDisplayLength: groupDisplayLength + 10,
                search: text || ''
            };
            this.props.actionHome.getListGroupTicket(data, true);
            this.setState({
                groupDisplayLength: groupDisplayLength + 10
            });
        }
    };

    renderGroup = () => {
        return (
            <RenderStore
                titleHeader={translate('select_group')}
                onPress={() => {
                    this.setState({ showModalGroup: true });
                }}
                renderPlaceholder={() => {
                    return (
                        <RenderPlaceholderGroup
                            storeSelected={this.state.dataFilter.groupSelected}
                            listStatusTicket={this.props.loadMoreGroup?.data}
                            xworkData={this.props.xworkData}
                        />
                    );
                }}
                xworkData={this.props.xworkData}
            />
        );
    };

    listUser = () => {
        const { memberData, dataFilter } = this.state;
        if (memberData.id === 1) return dataFilter.selectedCreateUserId;
        if (memberData.id === 2) return dataFilter.selectedAssigneeUserId;
        if (memberData.id === 3) return dataFilter.selectedWatcherId;
    };

    render() {
        const { onPressDimiss, xworkData, listGroupTicket } = this.props;
        const { GestureHandler, BottomSheetGorhom } = xworkData;
        const { memberData } = this.state;
        console.log(
            'listGroupTicketlistGroupTicket',
            JSON.stringify(listGroupTicket, null, 2)
        );
        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        onPressDimiss();
                    }
                }}
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        {...props}
                        onPress={onPressDimiss}
                    />
                )}
                android_keyboardInputMode="adjustResize"
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                enablePanDownToClose={true}
                style={styles.ctn}>
                <GestureHandler.NativeViewGestureHandler
                    disallowInterruption={true}>
                    <View
                        style={{
                            flex: 1,
                            marginTop: Mixins.scale(16),
                            paddingHorizontal: Mixins.scale(12)
                        }}>
                        <View style={{ flex: 1 }}>
                            {this.renderHeader()}
                            <View style={styles.viewLine} />
                            <GestureHandler.ScrollView
                                style={{
                                    flex: 1,
                                    paddingTop: Mixins.scale(20)
                                }}
                                contentContainerStyle={{ paddingBottom: 40 }}
                                showsVerticalScrollIndicator={false}>
                                {this.renderGroup()}
                                {this.renderStatus()}
                                <RenderStore
                                    titleHeader={translate(
                                        'choose_supermarket'
                                    )}
                                    onPress={() => {
                                        this.setState({ showModalStore: true });
                                    }}
                                    renderPlaceholder={() => {
                                        return (
                                            <RenderPlaceholderStore
                                                storeSelected={
                                                    this.state.dataFilter
                                                        .storeSelected
                                                }
                                                listStatusTicket={
                                                    this.props.loadMoreLocation
                                                        ?.data
                                                }
                                                xworkData={this.props.xworkData}
                                            />
                                        );
                                    }}
                                    xworkData={this.props.xworkData}
                                />

                                {this.renderCreateUser({
                                    title: translate('ticket_creator'),
                                    id: 1
                                })}
                                {this.renderAssigneeUser({
                                    title: translate('receiver'),
                                    id: 2
                                })}
                                {this.renderWatcherUser({
                                    title: translate('watcher'),
                                    id: 3
                                })}
                                <RenderTime
                                    startDate={this.state.dataFilter.startDate}
                                    endDate={this.state.dataFilter.endDate}
                                    onPressShowModal={(item) => {
                                        this.setState({
                                            chooseDate: item,
                                            isShowCalender: true
                                        });
                                    }}
                                />
                                {this.renderKeyFilter()}
                            </GestureHandler.ScrollView>
                            {this.renderBottomButton()}
                        </View>
                        <xworkData.component.ModalCalendarNew
                            isVisible={this.state.isShowCalender}
                            endDate={this.state.dataFilter.endDate}
                            startDate={this.state.dataFilter.startDate}
                            chooseDate={this.state.chooseDate}
                            onPressDimiss={this.handleDimissModalCalendar}
                            onChangeSelectedDate={this.onChangeSelectedDate}
                        />
                        {this.state.showModalStore && (
                            <ModalListStore
                                isVisible={this.state.showModalStore}
                                titleModal={translate('choose_supermarket')}
                                onPressDimiss={() => {
                                    this.setState({
                                        showModalStore: false,
                                        heightSheet: '90%'
                                    });
                                }}
                                handleLoadMoreUser={this.loadMoreLocation}
                                handleSearchStore={this.loadMoreLocation}
                                listStore={this.props.listLocationGeo.data}
                                storeSelected={
                                    this.state.dataFilter.storeSelected
                                }
                                xworkData={this.props.xworkData}
                                handleAddStore={this.handleAddStore}
                                isChooseGroup={false}
                            />
                        )}
                        {this.state.showModalGroup && (
                            <ModalListStore
                                isVisible={this.state.showModalGroup}
                                titleModal={translate('select_group')}
                                onPressDimiss={() => {
                                    this.setState({
                                        showModalGroup: false,
                                        heightSheet: '90%'
                                    });
                                }}
                                handleLoadMoreUser={this.loadMoreGroup}
                                handleSearchStore={this.loadMoreGroup}
                                listStore={listGroupTicket}
                                storeSelected={
                                    this.state.dataFilter.groupSelected
                                }
                                xworkData={this.props.xworkData}
                                handleAddStore={this.handleAddGroup}
                                isChooseGroup={true}
                            />
                        )}
                        {this.state.showModalUser && (
                            <xworkData.component.ModalAddUser
                                isVisible={this.state.showModalUser}
                                hideRole={true}
                                titleModal={memberData.title}
                                onPressDimiss={() => {
                                    this.setState({
                                        showModalUser: false,
                                        heightSheet: '90%'
                                    });
                                }}
                                handleLoadMoreUser={
                                    this.props.handleLoadMoreUser
                                }
                                handleSearchUser={this.props.handleSearchUser}
                                listUser={this.props.listUser}
                                listMemberGroup={[]}
                                userSelected={this.listUser()}
                                handleAddMember={this.handleAddMember}
                            />
                        )}
                    </View>
                </GestureHandler.NativeViewGestureHandler>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}
const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listTicketNewFeed: state.groupTicketReducer.listTicketNewFeed,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listAllStatusTicket: state.ticketReducer.listAllStatusTicket,
        listUser: state.groupTicketReducer.listUser?.data,
        typeListData: state.ticketReducer.typeListData,
        listNewFeed: state.groupTicketReducer.listNewFeed,
        dataLocal: state.groupTicketReducer.dataLocal,
        listTicket: state.ticketReducer.listTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listGroupTicket: state.groupTicketReducer.listGroupTicket.data
    };
};
const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ModalFilterNewFeed);
const styles = StyleSheet.create({
    btnApply: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: 12,
        flex: 1,
        height: Mixins.scale(56),
        justifyContent: 'center',
        marginLeft: Mixins.scale(6)
    },

    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingVertical: 12
    },
    ctn: {
        flex: 1
    },

    rowCheckBox: {
        alignItems: 'center',
        flexDirection: 'row',
        marginLeft: 4,
        marginTop: Mixins.scale(12)
    },
    viewBottomButton: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(8),
        paddingBottom: Mixins.scale(24)
    },
    viewButtonDropDown: {
        backgroundColor: Colors.WHITE,
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        height: Mixins.scale(44),
        justifyContent: 'center',
        marginVertical: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        width: '100%'
    },

    viewCheckBox: {
        height: 20,
        marginRight: Mixins.scale(10),
        width: 20
    },
    viewLine: {
        backgroundColor: Colors.GRAYF5,
        height: 1,
        marginTop: Mixins.scale(20),
        width: '100%'
    }
});
