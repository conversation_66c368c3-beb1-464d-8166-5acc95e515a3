import React from 'react';
import {
    View,
    StyleSheet,
    Image,
    ScrollView,
    TouchableOpacity
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { MultiSelectComponent } from '@mwg-kits/components';

import AntDesign from 'react-native-vector-icons/AntDesign';
import moment from 'moment';
import { CONST_API } from '../constant';
const { translate } = global.props.getTranslateConfig();
export const CheckBox = ({ value, onValueChange, style }) => {
    return (
        <TouchableOpacity
            activeOpacity={1}
            hitSlop={{ top: 10, right: 10, left: 10, bottom: 10 }}
            style={[
                {
                    height: 18,
                    width: 18,
                    borderRadius: 4,
                    backgroundColor: value ? Colors.DARK_BLUE_60 : Colors.WHITE,
                    borderColor: value ? Colors.DARK_BLUE_60 : Colors.GRAYF4,
                    borderWidth: 1,
                    justifyContent: 'center'
                },
                style
            ]}
            onPress={() => onValueChange()}>
            {value && (
                <Image
                    style={{
                        height: 11,
                        width: 11,
                        alignSelf: 'center'
                    }}
                    source={{
                        uri: 'ic_check_white'
                    }}
                    resizeMode="contain"
                />
            )}
        </TouchableOpacity>
    );
};

export const RenderSelectUsers = (props) => {
    return (
        <>
            <MyText
                text={props.titleHeader}
                numberOfLines={1}
                addSize={2}
                typeFont="medium"
                style={{
                    marginVertical: 4,
                    color: Colors.BLACK
                }}
            />
            <MultiSelectComponent
                style={styles.viewButtonDropDown}
                keyboardAvoiding={false}
                isKeyboardShow={false}
                selectedTextProps={{
                    numberOfLines: 1
                }}
                inverted={false}
                dropdownPosition={props.dropdownPosition || 'auto'}
                visibleSelectedItem={false}
                search={false}
                data={props.data}
                labelField="user.name"
                valueField="user.username"
                placeholder=" "
                value={props.value}
                maxHeight={Mixins.scale(140)}
                renderRightIcon={() => {
                    return (
                        <AntDesign
                            name="down"
                            color={Colors.GRAYF9}
                            size={16}
                            style={{
                                marginRight: Mixins.scale(4)
                            }}
                        />
                    );
                }}
                renderPlaceholder={props.renderPlaceholder}
                onChange={props.onChange}
                renderItem={(item, selected) => {
                    return (
                        <View
                            style={{
                                padding: 17,
                                flexDirection: 'row',
                                alignItems: 'center'
                            }}>
                            <View
                                style={{
                                    height: 20,
                                    width: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderWidth: 1.5,
                                    borderColor: selected
                                        ? Colors.BLUE_EB
                                        : Colors.GRAY_BODERRADIUS,
                                    borderRadius: 4,
                                    backgroundColor: selected
                                        ? Colors.BLUE_EB
                                        : Colors.WHITE,
                                    marginRight: 8
                                }}>
                                {selected && (
                                    <Image
                                        source={{
                                            uri: 'ic_check_white'
                                        }}
                                        style={{
                                            height: 16,
                                            width: 13,
                                            tintColor: Colors.WHITE
                                        }}
                                        resizeMode="contain"
                                    />
                                )}
                            </View>
                            <Image
                                style={{
                                    height: Mixins.scale(28),
                                    width: Mixins.scale(28),
                                    borderRadius: 22
                                }}
                                source={{
                                    uri: `${CONST_API.baseAvatarURI}${item.user.profile.image}`
                                }}
                            />
                            <MyText
                                style={{
                                    marginLeft: 12,
                                    alignItems: 'center'
                                }}
                                numberOfLines={1}
                                text={`${item.user.username} -  ${item.user.profile.lastName} ${item.user.profile.firstName}`}
                            />
                        </View>
                    );
                }}
                itemTextStyle={{
                    fontSize: 14,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    backgroundColor: Colors.WHITE,
                    borderRadius: Mixins.scale(12)
                }}
                showsVerticalScrollIndicator={false}
            />
        </>
    );
};
export const RenderPlaceholderMember = (props) => {
    if (props.selectedMember && props.selectedMember?.length > 0) {
        return (
            <ScrollView
                keyboardShouldPersistTaps="handled"
                showsHorizontalScrollIndicator
                horizontal
                contentContainerStyle={{
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: Mixins.scale(4)
                }}>
                {props.selectedMember?.map((element, index) => {
                    return (
                        <View key={index} style={styles.membersSelected}>
                            <MyText
                                numberOfLines={1}
                                text={`${element}`}
                                style={{
                                    color: Colors.DARK_BLUE_40
                                }}
                            />
                        </View>
                    );
                })}
            </ScrollView>
        );
    } else {
        return (
            <MyText
                style={{ textAlign: 'left', color: Colors.GRAYF9 }}
                text={props.placeholder}
                numberOfLines={1}
            />
        );
    }
};
export const RenderPlaceholderStore = (props) => {
    if (props?.storeSelected && props.storeSelected.length > 0) {
        if (props.storeSelected?.length === 1) {
            return (
                <MyText
                    text={props?.storeSelected[0].name || ''}
                    numberOfLines={1}
                    style={{ textAlign: 'left', fontSize: 14 }}
                />
            );
        } else {
            return (
                <props.xworkData.component.GradientText
                    style={{ textAlign: 'left', fontSize: 14 }}
                    colors={[
                        'rgba(97, 110, 124, 1)',
                        'rgba(97, 110, 124, 0.95)',
                        'rgba(97, 110, 124, 0.7)',
                        'rgba(97, 110, 124, 0.2)'
                    ]}>
                    {props?.storeSelected[0] && props?.storeSelected[0].name
                        ? props?.storeSelected[0].name +
                          ` & ${
                              props.storeSelected.length - 1
                          } ${props.xworkData
                              .translate('supermarket')
                              .toLowerCase()}...`
                        : ''}
                </props.xworkData.component.GradientText>
            );
        }
    } else {
        return (
            <MyText
                style={{
                    textAlign: 'left',
                    fontSize: 14,
                    color: Colors.GRAYF9
                }}
                text={translate('choose_supermarket')}
                numberOfLines={1}
            />
        );
    }
};

export const RenderPlaceholderGroup = (props) => {
    if (props?.storeSelected && props.storeSelected.length > 0) {
        if (props.storeSelected?.length === 1) {
            return (
                <MyText
                    text={props?.storeSelected[0].name || ''}
                    numberOfLines={1}
                    style={{ textAlign: 'left', fontSize: 14 }}
                />
            );
        } else {
            return (
                <props.xworkData.component.GradientText
                    style={{ textAlign: 'left', fontSize: 14 }}
                    colors={[
                        'rgba(97, 110, 124, 1)',
                        'rgba(97, 110, 124, 0.95)',
                        'rgba(97, 110, 124, 0.7)',
                        'rgba(97, 110, 124, 0.2)'
                    ]}>
                    {props?.storeSelected[0] && props?.storeSelected[0].name
                        ? props?.storeSelected[0].name +
                          ` & ${
                              props.storeSelected.length - 1
                          } ${props.xworkData
                              .translate('group')
                              .toLowerCase()}...`
                        : ''}
                </props.xworkData.component.GradientText>
            );
        }
    } else {
        return (
            <MyText
                style={{
                    textAlign: 'left',
                    fontSize: 14,
                    color: Colors.GRAYF9
                }}
                text={translate('select_group')}
                numberOfLines={1}
            />
        );
    }
};

export const RenderPlaceholderStatus = (props) => {
    if (props.selectedStatus && props.selectedStatus.length > 0) {
        let data = props.listStatusTicket.find(
            (ele) => ele.id === props.selectedStatus[0]
        );

        if (props.selectedStatus?.length === 1) {
            return (
                <MyText
                    text={data && data.name ? data.name : ''}
                    numberOfLines={1}
                    style={{ textAlign: 'left', fontSize: 14 }}
                />
            );
        } else {
            return (
                <props.xworkData.component.GradientText
                    style={{ textAlign: 'left', fontSize: 14 }}
                    colors={[
                        'rgba(97, 110, 124, 1)',
                        'rgba(97, 110, 124, 0.95)',
                        'rgba(97, 110, 124, 0.7)',
                        'rgba(97, 110, 124, 0.2)'
                    ]}>
                    {data && data.name
                        ? data.name +
                          ` & ${
                              props.selectedStatus.length - 1
                          } ${props.xworkData
                              .translate('status')
                              .toLowerCase()}...`
                        : ''}
                </props.xworkData.component.GradientText>
            );
        }
    } else {
        return (
            <MyText
                style={{
                    textAlign: 'left',
                    fontSize: 14,
                    color: Colors.GRAYF9
                }}
                text={translate('job_status')}
                numberOfLines={1}
            />
        );
    }
};

export const RenderStatus = (props) => {
    return (
        <>
            <MyText
                text={props.titleHeader}
                numberOfLines={1}
                addSize={2}
                typeFont="medium"
                style={{
                    marginVertical: 4,
                    color: Colors.BLACK
                }}
            />
            <MultiSelectComponent
                style={styles.viewButtonDropDown}
                keyboardAvoiding={false}
                isKeyboardShow={false}
                selectedTextProps={{
                    numberOfLines: 1
                }}
                visibleSelectedItem={false}
                // iconStyle={styles.iconStyle}
                search={false}
                data={props.data}
                labelField="name"
                valueField="id"
                placeholder=" "
                value={props.value}
                maxHeight={Mixins.scale(140)}
                renderPlaceholder={props.renderPlaceholder}
                onChange={props.onChange}
                renderRightIcon={() => {
                    return (
                        <AntDesign
                            name="down"
                            size={16}
                            color={Colors.GRAYF9}
                            style={{
                                marginRight: Mixins.scale(4)
                            }}
                        />
                    );
                }}
                renderItem={(item, selected) => {
                    return (
                        <View
                            style={{
                                padding: 17,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                            }}>
                            <View
                                style={{
                                    height: 20,
                                    width: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderWidth: 1.5,
                                    borderColor: selected
                                        ? Colors.BLUE_EB
                                        : Colors.GRAY_BODERRADIUS,
                                    borderRadius: 4,
                                    backgroundColor: selected
                                        ? Colors.BLUE_EB
                                        : Colors.WHITE,
                                    marginRight: 8
                                }}>
                                {selected && (
                                    <Image
                                        source={{
                                            uri: 'ic_check_white'
                                        }}
                                        style={{
                                            height: 16,
                                            width: 13,
                                            tintColor: Colors.WHITE
                                        }}
                                        resizeMode="contain"
                                    />
                                )}
                            </View>
                            <MyText
                                style={{
                                    flex: 1,
                                    fontSize: 14,
                                    color: selected
                                        ? Colors.BLUE_EB
                                        : Colors.BLACK
                                }}
                                text={item.name}
                            />
                        </View>
                    );
                }}
                itemTextStyle={{
                    fontSize: 14,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    backgroundColor: Colors.WHITE,
                    borderRadius: Mixins.scale(12)
                }}
                showsVerticalScrollIndicator={false}
            />
        </>
    );
};
export const RenderStore = (props) => {
    return (
        <>
            <MyText
                text={props.titleHeader}
                numberOfLines={1}
                addSize={2}
                typeFont="medium"
                style={{
                    marginVertical: 4,
                    color: Colors.BLACK
                }}
            />
            <TouchableOpacity
                onPress={() => {
                    props.onPress();
                }}
                style={styles.viewButtonDropDown}>
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }}>
                    {props.renderPlaceholder()}
                    <AntDesign
                        name="down"
                        color={Colors.GRAYF9}
                        size={16}
                        style={{
                            marginRight: Mixins.scale(4)
                        }}
                    />
                </View>
            </TouchableOpacity>
        </>
    );
};

export const RenderTime = (props) => {
    const { endDate, startDate, onPressShowModal, hideTitle } = props;
    return (
        <>
            {!hideTitle && (
                <MyText
                    text={translate('date')}
                    numberOfLines={1}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        marginVertical: 4,
                        color: Colors.BLACK
                    }}
                />
            )}
            <View style={styles.btnTime}>
                <TouchableOpacity
                    activeOpacity={1}
                    style={styles.btnDate}
                    onPress={() => {
                        onPressShowModal('StartDate');
                    }}>
                    <MyText
                        numberOfLines={1}
                        text={
                            endDate !== ''
                                ? `${moment(startDate).format('DD/MM/YYYY')}`
                                : `${translate('start_date')}`
                        }
                        addSize={1}
                        style={{
                            color: endDate !== '' ? Colors.BLACK : Colors.GRAYF9
                        }}
                    />
                </TouchableOpacity>
                <MyText
                    numberOfLines={1}
                    text={'-'}
                    addSize={1}
                    style={{
                        marginHorizontal: Mixins.scale(8),
                        color: endDate !== '' ? Colors.BLACK : Colors.GRAYF9
                    }}
                />
                <TouchableOpacity
                    activeOpacity={1}
                    style={styles.btnDate}
                    onPress={() => {
                        onPressShowModal('EndDate');
                    }}>
                    <MyText
                        numberOfLines={1}
                        text={
                            endDate !== ''
                                ? `${moment(
                                      !endDate ? startDate : endDate
                                  ).format('DD/MM/YYYY')}`
                                : `${translate('end_date')}`
                        }
                        addSize={1}
                        style={{
                            color: endDate !== '' ? Colors.BLACK : Colors.GRAYF9
                        }}
                    />
                </TouchableOpacity>
            </View>
        </>
    );
};
const styles = StyleSheet.create({
    btnDate: {
        alignItems: 'center',
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(16),
        borderWidth: 1,
        flex: 1,
        height: Mixins.scale(44),
        justifyContent: 'center'
    },
    btnTime: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(44),
        justifyContent: 'space-between',
        marginVertical: Mixins.scale(16)
    },
    membersSelected: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_10,
        borderColor: Colors.DARK_BLUE_10,
        borderRadius: 12,
        borderWidth: 0.5,
        height: Mixins.scale(35),
        justifyContent: 'center',
        marginRight: Mixins.scale(4),
        width: Mixins.scale(100)
    },
    viewButtonDropDown: {
        backgroundColor: Colors.WHITE,
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        height: Mixins.scale(44),
        justifyContent: 'center',
        marginVertical: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        width: '100%'
    }
});
