import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AntDesign from 'react-native-vector-icons/AntDesign';
import React, { Component } from 'react';
import {
    View,
    Image,
    TouchableOpacity,
    StyleSheet,
    Keyboard,
    Dimensions
} from 'react-native';
const { translate } = global.props.getTranslateConfig();
class ModalListStore extends Component {
    constructor(props) {
        super(props);
        this.state = {
            txtSearch: '',
            storeSelected: [] || this.props.storeSelected,
            listUser: [],
            selectedRole: null,
            userSelectRecently: null,
            keyBoardShow: false,
            heightKeyboard: 0,
            heightSheet: '70%'
        };
        this.isDone = false;
        this.bottomSheetModalRef = React.createRef(null);
        this.timeOut = -1;
    }

    componentDidMount() {
        this.keyboardDidShowListener = Keyboard.addListener(
            'keyboardDidShow',
            this.keyboardDidShow
        );
        this.keyboardDidHideListener = Keyboard.addListener(
            'keyboardDidHide',
            this.keyboardDidHide
        );
        if (this.props.storeSelected) {
            this.setState({
                storeSelected: this.props.storeSelected
            });
        }

        if (this.props.isVisible) {
            this.openModal();
        }
    }
    async componentDidUpdate(prevProps, prevState) {
        if (this.props.storeSelected !== prevProps.storeSelected) {
            this.setState({
                storeSelected: this.props.storeSelected || []
            });
        }

        if (
            this.props.isVisible !== prevProps.isVisible &&
            this.props.isVisible
        ) {
            this.openModal();
        }
        if (
            this.state.storeSelected !== prevState.storeSelected &&
            this.state.storeSelected &&
            this.state.storeSelected.length > 0
        ) {
            if (this.state.keyBoardShow) {
                this.setState({ heightSheet: '90%' });
            } else {
                this.setState({ heightSheet: '70%' });
            }
        } else if (
            this.state.storeSelected !== prevState.storeSelected &&
            this.state.storeSelected &&
            this.state.storeSelected.length === 0
        ) {
            this.setState({ heightSheet: '70%' });
        }
    }

    keyboardDidHide = () => {
        if (this.state.storeSelected && this.state.storeSelected.length > 0) {
            this.setState({ heightSheet: '70%' });
        } else {
            this.setState({ heightSheet: '70%' });
        }
        this.setState({ keyBoardShow: false });
    };
    keyboardDidShow = () => {
        if (this.state.storeSelected && this.state.storeSelected.length > 0) {
            this.setState({ heightSheet: '90%' });
        } else {
            this.setState({ heightSheet: '80%' });
        }
        this.setState({
            keyBoardShow: true
        });
    };

    componentWillUnmount() {
        this.keyboardDidShowListener?.remove();
        this.keyboardDidHideListener?.remove();
    }

    renderSearch = () => {
        const { xworkData } = this.props;
        return (
            <View style={styles.input}>
                <Icon
                    size={20}
                    name="search"
                    style={{ marginHorizontal: Mixins.scale(5) }}
                />
                <xworkData.BottomSheetGorhom.BottomSheetTextInput
                    defaultValue={this.state.txtSearch}
                    onChangeText={this.searchUser}
                    style={{ flex: 1 }}
                    placeholder={translate('search')}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />
                {this.state.txtSearch && this.state.txtSearch?.length > 0 && (
                    <TouchableOpacity
                        hitSlop={{
                            top: 5,
                            right: 5,
                            left: 5,
                            bottom: 5
                        }}
                        style={{ height: 20, width: 20, marginRight: 12 }}
                        onPress={() => {
                            this.clearText();
                        }}>
                        <Image
                            resizeMode="contain"
                            source={{ uri: 'ic_error_ticket' }}
                            style={{
                                height: Mixins.scale(20),
                                width: Mixins.scale(20)
                            }}
                        />
                    </TouchableOpacity>
                )}
            </View>
        );
    };
    clearText = (text = '') => {
        const { handleSearchStore } = this.props;
        this.setState({ txtSearch: text });
        handleSearchStore(text);
    };
    handleLoadMore = async () => {
        const { handleSearchStore } = this.props;
        await handleSearchStore(this.state.txtSearch, false);
        this.setState({ listStore: this.props.listStore });
    };
    searchUser = (text) => {
        const { handleSearchStore } = this.props;
        this.setState(
            {
                txtSearch: text
            },
            async () => {
                if (
                    this.timeOut !== undefined &&
                    this.timeOut !== null &&
                    this.timeOut >= 0
                ) {
                    clearTimeout(this.timeOut);
                }
                this.timeOut = setTimeout(async () => {
                    if (text?.length > 0) {
                        await handleSearchStore(text);
                        this.setState({ listStore: this.props.listStore });
                    } else {
                        this.clearText('');
                    }
                }, 200);
            }
        );
    };
    clearState = () => {
        this.setState({
            storeSelected: []
        });
    };

    handlePushUser = (item) => {
        const exist = this.state.storeSelected?.findIndex(
            (element) => element?.id === item?.id
        );
        const { storeSelected } = this.state;
        if (exist === -1) {
            let newDataUser = [...storeSelected, item];

            this.setState({
                storeSelected: newDataUser
            });
        } else {
            let newData = this.state.storeSelected.filter(
                (element) => item?.id !== element?.id
            );
            this.setState({
                storeSelected: newData
            });
        }
    };
    renderListUserSearch = () => {
        const { xworkData, listStore, isChooseGroup } = this.props;
        const { GestureHandler } = xworkData;

        return (
            <GestureHandler.FlatList
                contentContainerStyle={{ paddingBottom: 70 }}
                style={{ height: Mixins.scale(350) }}
                keyboardShouldPersistTaps="handled"
                data={listStore}
                onEndReached={this.handleLoadMore}
                showsVerticalScrollIndicator={false}
                scrollEventThrottle={10}
                onEndReachedThreshold={0.1}
                bounces
                renderItem={({ item }) => {
                    const exist = this.state.storeSelected?.findIndex(
                        (element) => element?.id === item?.id
                    );

                    let checkItem = exist !== -1;
                    return (
                        <TouchableOpacity
                            key={item.id}
                            onPress={() => {
                                this.handlePushUser(item);
                            }}
                            style={styles.viewTouch}>
                            <View
                                style={{
                                    height: 20,
                                    width: 20,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    borderWidth: 1.5,
                                    borderColor: checkItem
                                        ? Colors.BLUE_EB
                                        : Colors.GRAY_BODERRADIUS,
                                    borderRadius: 4,
                                    backgroundColor: checkItem
                                        ? Colors.BLUE_EB
                                        : Colors.WHITE,
                                    marginRight: 8
                                }}>
                                {checkItem && (
                                    <Image
                                        source={{
                                            uri: 'ic_check_white'
                                        }}
                                        style={{
                                            height: 16,
                                            width: 13,
                                            tintColor: Colors.WHITE
                                        }}
                                        resizeMode="contain"
                                    />
                                )}
                            </View>
                            <MyText
                                style={{
                                    flex: 1,
                                    color: Colors.BLACK_HEADER_TITLE
                                }}
                                addSize={2}
                                text={`${
                                    isChooseGroup
                                        ? item.name
                                        : item.code + ' - ' + item.name
                                }`}
                                numberOfLines={1}
                            />
                        </TouchableOpacity>
                    );
                }}
            />
        );
    };
    openModal = () => {
        this.bottomSheetModalRef?.current?.present();
        this.isDone = false;
    };
    render() {
        const { onPressDimiss, titleModal, handleAddStore, xworkData } =
            this.props;
        const deviceHeight = Dimensions.get('window').height;
        const { GestureHandler, BottomSheetGorhom } = xworkData;

        return (
            <BottomSheetGorhom.BottomSheetModal
                ref={(ref) => (this.bottomSheetModalRef.current = ref)}
                index={0}
                snapPoints={[this.state.heightSheet]}
                onChange={(index) => {
                    if (index === -1) {
                        this.clearState();
                        onPressDimiss();
                        if (this.props.listStore?.length === 0) {
                            this.clearText();
                        }
                    }
                }}
                keyboardBlurBehavior="restore"
                backdropComponent={(props) => (
                    <BottomSheetGorhom.BottomSheetBackdrop
                        disappearsOnIndex={-1}
                        appearsOnIndex={0}
                        opacity={0.5}
                        onPress={() => {
                            this.clearState();

                            onPressDimiss();
                            if (this.props.listStore?.length === 0) {
                                this.clearText();
                            }
                        }}
                        {...props}
                    />
                )}
                handleComponent={() => (
                    <View style={styles.closeLineContainer}>
                        <View style={styles.closeLine} />
                    </View>
                )}
                android_keyboardInputMode="adjustResize"
                enablePanDownToClose={true}>
                <GestureHandler.NativeViewGestureHandler
                    disallowInterruption={true}>
                    <View
                        style={{
                            flex: 1,
                            paddingBottom: 20
                        }}>
                        <TouchableOpacity
                            style={{
                                paddingHorizontal: 16,
                                marginTop: 12,
                                flex: 1
                            }}
                            hitSlop={{
                                bottom:
                                    global.props.insets.bottom +
                                    deviceHeight * 0.5
                            }}
                            activeOpacity={1}
                            onPress={() => {
                                Keyboard.dismiss();
                                this.bottomSheetModalRef.current?.snapToIndex(
                                    0
                                );
                            }}>
                            <xworkData.BottomSheetGorhom.BottomSheetView
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    height: Mixins.scale(40),
                                    justifyContent: 'space-between'
                                }}>
                                <TouchableOpacity
                                    hitSlop={{
                                        top: 5,
                                        left: 5,
                                        bottom: 5,
                                        right: 5
                                    }}
                                    onPress={() => {
                                        this.bottomSheetModalRef.current?.close();
                                        onPressDimiss();
                                        this.clearState();
                                        this.isDone = true;
                                    }}>
                                    <AntDesign
                                        name="close"
                                        size={20}
                                        style={{
                                            marginRight: Mixins.scale(16)
                                        }}
                                    />
                                </TouchableOpacity>
                                <MyText
                                    text={titleModal}
                                    numberOfLines={1}
                                    addSize={2}
                                    style={{ flex: 1 }}
                                />
                                {/* {this.state.storeSelected?.length > 0 && ( */}
                                <TouchableOpacity
                                    onPress={() => {
                                        this.bottomSheetModalRef.current?.close();
                                        onPressDimiss();
                                        handleAddStore(
                                            this.state.storeSelected
                                        );
                                    }}
                                    style={{
                                        height: Mixins.scale(32),
                                        width: Mixins.scale(60),
                                        borderRadius: Mixins.scale(12),
                                        backgroundColor: Colors.DARK_BLUE_60,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <MyText
                                        text={translate('done')}
                                        style={{ color: Colors.WHITE }}
                                        numberOfLines={1}
                                        addSize={1}
                                    />
                                </TouchableOpacity>
                                {/* )} */}
                            </xworkData.BottomSheetGorhom.BottomSheetView>
                            <View
                                style={{
                                    height: 1,
                                    backgroundColor: Colors.WHITE_E4E7EB,
                                    width: '100%',
                                    marginVertical: Mixins.scale(12)
                                }}
                            />

                            {this.renderSearch()}
                            {this.renderListUserSearch()}
                        </TouchableOpacity>
                    </View>
                </GestureHandler.NativeViewGestureHandler>
            </BottomSheetGorhom.BottomSheetModal>
        );
    }
}

export { ModalListStore };
const styles = StyleSheet.create({
    closeLine: {
        backgroundColor: Colors.GREY_NEUTRALS_6,
        borderRadius: 100,
        height: 4,
        width: 32
    },
    closeLineContainer: {
        alignSelf: 'center',
        paddingBottom: Mixins.scale(4),
        paddingTop: Mixins.scale(12)
    },

    input: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(12),
        flexDirection: 'row',
        height: Mixins.scale(56),
        zIndex: 100
    },

    viewTouch: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(40)
    }
});
