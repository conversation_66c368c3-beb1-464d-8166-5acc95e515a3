const { translate } = global.props.getTranslateConfig();
import { XworkColor as Colors } from '@mwg-sdk/styles';

export const formatMoney = (
    amount,
    decimalCount = 0,
    decimal = '.',
    thousands = '.',
    currencyStr = 'đ'
) => {
    try {
        amount = amount.replace(/[,.]/g, '');
        decimalCount = Math.abs(decimalCount);
        decimalCount = Number.isNaN(decimalCount) ? 2 : decimalCount;

        const negativeSign = amount < 0 ? '-' : '';

        const i = parseInt(
            (amount = Math.abs(Number(amount) || 0)?.toFixed(decimalCount))
        ).toString();
        const j = i.length > 3 ? i.length % 3 : 0;

        return (
            negativeSign +
            (j ? i.substr(0, j) + thousands : '') +
            i.substr(j).replace(/(\d{3})(?=\d)/g, `$1${thousands}`) +
            (decimalCount
                ? decimal +
                  Math.abs(amount - i)
                      ?.toFixed(decimalCount)
                      ?.slice(2)
                : '') +
            currencyStr
        );
    } catch (e) {
        return 0;
    }
};
export const checkFileMime = (item) => {
    if (item.filemime?.includes('pdf')) {
        return 'ic_file_pdf';
    } else {
        return 'ic_file_doc';
    }
};
export const converToDate = (dataTime) => {
    const timeConvert = new Date(dataTime);
    const date = timeConvert.getDate();
    const month = timeConvert.getMonth() + 1;
    const hours = timeConvert.getHours();
    const minute = timeConvert.getMinutes();
    const seconds = timeConvert.getSeconds();
    return `${date < 10 ? `0${date}` : date}/${
        month < 10 ? `0${month}` : month
    }/${timeConvert.getFullYear()} ${hours < 10 ? `0${hours}` : hours}:${
        minute < 10 ? `0${minute}` : minute
    }`;
};
export const textRemoveComment = (item) => {
    switch (item?.contentType) {
        case 'text':
            return `${translate('comment_delete_at')} ${item?.updateTime}`;
        case 'image':
            return `${translate('image_delete_at')} ${item?.updateTime}`;
        case 'video':
            return `${translate('video_delete_at')} ${item?.updateTime}`;
        default:
            return `${translate('comment_delete_at')} ${item?.updateTime}`;
    }
};
export const defaultTextPriority = (id) => {
    switch (id) {
        case 1:
            return Colors.GRAYF7;
        case 2:
            return Colors.DARK_BLUE_40;
        case 3:
            return Colors.DARK_YELLOW_30;
        case 4:
            return Colors.DARK_RED_40;

        default:
            return Colors.GRAYF7;
    }
};
export function strToDate(dtStr) {
    if (!dtStr) return null;
    let dateParts = dtStr.split('-');
    let timeParts = dateParts[2].split(' ')[1].split(':');
    dateParts[2] = dateParts[2].split(' ')[0];
    // month is 0-based, that's why we need dataParts[1] - 1
    return new Date(
        Date.UTC(
            +dateParts[2],
            dateParts[1] - 1,
            +dateParts[0],
            timeParts[0],
            timeParts[1],
            timeParts[2]
        )
    );
}

export const formatRelativeTime = (dateString) => {
    const [datePart, timePart] = dateString.split(' ');
    const [day, month, year] = datePart.split('-');
    const [hours, minutes, seconds] = timePart.split(':');

    const date = new Date(year, month - 1, day, hours, minutes, seconds);
    const now = new Date();
    const diff = Math.floor((now - date) / 1000); // difference in seconds

    // Vừa xong (dưới 1 phút)
    if (diff < 60) {
        return 'Vừa xong';
    }

    // Less than 60 minutes
    const minutesDiff = Math.floor(diff / 60);
    if (minutesDiff < 60) {
        return `${minutesDiff} phút trước`;
    }

    // Less than 24 hours
    const hoursDiff = Math.floor(diff / 3600);
    if (hoursDiff < 24) {
        return `${hoursDiff} giờ trước`;
    }

    // Yesterday
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.getDate() === yesterday.getDate()) {
        return `${String(date.getHours()).padStart(2, '0')}:${String(
            date.getMinutes()
        ).padStart(2, '0')} hôm qua`;
    }

    // Older dates
    return `${String(date.getHours()).padStart(2, '0')}:${String(
        date.getMinutes()
    ).padStart(2, '0')}, ${String(date.getDate()).padStart(2, '0')}/${String(
        date.getMonth() + 1
    ).padStart(2, '0')}`;
};
