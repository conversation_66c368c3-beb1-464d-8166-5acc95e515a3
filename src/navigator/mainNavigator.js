import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import Tickets from '../Tickets';
import CreateGroupXticket from '../Tickets/GroupTicket/CreateGroupXticket';
import DetailGroup from '../Tickets/GroupTicket/DetailGroup';
import EditGroupXticket from '../Tickets/GroupTicket/EditGroupXticket';
import CreateTicket from '../Tickets/Ticket/CreateTicket';
import DetailTicket from '../Tickets/Ticket/Details';
import EditTicket from '../Tickets/Ticket/EditTicket';
import EditSchedule from '../Tickets/Ticket/Details/GroundTask/EditSchedule';
import TicketList from '../Tickets/Ticket/TicketList';
import * as actionTicketCreator from '../Tickets/action';
import TicketActivityHistory from '../Tickets/Ticket/TicketActivityHistory';
import NewFeedIndex from '../Tickets/NewFeedIndex';
import CreateSuggestComment from '../Tickets/Ticket/Details/CreateSuggestComment';
import EditSuggestComment from '../Tickets/Ticket/Details/EditSuggestComment';
import CreateSchedule from '../Tickets/Ticket/CreateSchedule';
import CreateGroundTask from '../Tickets/Ticket/Details/GroundTask/CreateGroundTask';
import DetailGroundTask from '../Tickets/Ticket/Details/GroundTask/DetailGroundTask';
import ChooseLocation from '../Tickets/Ticket/Details/GroundTask/ChooseLocation';
import CallCenterStack from './CallCenter';

class MainAppNavigator extends Component {
    constructor(props) {
        super(props);
    }

    async componentDidMount() {
        this.actionAdd();
    }

    actionAdd = async () => {
        const {
            constants,
            stylesXwork,
            screenName,
            component,
            common,
            profile,
            global,
            fullProfile,
            imageXwork,
            TooltipXticket,
            BottomSheetGorhom,
            translate,
            GestureHandler,
            currentLang,
            DraggableFlatList,
            ScaleDecorator,
            DateTimePickerModal,
            imageAssets,
            Icon
        } = this.props;
        const data = {
            translate,
            constants,
            stylesXwork,
            profile,
            screenName,
            common,
            component,
            global,
            fullProfile,
            imageXwork,
            TooltipXticket,
            BottomSheetGorhom,
            GestureHandler,
            currentLang,
            DraggableFlatList,
            ScaleDecorator,
            DateTimePickerModal,
            imageAssets,
            Icon
        };
        await this.props.actionTicket.getDataFromXwork(data);
    };

    render() {
        const { XticketStack, screenName, common, fullProfile } = this.props;
        const { ProgressBar } = common;
        return (
            <XticketStack.Navigator
                screenOptions={{
                    headerShown: false
                }}
                initialRouteName={screenName}>
                <XticketStack.Screen name="NewFeed" component={NewFeedIndex} />
                <XticketStack.Screen name="Xticket" component={Tickets} />
                <XticketStack.Screen
                    name="CreateGroupXticket"
                    component={CreateGroupXticket}
                />
                <XticketStack.Screen
                    name="DetailGroup"
                    component={DetailGroup}
                />
                <XticketStack.Screen name="TicketList" component={TicketList} />
                <XticketStack.Screen
                    name="CreateTicket"
                    component={CreateTicket}
                />
                <XticketStack.Screen
                    name="CreateSchedule"
                    component={CreateSchedule}
                />
                <XticketStack.Screen
                    name="DetailTicket"
                    component={DetailTicket}
                />
                <XticketStack.Screen name="EditTicket" component={EditTicket} />
                <XticketStack.Screen
                    name="EditSchedule"
                    component={EditSchedule}
                />
                <XticketStack.Screen
                    name="CreateGroundTask"
                    component={CreateGroundTask}
                />
                <XticketStack.Screen
                    name="DetailGroundTask"
                    component={DetailGroundTask}
                />
                <XticketStack.Screen
                    name="ChooseLocation"
                    component={ChooseLocation}
                />
                <XticketStack.Screen
                    name="EditGroupXticket"
                    component={EditGroupXticket}
                />
                <XticketStack.Screen
                    name="TicketActivityHistory"
                    component={TicketActivityHistory}
                />
                <XticketStack.Screen
                    name="CreateSuggestComment"
                    component={CreateSuggestComment}
                />
                <XticketStack.Screen
                    name="EditSuggestComment"
                    component={EditSuggestComment}
                />
                <XticketStack.Screen
                    name="CallCenter"
                    component={CallCenterStack}
                    options={{ gestureEnabled: false }}
                    initialParams={{
                        CallcenterStackNavigator: XticketStack,
                        ProgressBar: ProgressBar,
                        fullProfile: fullProfile
                    }}
                />
            </XticketStack.Navigator>
        );
    }
}

const mapStateToProps = function () {
    return {};
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(actionTicketCreator, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(MainAppNavigator);
