import React, { Component } from 'react';
import { WrapperRepack } from '@mwg-kits/core';

class CallCenterStack extends Component {
    render() {
        const { navigation, route } = this.props;
        const { videoCallData, fullProfile } = route.params;
        const profileState = {
            userProfile: fullProfile
        };

        return (
            <WrapperRepack
                infoModule={{
                    namePlugin: 'CallCenter',
                    container: './VideoCall'
                }}
                name="Gọi video"
                navigation={navigation}
                videoCallData={videoCallData}
                profileState={profileState}
                style={{
                    color: 'rgb(0, 110, 233)'
                }}
                source={require('../assets/images/icon_app.png')}
                icGoBack={require('../assets/images/ic_back.png')}
            />
        );
    }
}

export default CallCenterStack;
