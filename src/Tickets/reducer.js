import * as _action from './action';
import * as _state from './state';

const groupTicketReducer = function (state = _state.ticketState, action) {
    switch (action.type) {
        case 'GET_DATA':
            return {
                ...state,
                xworkData: action.data
            };
        case 'GET_LOCAL':
            return {
                ...state,
                dataLocal: action.data
            };
        case _action.homeAction.START_GET_NEW_FEED:
            return {
                ...state,
                listNewFeed: {
                    ...state.listNewFeed,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_GET_NEW_FEED:
            return {
                ...state,
                listNewFeed: {
                    ...state.listNewFeed,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                }
            };
        case _action.homeAction.START_GET_LIST_GROUP_TICKET:
            return {
                ...state,
                listGroupTicket: {
                    ...state.listGroupTicket,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_GET_LIST__GROUP_TICKET:
            return {
                ...state,
                listGroupTicket: {
                    ...state.listGroupTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.homeAction.START_GET_DETAIL_GROUP_TICKET:
            return {
                ...state,
                detailGroupTicket: {
                    ...state.detailGroupTicket,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_GET_DETAIL_GROUP_TICKET:
            return {
                ...state,
                detailGroupTicket: {
                    ...state.detailGroupTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                }
            };
        case _action.homeAction.START_GET_LIST_MEMBER_GROUP:
            return {
                ...state,
                listMemberGroup: {
                    ...state.listMemberGroup,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_GET_LIST_MEMBER_GROUP:
            return {
                ...state,
                listMemberGroup: {
                    ...state.listMemberGroup,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };

        case _action.homeAction.START_GET_LIST_SERVICE:
            return {
                ...state,
                listService: {
                    ...state.listService,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_GET_LIST_SERVICE:
            return {
                ...state,
                listService: {
                    ...state.listService,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data],
                    dataSearch: [...action.dataSearch]
                }
            };
        case _action.homeAction.START_SEARCH_USER:
            return {
                ...state,
                listUser: {
                    ...state.listUser,
                    isFetching: true
                }
            };
        case _action.homeAction.STOP_SEARCH_USER:
            return {
                ...state,
                listUser: {
                    ...state.listUser,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.homeAction.START_REMOVE_MEMBER:
            return {
                ...state,
                removeMember: {
                    isFetching: true
                }
            };
        case _action.homeAction.STOP_REMOVE_MEMBER:
            return {
                ...state,
                removeMember: {
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? ''
                }
            };
        case _action.homeAction.START_ADD_MEMBER:
            return {
                ...state,
                addMember: {
                    isFetching: true
                }
            };
        case _action.homeAction.STOP_ADD_MEMBER:
            return {
                ...state,
                addMember: {
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? ''
                }
            };
        case _action.homeAction.START_GET_TICKET_IN_FEED:
            return {
                ...state,
                listTicketNewFeed: {
                    isFetching: true
                }
            };
        case _action.homeAction.GET_TICKET_IN_FEED_SUCCESS:
            return {
                ...state,
                listTicketNewFeed: {
                    data: action.data,
                    isFetching: false,
                    isSuccess: true,
                    isError: false
                }
            };
        case _action.homeAction.LOADED_TICKET:
            return {
                ...state,
                listTicketNewFeed: {
                    data: action.data,
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    isLoadMore: action.isLoadMore
                }
            };
        case _action.homeAction.FEED_FULL:
            return {
                ...state,
                listTicketNewFeed: {
                    isFeedFull: true,
                    data: action.data,
                    isFetching: false,
                    isSuccess: true,
                    isError: false,
                    isLoadMore: action.isLoadMore
                }
            };
        case _action.homeAction.GET_TICKET_IN_FEED_FAIL:
            return {
                ...state,
                listTicketNewFeed: {
                    data: [],
                    msgError: action.stringError,
                    isFetching: false,
                    isSuccess: false,
                    isError: true
                }
            };
        case _action.homeAction.START_LIST_ROLE:
            return {
                ...state,
                listRole: {
                    isFetching: true
                }
            };
        case _action.homeAction.STOP_LIST_ROLE:
            return {
                ...state,
                listRole: {
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.homeAction.START_SET_PRIORITY:
            return {
                ...state,
                setPriority: {
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.homeAction.STOP_SET_PRIORITY:
            return {
                ...state,
                setPriority: {
                    isFetching: false,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? ''
                }
            };
        case _action.homeAction.SAVE_GROUP_ID:
            return {
                ...state,
                listMemberGroup: {
                    ...state.listMemberGroup,
                    groupId: action.id
                }
            };
        default:
            return state;
    }
};

export { groupTicketReducer };
