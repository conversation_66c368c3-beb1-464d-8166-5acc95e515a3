export const ticketState = {
    xworkData: {
        screenName: 'Xticket'
    },
    dataLocal: {
        isRefresh: false,
        isFilter: false
    },
    setPriority: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: ''
    },
    dataSerivce: null,
    listGroupTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    detailGroupTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: {}
    },
    listMemberGroup: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: [],
        groupId: ''
    },
    listTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listService: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: [],
        dataSearch: []
    },
    detailTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: {}
    },
    listLocationGeo: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listComment: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    streamToken: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: {}
    },
    listUser: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listPriority: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    removeMember: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    addMember: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listFileTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listActivity: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listTicketNewFeed: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: [],
        isLoadMore: false,
        isFeedFull: false
    },
    slaTicketDetail: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: {}
    },
    listAllStatusTicket: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listRole: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    listTask: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        percentDone: 0,
        msgError: '',
        data: []
    },
    listTemplateTask: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    approveData: {
        isSuccess: false,
        isError: false
    },
    typeListData: {
        data: [],
        msgError: ''
    },
    suggestComment: {
        data: [],
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: ''
    },
    listLocation: {
        data: [],
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        dataLocation: {}
    },
    listNewFeed: {
        isFetching: true,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    selectedTask: {
        files: [],
        data: null
    },
    listHashtag: {
        isFetching: false,
        isSuccess: false,
        isError: false,
        msgError: '',
        data: []
    },
    ratingInfo: null
};
