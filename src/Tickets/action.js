import { METHOD, apiBase } from '@mwg-kits/core';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { helper } from '@mwg-kits/common';
import { CONST_API } from '../constant';
const { translate } = global.props.getTranslateConfig();

export const getDataFromXwork = (data) => {
    return (dispatch) => {
        dispatch({
            type: 'GET_DATA',
            data
        });
    };
};
export const getLocal = (data) => {
    return (dispatch) => {
        dispatch({
            type: 'GET_LOCAL',
            data
        });
    };
};
export const getTicketFeed = (data) => {
    return (dispatch) => {
        dispatch({
            type: 'FEED_FULL',
            data
        });
    };
};
function IsValidateObject(object) {
    return object !== undefined && object !== null;
}
export const hasProperty = (object, property) => {
    return (
        IsValidateObject(object) &&
        Object.hasOwnProperty.call(object, property) &&
        IsValidateObject(object[property])
    );
};
const START_GET_LIST_GROUP_TICKET = 'START_GET_LIST_GROUP_TICKET';
const STOP_GET_LIST__GROUP_TICKET = 'STOP_GET_LIST__GROUP_TICKET';
const START_GET_DETAIL_GROUP_TICKET = 'START_GET_DETAIL_GROUP_TICKET';
const STOP_GET_DETAIL_GROUP_TICKET = 'STOP_GET_DETAIL_GROUP_TICKET';
const START_GET_LIST_MEMBER_GROUP = 'START_GET_LIST_MEMBER_GROUP';
const STOP_GET_LIST_MEMBER_GROUP = 'STOP_GET_LIST_MEMBER_GROUP';
const START_GET_LIST_SERVICE = 'START_GET_LIST_SERVICE';
const STOP_GET_LIST_SERVICE = 'STOP_GET_LIST_SERVICE';
const START_SEARCH_USER = 'START_SEARCH_USER';
const STOP_SEARCH_USER = 'STOP_SEARCH_USER';
const START_REMOVE_MEMBER = 'START_REMOVE_MEMBER';
const STOP_REMOVE_MEMBER = 'STOP_REMOVE_MEMBER';
const START_ADD_MEMBER = 'START_ADD_MEMBER';
const STOP_ADD_MEMBER = 'STOP_ADD_MEMBER';
const START_GET_TICKET_IN_FEED = 'START_GET_TICKET_IN_FEED';
const GET_TICKET_IN_FEED_SUCCESS = 'GET_TICKET_IN_FEED_SUCCESS';
const GET_TICKET_IN_FEED_FAIL = 'GET_TICKET_IN_FEED_FAIL';
const STOP_GET_TICKET_IN_FEED = 'STOP_GET_TICKET_IN_FEED';
const LOADED_TICKET = 'LOADED_TICKET';
const START_LIST_ROLE = 'START_LIST_ROLE';
const STOP_LIST_ROLE = 'STOP_LIST_ROLE';
const FEED_FULL = 'FEED_FULL';
const SEARCH_SERVICE = 'SEARCH_SERVICE';
const START_SET_PRIORITY = 'START_SET_PRIORITY';
const STOP_SET_PRIORITY = 'STOP_SET_PRIORITY';
const START_GET_NEW_FEED = 'START_GET_NEW_FEED';
const STOP_GET_NEW_FEED = 'STOP_GET_NEW_FEED';
const SAVE_GROUP_ID = 'SAVE_GROUP_ID';

export const getDataTicketNewFeed = (data) => {
    return (dispatch) => {
        dispatch({
            type: GET_TICKET_IN_FEED_SUCCESS,
            data
        });
    };
};
export const homeAction = {
    START_LIST_ROLE,
    STOP_LIST_ROLE,
    START_GET_LIST_GROUP_TICKET,
    STOP_GET_LIST__GROUP_TICKET,
    START_GET_DETAIL_GROUP_TICKET,
    STOP_GET_DETAIL_GROUP_TICKET,
    START_GET_LIST_MEMBER_GROUP,
    STOP_GET_LIST_MEMBER_GROUP,
    START_GET_LIST_SERVICE,
    STOP_GET_LIST_SERVICE,
    START_SEARCH_USER,
    STOP_SEARCH_USER,
    START_REMOVE_MEMBER,
    STOP_REMOVE_MEMBER,
    START_ADD_MEMBER,
    STOP_ADD_MEMBER,
    START_GET_TICKET_IN_FEED,
    GET_TICKET_IN_FEED_SUCCESS,
    GET_TICKET_IN_FEED_FAIL,
    STOP_GET_TICKET_IN_FEED,
    LOADED_TICKET,
    FEED_FULL,
    SEARCH_SERVICE,
    START_GET_NEW_FEED,
    STOP_GET_NEW_FEED,
    SAVE_GROUP_ID
};
export const getToken = async (key) => await AsyncStorage.getItem(key);

const start_get_new_feed = () => {
    return {
        type: START_GET_NEW_FEED,
        isFetching: true
    };
};
export const stop_get_new_feed = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_NEW_FEED,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getNewFeed = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_new_feed());
            let body = {
                arrayAssigneeUserId: data?.arrayAssigneeUserId ?? [],
                arrayCreateUserId: data?.arrayCreateUserId ?? [],
                arrayIssueStatusId: data?.arrayIssueStatusId ?? [],
                arrayPrioritiesId: data?.arrayPrioritiesId || [],
                arrayStoreId: data?.arrayStoreId || [],
                arraySupportServiceIds: data?.arraySupportServiceIds ?? [],
                arrayWatcherId: data?.arrayWatcherId ?? [],
                asTemplate: data?.asTemplate ?? false,
                chooseDate: 1,
                pageRequest: {
                    iDisplayStart: data?.iDisplayStart ?? 0,
                    iDisplayLength: data?.iDisplayLength ?? 10,
                    search: data?.search ?? ''
                },
                quickFilterId: -1,
                selectedFilterFromDay: true,
                supportServiceId: data?.supportServiceId ?? -1,
                supportServiceName: data?.supportServiceName ?? null,
                timeFrom:
                    data?.timeFrom ??
                    new Date(new Date().getTime() - 86400000 * 7).getTime(),
                timeTo: data?.timeTo ?? new Date().getTime()
            };
            const response = await apiBase(
                CONST_API.API_NEW_FEED,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true,
                    _timeout: 30000
                }
            );
            console.log(
                'getNewFeedgetNewFeed2',
                JSON.stringify(body, null, 2),
                CONST_API.API_NEW_FEED
            );
            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.IsValidateObject(response?.object) &&
                !response?.error &&
                helper.hasProperty(response.object, 'data')
            ) {
                dispatch(
                    stop_get_new_feed({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            }
            console.log(
                'getNewFeedgetNewFeed',
                JSON.stringify(response, null, 2)
            );
        } catch (error) {
            dispatch(
                stop_get_new_feed({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

const start_get_list_group_ticket = () => {
    return {
        type: START_GET_LIST_GROUP_TICKET,
        isFetching: true,
        isLoadMore: false
    };
};
export const stop_get_list_group_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST__GROUP_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getListGroupTicket = (data, isLoadMore = false) => {
    return async (dispatch) => {
        console.log(
            'getListGroupTicketgetListGroupTicket',
            CONST_API.API_GET_MY_GROUP_TICKET,
            await getToken('TOKEN_ACCESS'),
            isLoadMore,
            JSON.stringify(data, null, 2)
        );
        try {
            dispatch(start_get_list_group_ticket(isLoadMore));
            let body = {
                owner: data?.owner,
                pageRequest: {
                    iDisplayStart: data?.iDisplayStart || 0,
                    iDisplayLength: data?.iDisplayLength || 10,
                    search: data?.search || ''
                }
            };
            const response = await apiBase(
                CONST_API.API_GET_MY_GROUP_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                !helper.IsValidateObject(response) ||
                !helper.hasProperty(response, 'object') ||
                !helper.IsValidateObject(response?.object) ||
                response?.error
            ) {
                dispatch(
                    stop_get_list_group_ticket({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }

            dispatch(
                stop_get_list_group_ticket({
                    isSuccess: true,
                    data: response?.object?.data
                })
            );
            console.log(
                'getListGroupTicketgetListGroupTicket',
                JSON.stringify(response.object, null, 2)
            );
        } catch (error) {
            dispatch(
                stop_get_list_group_ticket({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_get_detail_group_ticket = () => {
    return {
        type: START_GET_DETAIL_GROUP_TICKET,
        isFetching: true
    };
};
const stop_get_detail_group_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_DETAIL_GROUP_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getDetailGroupTicket = (supportServiceId, navigation) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_detail_group_ticket());
            let body = {
                supportServiceId: supportServiceId
            };

            const response = await apiBase(
                CONST_API.API_GET_DETAIL_GROUP,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                !helper.IsValidateObject(response) ||
                !helper.hasProperty(response, 'object') ||
                !helper.IsValidateObject(response?.object) ||
                response?.error
            ) {
                dispatch(
                    stop_get_list_group_ticket({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
            dispatch(
                stop_get_detail_group_ticket({
                    isSuccess: true,
                    data: response?.object
                })
            );
        } catch (error) {
            if (helper.hasProperty(error, 'errorReason')) {
                if (error.errorReason.length > 0) {
                    if (error.errorReason === 'Nhóm không tồn tại') {
                        global.props.alert({
                            show: true,
                            title: translate('notification'),
                            message: error.errorReason,
                            confirmText: translate('confirm'),
                            onConfirmPressed: () => {
                                global.props.alert({ show: false });
                                navigation.goBack();
                            }
                        });
                    } else {
                        dispatch(
                            stop_get_detail_group_ticket({
                                isError: true,
                                isSuccess: false,
                                data: [],
                                msgError: error.errorReason
                            })
                        );
                    }
                } else {
                    dispatch(
                        stop_get_detail_group_ticket({
                            isError: true,
                            isSuccess: false,
                            data: [],
                            msgError: translate('something_wrong_server')
                        })
                    );
                }
            }
        }
    };
};

export const createGroupTicket = (data) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let body = data;
                const response = await apiBase(
                    CONST_API.API_CREATE_GROUP_TICKET,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (response?.object && !response.error) {
                    return resolve(response.object);
                }

                if (response.error && response?.errorReason) {
                    throw new Error(response.errorReason);
                }
                throw new Error('Không xác định được dữ liệu hệ thống');
            } catch (error) {
                reject(error);
            }
        });
    };
};

const start_get_list_member_group = () => {
    return {
        type: START_GET_LIST_MEMBER_GROUP,
        isFetching: true
    };
};
export const stop_get_list_member_group = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_MEMBER_GROUP,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListMemberGroup = (data) => {
    return async (dispatch, getState) => {
        // const groupId = getState().groupTicketReducer.listMemberGroup.groupId;

        try {
            dispatch(start_get_list_member_group());
            let body = {
                ...data
            };

            const response = await apiBase(
                CONST_API.API_GET_LIST_MEMBER_GROUP,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                !helper.IsValidateObject(response) ||
                !helper.hasProperty(response, 'object') ||
                !helper.IsValidateObject(response?.object) ||
                response?.error
            ) {
                dispatch(
                    stop_get_list_member_group({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
            dispatch(
                stop_get_list_member_group({
                    isSuccess: true,
                    data: response?.object?.data
                })
            );
            return response.object;
        } catch (error) {
            dispatch(
                stop_get_list_member_group({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
            return error;
        }
    };
};

export const saveGroupId = (id) => {
    return {
        type: SAVE_GROUP_ID,
        id
    };
};

const start_get_list_service = () => {
    return {
        type: START_GET_LIST_SERVICE,
        isFetching: true
    };
};
const stop_get_list_service = ({
    data = [],
    dataSearch = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_SERVICE,
        data,
        isSuccess,
        isFetching,
        dataSearch,
        msgError
    };
};
export const getListService = (isSearch = false, txt = '') => {
    return async (dispatch, getState) => {
        const { dataSearch } = getState().groupTicketReducer.listService;

        try {
            dispatch(start_get_list_service());
            let body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 100
                }
            };
            const response = await apiBase(
                CONST_API.API_GET_LIST_SERVICE_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            //// search local
            if (isSearch && txt?.length > 0) {
                const _removeVietnameseTones =
                    removeVietnameseTones(txt).toLocaleUpperCase();
                let arr = [];
                arr = dataSearch.filter((item) => {
                    return removeVietnameseTones(item?.name)
                        .toLocaleUpperCase()
                        .includes(_removeVietnameseTones);
                });

                return dispatch(
                    stop_get_list_service({
                        isSuccess: true,
                        data: arr,
                        dataSearch: dataSearch
                    })
                );
            } else if (isSearch && txt.length === 0) {
                return dispatch(
                    stop_get_list_service({
                        isSuccess: true,
                        data: dataSearch,
                        dataSearch: dataSearch
                    })
                );
            } else {
                if (
                    !helper.IsValidateObject(response) ||
                    !helper.hasProperty(response, 'object') ||
                    !helper.IsValidateObject(response?.object) ||
                    response?.error
                ) {
                    dispatch(
                        stop_get_list_service({
                            isError: true,
                            isSuccess: false,
                            data: [],
                            msgError: response?.errorReason
                        })
                    );
                }
                dispatch(
                    stop_get_list_service({
                        isSuccess: true,
                        data: response?.object?.data,
                        dataSearch: response?.object?.data
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_service({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

export const deleteGroup = (params) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                const body = params;
                const response = await apiBase(
                    CONST_API.API_UPDATE_GROUP_TICKET,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (!response.error) {
                    resolve(response.object);
                }
                if (response.error) {
                    const msg = { errorReason: response.errorReason };
                    throw new Error(msg);
                }
            } catch (error) {
                let msg = 'Hiện hệ thống đang lỗi vui lòng thử lại sau';
                if (helper.IsValidateObject(error.errorReason)) {
                    msg = error.errorReason;
                }
                reject(new Error(msg));
            }
        });
    };
};
export const updateGroup = (params) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                const body = params;
                const response = await apiBase(
                    CONST_API.API_UPDATE_GROUP_TICKET,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (
                    response.error ||
                    !helper.IsValidateObject(response) ||
                    !helper.hasProperty(response, 'object') ||
                    !helper.IsValidateObject(response?.object)
                ) {
                    const msg = { errorReason: response.errorReason };
                    reject(new Error(msg));
                }
                resolve(response.object);
            } catch (error) {
                let msg = 'Hiện hệ thống đang lỗi vui lòng thử lại sau';
                if (helper.IsValidateObject(error.errorReason)) {
                    msg = error.errorReason;
                }
                reject(new Error(msg));
            }
        });
    };
};

export const removeMemberGroup = (data) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let body = data;
                const response = await apiBase(
                    CONST_API.API_REMOVE_MEMBER_GROUP,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response.object);
                }
            } catch (error) {
                reject(error);
            }
        });
    };
};

const start_add_member = () => {
    return {
        type: START_ADD_MEMBER,
        isFetching: true
    };
};
const stop_add_member = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_ADD_MEMBER,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const addMemberGroup = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_add_member());
            let body = data;

            const response = await apiBase(
                CONST_API.API_ADD_MEMBER_GROUP,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_add_member({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_add_member({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
            return response;
        } catch (error) {
            dispatch(
                stop_add_member({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
            return error;
        }
    };
};

const start_search_user = () => {
    return {
        type: START_SEARCH_USER,
        isFetching: true
    };
};
const stop_search_user = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_SEARCH_USER,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const searchAllUser = (data, handleData = false) => {
    return async (dispatch) => {
        try {
            dispatch(start_search_user());
            let body = data;
            const response = await apiBase(
                handleData
                    ? CONST_API.API_SEARCH_USER_MWG
                    : CONST_API.API_SEARCH_USER,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.IsValidateObject(response?.object) &&
                !response?.error
            ) {
                if (handleData) {
                    let newData = [...response.object.data];
                    newData = response.object.data.map((item) => {
                        return {
                            user: {
                                ...item,
                                id: item.userID,
                                profile: {
                                    id: item.userID,
                                    ...item
                                }
                            }
                        };
                    });

                    dispatch(
                        stop_search_user({
                            isSuccess: true,
                            data: newData
                        })
                    );
                } else {
                    dispatch(
                        stop_search_user({
                            isSuccess: true,
                            data: response?.object?.data
                        })
                    );
                }
            } else {
                dispatch(
                    stop_search_user({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_search_user({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_set_priority = () => {
    return {
        type: START_SET_PRIORITY
    };
};
const stop_set_priority = ({ isSuccess, msgError }) => {
    return {
        type: STOP_SET_PRIORITY,
        isSuccess,
        msgError
    };
};
export const setPriorityGroup = (groupId, isSet) => async (dispatch) => {
    dispatch(start_set_priority());
    return new Promise(async (resolve, reject) => {
        try {
            const response = await apiBase(
                CONST_API.API_SET_PRIORITY,
                METHOD.POST,
                {
                    supportServiceID: groupId,
                    pin: isSet
                },
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (!response.error) {
                resolve(response?.error);
                dispatch(stop_set_priority({ isSuccess: true }));
            } else {
                const msg = { errorReason: response?.errorReason };
                reject(new Error(msg));
                dispatch(
                    stop_set_priority({
                        isSuccess: false,
                        msgError: response.errorReason
                    })
                );
            }
        } catch (error) {
            reject(new Error(error));
            dispatch(
                stop_set_priority({
                    isSuccess: false,
                    msgError: error
                })
            );
        }
    });
};

// API_GROUP_ROLE
const start_list_role = () => {
    return {
        type: START_LIST_ROLE,
        isFetching: true
    };
};
const stop_list_role = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_LIST_ROLE,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const listGroupRole = (params) => {
    return async (dispatch) => {
        dispatch(start_list_role());
        let body = params;
        return new Promise(async (resolve, reject) => {
            try {
                const response = await apiBase(
                    CONST_API.API_GROUP_ROLE,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );
                if (
                    !response.error ||
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object)
                ) {
                    dispatch(
                        stop_list_role({
                            isSuccess: true,
                            data: response?.object?.data
                        })
                    );
                    resolve(response?.object?.data);
                } else {
                    dispatch(
                        stop_list_role({
                            isError: true,
                            isSuccess: false,
                            data: {},
                            msgError: response?.errorReason
                        })
                    );
                    const msg = { errorReason: response.errorReason };
                    reject(new Error(msg));
                }
            } catch (error) {
                let msg = 'Hiện hệ thống đang lỗi vui lòng thử lại sau';
                if (helper.IsValidateObject(error.errorReason)) {
                    msg = error.errorReason;
                }
                dispatch(
                    stop_list_role({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                    })
                );
                reject(new Error(msg));
            }
        });
    };
};
export const changeRoleMember = (params) => {
    return async (dispatch) => {
        let body = params;
        return new Promise(async (resolve, reject) => {
            try {
                const response = await apiBase(
                    CONST_API.API_CHANGE_ROLE,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (
                    !response.error ||
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object)
                ) {
                    resolve(response?.object);
                    dispatch(
                        stop_get_list_member_group({
                            isSuccess: true,
                            data: response?.object
                        })
                    );
                } else {
                    const msg = { errorReason: response.errorReason };
                    reject(new Error(msg));
                }
            } catch (error) {
                let msg = 'Hiện hệ thống đang lỗi vui lòng thử lại sau';
                if (helper.IsValidateObject(error.errorReason)) {
                    msg = error.errorReason;
                }
                reject(new Error(msg));
            }
        });
    };
};

export function removeVietnameseTones(str) {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A');
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E');
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I');
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O');
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U');
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y');
    str = str.replace(/Đ/g, 'D');
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ''); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ''); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, ' ');
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(
        /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
        ' '
    );
    return str;
}
