import {
    View,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    RefreshControl
} from 'react-native';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import * as _actionHome from './action';
import AntDesign from 'react-native-vector-icons/AntDesign';

import { COLOR_LOADING } from '.';
import * as _actionTicket from './Ticket/action';
import { ModalFilterNewFeed } from '../modal';
import Toast from 'react-native-toast-message';
export const COLOR_HEADER = 'rgba(97, 110, 124, 1)';

import { RenderItemList } from './Ticket/Components/ComponentItemTicket';
import { GlobalStore } from 'redux-micro-frontend';
const { translate } = global.props.getTranslateConfig();
class NewFeedIndex extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isRefershData: false,
            isShowSheet: false,
            isShowModalFilter: false,
            isLoadMore: false,
            isLengthUser: 10,
            iDisplayLength: 10,
            showModalBottom: false,
            isFilter: false,
            dataFilter: {
                startDate: new Date(
                    new Date().getTime() - 86400000 * 7
                ).getTime(),

                endDate: new Date().getTime()
            },
            isLoading: false,
            isShowModalResson: false
        };
        this.globalStore = GlobalStore.Get();
    }

    componentDidMount() {
        this.initData();
        this.props.navigation.addListener('blur', () => {
            this.setState({
                isShowModalFilter: false,
                showModalBottom: false
            });
        });
        this.props.navigation.addListener('focus', () => {
            this.initNewFeed();
        });
    }
    initLocationGeo = (txt) => {
        const data = {
            supportServiceId: -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: txt || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
    };
    initNewFeed = () => {
        const { dataLocal } = this.props;
        if (dataLocal && dataLocal?.isRefresh) {
            this.setState({
                dataFilter: {
                    startDate: new Date(
                        new Date().getTime() - 86400000 * 7
                    ).getTime(),

                    endDate: new Date().getTime()
                },
                isLengthUser: 10,
                iDisplayLength: 10,
                showModalBottom: false,
                isFilter: false
            });
            this.props.actionHome.getLocal({ isRefresh: false });
        }
    };
    handleNewFeed = (item) => {
        const { listNewFeed } = this.props;
        let newData = [...listNewFeed.data];

        let index = this.props.listNewFeed.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };

            this.props.actionHome.stop_get_new_feed({
                data: newData
            });
        }
    };

    handleFetchingData = async (refresh = false) => {
        if (refresh) {
            this.setState({
                isLoading: true
            });
        }
        await this.props.actionHome.getNewFeed();
        this.props.actionHome.getLocal({ isFilter: false });
        let dataFilter = {
            startDate: new Date(new Date().getTime() - 86400000 * 7).getTime(),
            endDate: new Date().getTime()
        };
        this.setState({
            dataFilter: dataFilter,
            iDisplayLength: 10,
            isLoading: false,
            isFilter: false
        });
    };

    refreshData = async () => {
        this.setState({
            isRefershData: true,
            iDisplayLength: 10
        });
        if (helper.IsValidateObject(this.state.dataFilter)) {
            await this.handleFilter(this.state.dataFilter, 10);
        }
        setTimeout(() => {
            this.setState({
                isRefershData: false
            });
        }, 1500);
    };
    handleFilter = async (data, lengthList = 10) => {
        // console.log(
        //     'handleFilterTickethandleFilterTicket',
        //     JSON.stringify(data?.arrayCreateUserId, null, 2)
        // );
        const { xworkData } = this.props;
        const { fullProfile } = xworkData;

        let dataSelectedMemberCreate = [];
        let dataSelectedMember = [];
        let dataSelectedFollow = [];
        let arrayStoreId = [];
        let arrayGroupId = [];
        if (data?.isCheckTicketMyCreate) {
            dataSelectedMemberCreate.push(fullProfile?.id);
        }
        if (data?.isCheckTicketMyFollow) {
            dataSelectedFollow.push(fullProfile?.id);
        }
        if (data?.isCheckTicketMyAssign) {
            dataSelectedMember.push(fullProfile?.id);
        }

        if (data?.storeSelected?.length > 0) {
            data?.storeSelected.map((store) => {
                arrayStoreId.push(store.id);
            });
        }

        if (data?.groupSelected?.length > 0) {
            data?.groupSelected.map((store) => {
                arrayGroupId.push(store.id);
            });
        }
        if (data?.arrayAssigneeUserId?.length > 0) {
            data?.arrayAssigneeUserId.map((store) => {
                dataSelectedMember.push(store.user.id);
            });
        }
        if (data?.arrayCreateUserId?.length > 0) {
            data?.arrayCreateUserId.map((store) => {
                dataSelectedMemberCreate.push(store.user.id);
            });
        }
        if (data?.arrayWatcherId?.length > 0) {
            data?.arrayWatcherId.map((store) => {
                dataSelectedFollow.push(store.user.id);
            });
        }
        console.log(
            'handleFilterhandleFilter',
            JSON.stringify(dataSelectedMemberCreate, null, 2)
        );
        const body = {
            supportServiceId: this.state.groupID || -1,
            arrayIssueStatusId: helper.IsValidateObject(data?.selectedStatus)
                ? data?.selectedStatus
                : [],
            arrayCreateUserId: dataSelectedMemberCreate,
            arrayAssigneeUserId: dataSelectedMember,
            arrayWatcherId: dataSelectedFollow,
            arraySupportServiceIds: arrayGroupId,
            arrayStoreId: arrayStoreId || [],
            asTemplate: false,
            timeFrom:
                Date.parse(data.startDate) ||
                new Date(new Date().getTime() - 86400000 * 7).getTime(),
            timeTo: Date.parse(data.endDate) || new Date().getTime(),
            iDisplayStart: 0,
            iDisplayLength: lengthList,
            search: data.textValue || '',
            requestId: -1,
            requestAction: -1,
            lastId: -1
        };
        await this.props.actionHome.getNewFeed(body);
        this.setState({ iDisplayLength: lengthList });
    };

    componentDidUpdate(prevProps) {
        if (
            this.props.xworkData?.constants !== prevProps.xworkData?.constants
        ) {
            this.initData();
        }
    }
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    initData = () => {
        this.props.actionHome.getNewFeed();
        this.props.actionTicket.getAllStatusTicket();
        this.props.actionTicket.getApproveTypeList();
        this.props.actionHome.getListGroupTicket({
            owner: -1,
            iDisplayStart: 0,
            iDisplayLength: 20,
            search: ''
        });
        this.initLocationGeo();
    };

    onLoadMore = () => {
        const { iDisplayLength, isLoadMore, isShowFetching, isRefresh } =
            this.state;
        const { listNewFeed } = this.props;

        if (listNewFeed.data?.length < iDisplayLength) {
            return null;
        } else {
            this.setState(
                {
                    isLoadMore: true
                },
                async () => {
                    if (helper.IsValidateObject(this.state.dataFilter)) {
                        await this.handleFilter(
                            this.state.dataFilter,
                            iDisplayLength + 10
                        );
                        this.setState({
                            isLoadMore: false
                        });
                    }
                }
            );
        }

        if (
            listNewFeed.isFetching ||
            isLoadMore ||
            isRefresh ||
            isShowFetching
        ) {
            return null;
        }
    };
    renderLoadingcontent = () => {
        if (this.state.isLoadMore) {
            return (
                <View
                    style={{
                        alignItems: 'center',
                        marginTop: 10
                    }}>
                    <MyText
                        style={{
                            color: Colors.GRAY_PLACEHODER
                        }}
                        text={translate('loading_more')}
                    />
                </View>
            );
        }
        return null;
    };

    renderListTicket = () => {
        return (
            <FlatList
                showsVerticalScrollIndicator={false}
                data={this.props.listNewFeed.data}
                renderItem={this.renderItem}
                extraData={this.state || this.props}
                refreshControl={
                    <RefreshControl
                        progressViewOffset={0}
                        tintColor={COLOR_LOADING}
                        colors={[COLOR_LOADING]}
                        refreshing={this.state.isRefershData}
                        onRefresh={this.refreshData}
                    />
                }
                L
                scrollEventThrottle={10}
                initialNumToRender={10}
                windowSize={11}
                onEndReached={this.onLoadMore}
                ListFooterComponent={this.renderLoadingcontent()}
                onEndReachedThreshold={0.2}
            />
        );
    };
    onChangeStatus = async (itemStatus, item) => {
        let body = {
            id: item?.ticket.id,
            statusId: itemStatus.id
        };
        if (itemStatus?.isReason) {
            return this.setState({
                itemSelected: item,
                itemStatus: itemStatus,
                isShowModalResson: true
            });
        }

        this.updateStatusTicket(body);
    };
    renderMentionContent = (content) => {
        let contentMention = content;
        let indexContent = contentMention.indexOf('@mentionuser');
        if (indexContent !== -1) {
            contentMention = content?.slice(0, indexContent);
            return contentMention;
        }
        return content;
    };

    renderItem = ({ item }) => {
        const { common } = this.props.xworkData;
        const { typeListData } = this.props;

        return (
            <RenderItemList
                item={item}
                common={common}
                typeListData={typeListData}
                isGroundTask={this.state.isGroundTask}
                navigation={this.props.navigation}
                onChangeStatus={this.onChangeStatus}
            />
        );
    };
    saveDataFilter = (data) => {
        this.setState({ dataFilter: data });
    };
    handleLoadMoreUser = async (text) => {
        const { isLengthUser } = this.state;

        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: isLengthUser + 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
        this.setState({
            isLengthUser: isLengthUser + 10
        });
    };

    handleSearchUser = async (text) => {
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
    };
    onPressBottomAction = (item) => {
        if (item.id === 0) {
            this.props.navigation.navigate('CreateGroupXticket');
        }
        if (item.id === 1) {
            this.props.navigation.navigate('CreateTicket');
        }
        if (item.id === 2) {
            this.setState({
                showModalBottom: false
            });
        }
    };
    updateStatusTicket = async (body) => {
        const response = await this.props.actionTicket.updateStatusTicket(body);
        try {
            if (
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object)
            ) {
                this.handleGlobalState();
                setTimeout(() => {
                    Toast.show({
                        type: 'success',
                        text1: translate('change_status_success')
                    });
                }, 500);
                this.handleNewFeed(response.object);
            } else {
                setTimeout(() => {
                    Toast.show({
                        type: 'error',
                        text1: response ?? translate('change_status_fail')
                    });
                }, 500);
            }
        } catch (error) {
            setTimeout(() => {
                Toast.show({
                    type: 'error',
                    text1: translate('change_status_fail')
                });
            }, 500);
        }
    };
    handleFilterTicket = (data) => {
        this.setState({
            iDisplayLength: 10,
            isFilter: true
        });
        this.props.actionHome.getLocal({ isFilter: true });
        this.handleFilter(data);
    };
    onPressSendReason = async (text) => {
        const { itemSelected, itemStatus } = this.state;
        let body = {
            id: itemSelected?.ticket.id,
            statusId: itemStatus.id,
            reasonStatus: text
        };

        this.updateStatusTicket(body);
    };
    render() {
        const { xworkData, listNewFeed } = this.props;
        const { component, common } = xworkData;
        if (component === undefined) {
            return null;
        }
        const {
            showModalBottom,
            isRefershData,
            isLoadMore,
            isLoading,
            dataFilter,
            isFilter
        } = this.state;
        const { WrapperContainerTicket, BottomAction, ModalReason } = component;

        return (
            <View style={{ flex: 1 }}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={translate('feed')}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    childrenHeader={() => {
                        if (
                            listNewFeed.isError ||
                            (listNewFeed.isFetching &&
                                !isRefershData &&
                                !isLoadMore &&
                                !isLoading)
                        ) {
                            return null;
                        }
                        return (
                            <View
                                style={{
                                    paddingHorizontal: Mixins.scale(16),
                                    marginTop: Mixins.scale(8)
                                }}>
                                <MyText
                                    numberOfLines={1}
                                    text={`${translate('header_newfeed')}:`}
                                    addSize={1}
                                    style={{
                                        color: COLOR_HEADER
                                    }}
                                />
                                <MyText
                                    numberOfLines={1}
                                    text={`${common.helper.getFullDate(
                                        dataFilter?.startDate
                                    )} - ${common.helper.getFullDate(
                                        dataFilter?.endDate
                                    )}`}
                                    addSize={1}
                                    style={{ color: Colors.BLACK }}
                                />
                            </View>
                        );
                    }}
                    onPressBack={() => {
                        this.props.navigation.goBack();
                    }}
                    buttonRightOutsize={() => {
                        this.setState({
                            isShowModalFilter: true
                        });
                    }}
                    imgButtonIn="ic_add_group"
                    buttonRightIn={() => {
                        this.props.navigation.navigate('Xticket');
                    }}
                    actionRetry={this.initData}
                    imgButtonOutSize="ic_filter"
                    isSuccess={!listNewFeed.isError}
                    messageLoading={
                        dataFilter && isFilter
                            ? translate('filter_ticket')
                            : translate('getting_ticket')
                    }
                    messageError={translate('something_wrong_server')}
                    messageEmpty={
                        dataFilter && isFilter
                            ? translate('no_data_found')
                            : translate('no_job_ticket')
                    }
                    isError={listNewFeed.isError}
                    isLoading={
                        listNewFeed.isFetching &&
                        !isRefershData &&
                        !isLoadMore &&
                        !isLoading
                    }
                    isEmpty={listNewFeed?.data?.length === 0}
                    colorTitle>
                    <View
                        style={{
                            flex: 1,
                            paddingHorizontal: 16
                        }}>
                        {this.renderListTicket()}
                    </View>
                </WrapperContainerTicket>

                {showModalBottom ? (
                    <BottomAction
                        dataBottom={dataBottom}
                        navigation={this.props.navigation}
                        isVisible={this.state.showModalBottom}
                        onPressXticket={(item) => {
                            this.onPressBottomAction(item);
                        }}
                        onPressDimiss={() => {
                            this.setState({
                                showModalBottom: false
                            });
                        }}
                    />
                ) : (
                    <View
                        style={[
                            style.btnPlus,
                            {
                                bottom: 67 + global.props.insets.bottom
                            }
                        ]}>
                        <TouchableOpacity
                            onPress={() => {
                                this.setState({
                                    showModalBottom: true
                                });
                            }}
                            style={style.btnBottom}>
                            <AntDesign
                                name="plus"
                                size={24}
                                color={Colors.WHITE}
                            />
                        </TouchableOpacity>
                    </View>
                )}
                {this.state.isShowModalResson && (
                    <ModalReason
                        isVisible={this.state.isShowModalResson}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalResson: false
                            });
                        }}
                        onPressSendReason={this.onPressSendReason}
                    />
                )}
                {this.state.isShowModalFilter && (
                    <ModalFilterNewFeed
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalFilter: false
                            });
                        }}
                        dataFilter={this.state.dataFilter}
                        saveDataFilter={this.saveDataFilter}
                        handleRefreshData={this.handleFetchingData}
                        handleFilter={this.handleFilterTicket}
                        listUser={this.props.listUser.data}
                        handleSearchUser={this.handleSearchUser}
                        listStatusTicket={this.props.listAllStatusTicket.data}
                        isVisible={this.state.isShowModalFilter}
                        handleLoadMoreUser={this.handleLoadMoreUser}
                        navigation={this.props.navigation}
                        xworkData={this.props.xworkData}
                        listLocationGeo={this.props.listLocationGeo}
                        initLocationGeo={this.initLocationGeo}
                        listGroupTicket={this.props.listGroupTicket}
                    />
                )}
            </View>
        );
    }
}

const style = StyleSheet.create({
    btnBottom: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(28),
        height: Mixins.scale(56),
        justifyContent: 'center',
        width: Mixins.scale(56)
    },
    btnPlus: {
        alignItems: 'center',
        position: 'absolute',
        right: Mixins.scale(10)
    }
});

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listTicketNewFeed: state.groupTicketReducer.listTicketNewFeed,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listAllStatusTicket: state.ticketReducer.listAllStatusTicket,
        listUser: state.groupTicketReducer.listUser,
        typeListData: state.ticketReducer.typeListData,
        listNewFeed: state.groupTicketReducer.listNewFeed,
        dataLocal: state.groupTicketReducer.dataLocal,
        listTicket: state.ticketReducer.listTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listGroupTicket: state.groupTicketReducer.listGroupTicket.data
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(NewFeedIndex);
export const dataBottom = [
    {
        backgroundColor: Colors.WHITE,
        id: 0,
        nameItem: translate('create_group'),
        icon: 'ic_add_group',
        onPressScreen: 'CreateGroupXticket'
    },
    {
        backgroundColor: Colors.WHITE,
        id: 1,
        nameItem: translate('create_ticket'),
        icon: 'ic_create_ticket',
        onPressScreen: 'CreateTicket'
    },
    {
        backgroundColor: Colors.DARK_BLUE_60,
        id: 2,
        nameItem: null,
        icon: 'ic_close_cmt',
        navigation: ''
    }
];
