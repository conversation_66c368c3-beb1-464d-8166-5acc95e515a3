import React, { useEffect, useState } from 'react';
import {
    View,
    StyleSheet,
    Dimensions,
    Text,
    TouchableOpacity,
    Platform
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, Tooltip } from '@mwg-kits/components';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Clipboard from '@react-native-clipboard/clipboard';
const { translate } = global.props.getTranslateConfig();

const { width } = Dimensions.get('window');

const ControlledTooltip = (props) => {
    const [open, setOpen] = React.useState(false);
    return (
        <Tooltip
            visible={open}
            onOpen={() => {
                setOpen(true);
            }}
            onClose={() => {
                setOpen(false);
            }}
            {...props}
        />
    );
};

export const TooltipComment = (props) => {
    const { children } = props;
    const [open, setOpen] = React.useState(false);

    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={width * 0.3}
                    visible={open}
                    onOpen={() => {
                        if (props?.dataCopy?.length === 0) {
                            return null;
                        } else {
                            setOpen(true);
                        }
                    }}
                    onClose={() => {
                        setOpen(false);
                    }}
                    containerStyle={{
                        borderRadius: 12
                    }}
                    toggleAction="onLongPress"
                    pointerColor={Colors.BLACK}
                    backgroundColor={Colors.BLACK}
                    height={40}
                    popover={
                        <View style={{ flexDirection: 'row' }}>
                            <TouchableOpacity
                                hitSlop={{
                                    top: 20,
                                    bottom: 20,
                                    right: 20,
                                    left: 20
                                }}
                                onPress={() => {
                                    setOpen(false);
                                    Clipboard.setString(props?.dataCopy || '');
                                }}
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <MyText
                                    addSize={-1}
                                    style={{ color: Colors.WHITE }}
                                    text={translate('copy')}
                                />
                            </TouchableOpacity>
                            {props.allowDelete && (
                                <>
                                    <View
                                        style={{
                                            width: 1,
                                            height: 20,
                                            backgroundColor: Colors.WHITE,
                                            marginLeft: 5
                                        }}
                                    />
                                    <TouchableOpacity
                                        hitSlop={{
                                            top: 20,
                                            bottom: 20,
                                            right: 20,
                                            left: 20
                                        }}
                                        onPress={() => {
                                            setOpen(false);
                                            props.deleteComment();
                                        }}
                                        style={{
                                            justifyContent: 'center',
                                            alignItems: 'center',

                                            paddingLeft: 5
                                        }}>
                                        <MyText
                                            addSize={-1}
                                            style={{ color: Colors.WHITE }}
                                            text={translate('delete')}
                                        />
                                    </TouchableOpacity>
                                </>
                            )}
                        </View>
                    }>
                    {children}
                </ControlledTooltip>
            </View>
        </>
    );
};

export const TooltipCopyPaste = (props) => {
    const { children, onPressPressable } = props;
    const [open, setOpen] = React.useState(false);

    return (
        // <View style={styles.view}>
        <ControlledTooltip
            width={80}
            visible={open}
            onOpen={() => {
                if (props?.dataCopy?.length === 0) {
                    return null;
                } else {
                    setOpen(true);
                }
            }}
            onClose={() => {
                setOpen(false);
            }}
            containerStyle={{
                borderRadius: 12
            }}
            onPressPressable={() => {
                if (helper.IsValidateObject(onPressPressable)) {
                    return onPressPressable();
                } else {
                    return null;
                }
            }}
            toggleAction="onLongPress"
            pointerColor={Colors.BLACK}
            backgroundColor={Colors.BLACK}
            height={38}
            popover={
                <TouchableOpacity
                    hitSlop={{
                        top: 20,
                        bottom: 20,
                        right: 20,
                        left: 20
                    }}
                    onPress={() => {
                        setOpen(false);
                        Clipboard.setString(props?.dataCopy.toString() || '');
                    }}
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>
                    <MyText
                        addSize={-1}
                        style={{ color: Colors.WHITE }}
                        text={translate('copy')}
                    />
                </TouchableOpacity>
            }>
            {children()}
        </ControlledTooltip>
        // </View>
    );
};
////
const TooltipComponent = () => {
    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={width * 0.8}
                    containerStyle={{
                        borderRadius: 12,
                        borderWidth: 1,
                        padding: Mixins.scale(12),
                        borderColor: Colors.GRAYF7
                    }}
                    pointerColor="#9AA5B1"
                    backgroundColor={Colors.WHITE}
                    height={150}
                    popover={
                        <View style={{ justifyContent: 'center', padding: 4 }}>
                            <MyText
                                typeFont="semiBold"
                                style={[styles.txtDefault, { marginTop: 8 }]}
                                text={`${translate('group_creator_right')}: `}
                            />
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.txtDefault}>
                                    {'\u2022'}
                                </Text>
                                <MyText
                                    style={styles.txtDefault}
                                    text={` ${translate('invite_all_member')}`}
                                />
                            </View>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.txtDefault}>
                                    {'\u2022'}
                                </Text>
                                <MyText
                                    style={styles.txtDefault}
                                    text={` ${translate('delete_any_member')} `}
                                />
                            </View>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.txtDefault}>
                                    {'\u2022'}
                                </Text>
                                <MyText
                                    style={styles.txtDefault}
                                    text={` ${translate(
                                        'change_member_right'
                                    )} `}
                                />
                            </View>
                            <View style={{ flexDirection: 'row' }}>
                                <Text style={styles.txtDefault}>
                                    {'\u2022'}
                                </Text>
                                <MyText
                                    style={styles.txtDefault}
                                    text={` ${translate('change_group_info')}`}
                                />
                            </View>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    marginBottom: 8
                                }}>
                                <Text style={styles.txtDefault}>
                                    {'\u2022'}
                                </Text>
                                <MyText
                                    style={styles.txtDefault}
                                    text={` ${translate('tranfer_right')}`}
                                />
                            </View>
                        </View>
                    }>
                    <AntDesign size={16} name={'exclamationcircleo'} />
                </ControlledTooltip>
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    descTooltips: {
        alignItems: 'center',
        height: 40,
        justifyContent: 'center'
    },
    // iconClock: {
    //     height: Mixins.scale(24),
    //     marginLeft: Mixins.scale(16),
    //     marginRight: Mixins.scale(16),
    //     width: Mixins.scale(24)
    // },
    txtDefault: {
        color: Colors.BLACK
    },
    view: {
        display: 'flex'
    }
});

export default TooltipComponent;

export const TooltipFinishDay = ({ children }) => {
    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={width * 0.4}
                    containerStyle={{
                        borderRadius: 12,
                        borderWidth: 1,
                        padding: Mixins.scale(8),
                        borderColor: Colors.GRAYF7
                    }}
                    pointerColor="#9AA5B1"
                    backgroundColor={Colors.WHITE}
                    height={40}
                    popover={
                        <View style={styles.descTooltips}>
                            <MyText
                                style={styles.txtDefault}
                                text={`${translate('finish_day')}`}
                            />
                        </View>
                    }>
                    {children}
                </ControlledTooltip>
            </View>
        </>
    );
};

export const TooltipShow = ({
    children,
    textShow = `'${translate('finish_day')}'`,
    onCheckVisible
}) => {
    const [isOpen, setIsOpen] = useState(false);
    useEffect(() => {
        if (onCheckVisible) {
            onCheckVisible().then((res) => {
                setIsOpen(res);
            });
        }
    }, []);
    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={width * 0.4}
                    visible={isOpen}
                    onOpen={() => {
                        if (isOpen) {
                            setTimeout(() => {
                                setIsOpen(false);
                            }, 1000);
                        } else {
                            setIsOpen(true);
                        }
                    }}
                    onClose={() => {
                        setIsOpen(false);
                    }}
                    toggleAction="onPress"
                    containerStyle={{
                        borderRadius: 12,
                        borderWidth: 1,
                        padding: Mixins.scale(8),
                        borderColor: Colors.GRAYF7
                    }}
                    pointerColor="#9AA5B1"
                    backgroundColor={Colors.WHITE}
                    height={40}
                    popover={
                        <View style={styles.descTooltips}>
                            <MyText style={styles.txtDefault} text={textShow} />
                        </View>
                    }>
                    {children}
                </ControlledTooltip>
            </View>
        </>
    );
};
export const TooltipService = ({ children, text = '' }) => {
    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={text?.length > 20 ? width * 0.65 : width * 0.45}
                    containerStyle={{
                        borderRadius: 12,
                        borderWidth: 1,
                        padding: Mixins.scale(4),
                        borderColor: Colors.GRAYF7
                    }}
                    pointerColor="#9AA5B1"
                    backgroundColor={Colors.WHITE}
                    height={40}
                    popover={
                        <View style={styles.descTooltips}>
                            <MyText
                                style={styles.txtDefault}
                                text={text || ''}
                            />
                        </View>
                    }>
                    {children}
                </ControlledTooltip>
            </View>
        </>
    );
};

export const TooltipCall = ({
    children,
    textShow = `Nhấn vào ảnh hoặc tên để có thể gọi thoại và gọi video`,
    onCheckVisible
}) => {
    const [isOpen, setIsOpen] = useState(false);
    useEffect(() => {
        if (onCheckVisible) {
            onCheckVisible().then((res) => {
                setIsOpen(res);
            });
        }
    }, []);

    return (
        <>
            <View style={styles.view}>
                <ControlledTooltip
                    width={width * 0.65}
                    visible={isOpen}
                    onOpen={() => {
                        if (isOpen) {
                            setTimeout(() => {
                                setIsOpen(false);
                            }, 5000);
                        }
                    }}
                    onClose={() => {
                        setIsOpen(false);
                    }}
                    toggleAction="onPress"
                    containerStyle={{
                        borderRadius: 12,
                        borderWidth: 1,
                        borderColor: Colors.GRAYF7
                        // marginLeft:
                        //     width > 350 && Platform.OS === 'android'
                        //         ? Mixins.scale(-width / 4.5)
                        //         : Mixins.scale(24)
                    }}
                    pointerColor="#9AA5B1"
                    backgroundColor={Colors.WHITE}
                    height={50}
                    popover={
                        <View style={styles.descTooltips}>
                            <MyText
                                style={styles.txtDefault}
                                text={textShow}
                                addSize={-1}
                            />
                        </View>
                    }>
                    {children}
                </ControlledTooltip>
            </View>
        </>
    );
};
