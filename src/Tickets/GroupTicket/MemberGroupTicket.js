import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, DropdownComponent } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    ActivityIndicator,
    FlatList,
    Image,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import Toast, { BaseToast } from 'react-native-toast-message';
import { ThemeXwork } from '@mwg-sdk/styles';
import { GlobalStore } from 'redux-micro-frontend';
import { CONST_API } from '../../constant';
const { translate } = global.props.getTranslateConfig();

export const toastConfig = {
    success: (props) => {
        return props.isVisible ? (
            <View style={style.toastSuccess}>
                <Image
                    source={{ uri: 'ic_success' }}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    style={style.baseToastSuccess}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                    text2Style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                    text2NumberOfLines={2}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_img' }}
                        style={Mixins.scaleImage(20, 20)}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },
    error: (props) => {
        return props.isVisible ? (
            <View
                style={[
                    style.toastSuccess,
                    {
                        height: 'auto',
                        paddingVertical: Mixins.scale(4)
                    }
                ]}>
                <Image
                    source={{ uri: 'ic_check_error' }}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    style={style.baseToastSuccess}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                    text2Style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                    text1NumberOfLines={2}
                    text2NumberOfLines={2}
                />
                <TouchableOpacity
                    onPress={() => {
                        Toast.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_img' }}
                        style={Mixins.scaleImage(20, 20)}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },
    warning: (props) => {
        return props.isVisible ? (
            <View style={style.toastSuccess}>
                <Image
                    source={{ uri: 'ic_check_warning' }}
                    style={Mixins.scaleImage(20, 20)}
                    resizeMode="contain"
                />
                <BaseToast
                    {...props}
                    text1NumberOfLines={3}
                    style={style.baseToastSuccess}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 15,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                />
                <TouchableOpacity
                    onPress={() => {
                        props.hide();
                    }}>
                    <Image
                        source={{ uri: 'ic_close_img' }}
                        style={Mixins.scaleImage(20, 20)}
                        resizeMode="contain"
                    />
                </TouchableOpacity>
            </View>
        ) : null;
    },
    loading: (props) => {
        return props.isVisible ? (
            <View style={style.toastSuccess}>
                <View style={style.viewImage}>
                    <ActivityIndicator size="small" color={'#9FC6FF'} />
                </View>
                <BaseToast
                    {...props}
                    style={style.baseToastSuccess}
                    contentContainerStyle={{
                        paddingLeft: Mixins.scale(6)
                    }}
                    text1Style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: Colors.WHITE
                    }}
                />
            </View>
        ) : null;
    }
};
class MemberGroupTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            editMember: false,
            txtSearch: ''
        };
        this.globalStore = GlobalStore.Get();
    }

    componentDidMount() {}

    componentWillFocus() {}

    componentDidUpdate() {}
    initMember = (text) => {
        const { detailGroupTicket } = this.props;
        const dataMember = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: text
            },
            requestId: '',
            supportServiceId: detailGroupTicket?.data?.id
        };
        this.props.actionHome.getListMemberGroup(dataMember);
    };

    searchUser = (text) => {
        this.setState(
            {
                txtSearch: text
            },
            () => {
                if (
                    this.timeOut !== undefined &&
                    this.timeOut !== null &&
                    this.timeOut >= 0
                ) {
                    clearTimeout(this.timeOut);
                }
                this.timeOut = setTimeout(() => {
                    if (text?.length > 0) {
                        this.initMember(text);
                    } else {
                        this.initMember('');
                    }
                }, 250);
            }
        );
    };

    renderSearchMember = () => {
        return (
            <View style={style.viewSearch}>
                <View style={style.input}>
                    <Image
                        style={style.icon}
                        resizeMode="contain"
                        source={{ uri: 'ic_search_ticket' }}
                    />
                    <TextInput
                        value={this.state.txtSearch}
                        style={style.stl_input}
                        placeholder={translate('search')}
                        onChangeText={(txt) => {
                            this.searchUser(txt);
                        }}
                        placeholderTextColor={Colors.GRAY_PLACEHODER}
                    />
                    {this.state.txtSearch.length > 0 && (
                        <TouchableOpacity
                            hitSlop={{
                                top: 5,
                                right: 5,
                                left: 5,
                                bottom: 5
                            }}
                            style={{ height: 20, width: 20 }}
                            onPress={() => {
                                this.setState({
                                    txtSearch: ''
                                });
                                this.initMember('');
                            }}>
                            <Image
                                resizeMode="contain"
                                source={{ uri: 'ic_error_ticket' }}
                                style={{
                                    height: Mixins.scale(20),
                                    width: Mixins.scale(20)
                                }}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    };

    renderMembers = () => {
        const { listMemberGroup } = this.props;
        const { editMember } = this.state;
        const { profile } = this.props.xworkData;
        const { detailGroupTicket } = this.props;

        const checkProfileCreate =
            profile?.userName === detailGroupTicket.data?.creatorUserName;
        return (
            <View style={{ flex: 1 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16),
                        marginBottom: Mixins.scale(16)
                    }}>
                    <MyText
                        text={`${translate('members')} (${
                            listMemberGroup?.data?.length || 0
                        })`}
                        addSize={2}
                        typeFont="semiBold"
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <TouchableOpacity
                        onPress={() => {
                            this.setState({
                                editMember: !editMember
                            });
                            if (editMember) {
                                Toast.show({
                                    type: 'success',
                                    text1: translate('save_success'),
                                    position: 'bottom'
                                });
                            }
                        }}>
                        {checkProfileCreate && (
                            <MyText
                                text={
                                    this.state.editMember
                                        ? translate('done')
                                        : translate('edit')
                                }
                                addSize={2}
                                style={{ color: Colors.DARK_BLUE_60 }}
                            />
                        )}
                    </TouchableOpacity>
                </View>
                <FlatList
                    showsVerticalScrollIndicator={false}
                    ListEmptyComponent={() => {
                        return (
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    marginTop: Mixins.scale(100)
                                }}>
                                <Image
                                    style={{
                                        height: Mixins.scale(120),
                                        width: Mixins.scale(120)
                                    }}
                                    source={{ uri: 'ic_empty_member' }}
                                />
                                <MyText
                                    text={translate('no_member')}
                                    style={{
                                        color: Colors.BLACK,
                                        marginTop: Mixins.scale(16)
                                    }}
                                />
                            </View>
                        );
                    }}
                    data={this.sortListMember(listMemberGroup?.data)}
                    renderItem={this.renderItem}
                    extraData={this.state || this.props}
                />
            </View>
        );
    };

    removeMember = async (item) => {
        const { profile } = this.props.xworkData;
        const { detailGroupTicket } = this.props;

        if (profile?.userName !== detailGroupTicket.data?.creatorUserName) {
            return global.props.alert({
                title: translate('notify'),
                show: true,
                message: translate('group_owner_delete'),
                confirmText: translate('close'),
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        } else {
            global.props.alert({
                show: true,
                title: translate('delete_member'),
                message: `${translate('ask_delete_member')} @bold:{"${
                    item?.user?.profile?.lastName
                } ${item?.user?.profile?.firstName}"} ${translate(
                    'from_this_group'
                )}`,
                confirmText: translate('delete'),
                confirmButtonTextStyle: {
                    color: ThemeXwork.red.$500
                },
                titleColor: { color: ThemeXwork.red.$500 },
                cancelText: translate('cancel'),
                cancelButtonTextStyle: {
                    color: Colors.GRAYF7
                },
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                    this.confirmRemoveMember(item);
                },
                onCancelPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        }
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_GROUP',
            payload: { getListGroup: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    confirmRemoveMember = async (item) => {
        try {
            const { detailGroupTicket } = this.props;
            const { data } = detailGroupTicket;
            const body = {
                supportServiceId: data.id,
                userId: item.user.id
            };
            const parmas = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: ''
                },
                requestId: '',
                supportServiceId: data?.id
            };
            const responeRemoveMember =
                await this.props.actionHome.removeMemberGroup(body);
            if (responeRemoveMember) {
                this.props.actionHome.getListMemberGroup(parmas);
                this.props.actionHome.getListGroupTicket();
                this.handleGlobalState();
                Toast.show({
                    type: 'success',
                    text1: translate('delete_member_success'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('delete_member_fail'),
                position: 'bottom'
            });
        }
    };

    sortListMember = (listMember) => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;
        let createdMember = null;
        const sortedList = [];
        listMember.forEach((item) => {
            if (item.user?.id === data.creatorId) {
                createdMember = item;
            } else {
                if (item?.role?.name !== 'Thành viên') {
                    sortedList.unshift(item);
                } else {
                    sortedList.push(item);
                }
            }
        });
        if (helper.IsValidateObject(createdMember)) {
            sortedList.unshift(createdMember);
        }
        return sortedList;
    };

    renderItem = ({ item }) => {
        const { editMember } = this.state;
        const { detailGroupTicket, listRole } = this.props;
        const userCreateGroup =
            item?.user?.username === detailGroupTicket.data?.creatorUserName;

        let roleAndUpateCreateTicket = [
            ...listRole.data,
            { name: translate('tranfer_create'), id: 99 }
        ];

        return (
            <View
                style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    height: 50
                }}>
                {editMember &&
                    this.props.listMemberGroup?.data?.length > 1 &&
                    !userCreateGroup && (
                        <TouchableOpacity
                            onPress={() => {
                                this.removeMember(item);
                            }}
                            hitSlop={{ top: 5, bottom: 5, right: 5, left: 5 }}
                            style={{
                                marginRight: Mixins.scale(4)
                            }}>
                            <Image
                                style={{ height: 20, width: 20 }}
                                source={{ uri: 'ic_remove' }}
                            />
                        </TouchableOpacity>
                    )}
                {item?.user?.profile?.image?.length > 0 ||
                helper.IsValidateObject(item?.user.profile.image) ? (
                    <View style={style.imgMember}>
                        <Image
                            style={style.imgMember}
                            source={{
                                uri: `${CONST_API.baseAvatarURI}${item?.user?.profile?.image}`
                            }}
                        />
                        {userCreateGroup && (
                            <View
                                style={{
                                    position: 'absolute',
                                    bottom: -2,
                                    right: 0,
                                    height: Mixins.scale(10),
                                    width: Mixins.scale(10),
                                    borderRadius: Mixins.scale(5),
                                    backgroundColor: Colors.DARK_BLUE_60,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <Image
                                    style={{
                                        height: Mixins.scale(6),
                                        width: Mixins.scale(6)
                                    }}
                                    resizeMode="contain"
                                    source={{
                                        uri: 'ic_home_ticket'
                                    }}
                                />
                            </View>
                        )}
                    </View>
                ) : (
                    <View
                        style={[
                            style.imgMember,
                            {
                                borderWidth: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                borderColor: Colors.DARK_BLUE_60
                            }
                        ]}>
                        <Image
                            style={{
                                height: Mixins.scale(18),
                                width: Mixins.scale(18),
                                tintColor: Colors.DARK_BLUE_60
                            }}
                            source={{
                                uri: 'ic_profile'
                            }}
                        />
                    </View>
                )}

                <MyText
                    numberOfLines={1}
                    text={`${item?.user?.username} - ${item?.user?.profile?.lastName} ${item?.user?.profile?.firstName}`}
                    style={{
                        flex: 1,
                        color: Colors.BLACK
                    }}
                />
                {_actionHome.hasProperty(item.role, 'name') && (
                    <DropdownComponent
                        disable={!this.state.editMember}
                        showsVerticalScrollIndicator={false}
                        search={false}
                        keyboardAvoiding={false}
                        selectedTextStyle={{
                            fontSize: 14,

                            textAlign: 'right',
                            color: editMember
                                ? Colors.DARK_BLUE_60
                                : Colors.GRAYF9
                        }}
                        selectedTextProps={{ numberOfLines: 1 }}
                        placeholder={item?.role?.name}
                        placeholderStyle={{
                            fontSize: 14,
                            textAlign: 'right',
                            color: editMember
                                ? Colors.DARK_BLUE_60
                                : Colors.GRAYF9
                        }}
                        data={
                            userCreateGroup
                                ? listRole?.data
                                : roleAndUpateCreateTicket
                        }
                        style={{
                            backgroundColor: Colors.WHITE,
                            alignItems: 'center',
                            height: Mixins.scale(30),
                            width: Mixins.scale(150)
                        }}
                        maxHeight={Mixins.scale(150)}
                        itemTextStyle={{
                            fontSize: 13,
                            textAlign: 'left',
                            color: editMember
                                ? Colors.DARK_BLUE_60
                                : Colors.GRAYF9
                        }}
                        containerStyle={{
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        renderRightIcon={() => {
                            return null;
                        }}
                        renderItem={(item1) => {
                            return (
                                <View
                                    style={{
                                        height: Mixins.scale(40),
                                        paddingHorizontal: Mixins.scale(8),
                                        justifyContent: 'center'
                                    }}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        addSize={-1}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        onChange={(role) => this.changeRoleMember(role, item)}
                        value={this.state.selectedRole}
                        labelField="name"
                        valueField="id"
                    />
                )}
                {/* <MyText
                    numberOfLines={1}
                    text={`${item?.role?.name}`}
                    addSize={0}
                    style={{
                        color: editMember ? Colors.DARK_BLUE_60 : Colors.GRAYF9
                    }}
                /> */}
            </View>
        );
    };
    changeRoleMember = async (role, member) => {
        const { detailGroupTicket } = this.props;
        const body = {
            supportServiceId: detailGroupTicket?.data?.id,
            changeOwnerUserId: member.user.id,
            joinRoleId: role.id
        };
        if (role.id === 99) {
            const paramsUpdateCreateGroup = {
                supportServiceId: detailGroupTicket?.data?.id,
                changeOwnerUserId: member.user.id,
                action: 'CHANGE_OWNER'
            };
            this.handleUpdateCreateGroup(paramsUpdateCreateGroup);
            return;
        }
        const response = await this.props.actionHome.changeRoleMember(body);

        if (response) {
            this.props.actionHome.getListGroupTicket();
            this.handleGlobalState();
            Toast.show({
                type: 'success',
                text1: translate('edit_success'),
                position: 'bottom'
            });
        } else {
            Toast.show({
                type: 'error',
                text1: translate('edit_fail'),
                position: 'bottom'
            });
        }
    };
    handleUpdateCreateGroup = async (params) => {
        try {
            global.props.showLoader();

            const successRemove = await this.props.actionHome.deleteGroup(
                params
            );
            this.props.retryData();
            this.setState({
                editMember: false
            });
            global.props.hideLoader();

            if (successRemove) {
                this.props.actionHome.getListGroupTicket();
                this.handleGlobalState();
                Toast.show({
                    type: 'success',
                    text1: translate('group_right_tranfer_success'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('group_right_tranfer_failed'),
                position: 'bottom'
            });
        }
    };
    render() {
        return (
            <View
                style={{
                    flex: 1,
                    paddingHorizontal: Mixins.scale(8)
                }}>
                {this.renderSearchMember()}
                {this.renderMembers()}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listRole: state.groupTicketReducer.listRole
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(MemberGroupTicket);
const bg_color = '#9D9D9D';

const style = StyleSheet.create({
    baseToastSuccess: {
        backgroundColor: bg_color,
        borderLeftWidth: 0,
        elevation: 3,
        flex: 1,
        height: Mixins.scale(42),
        shadowColor: Colors.TRANSAPARENT,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0,
        shadowRadius: 0
    },
    icon: {
        height: Mixins.scale(16),
        marginLeft: Mixins.scale(4),
        marginRight: Mixins.scale(12),
        width: Mixins.scale(16)
    },
    imgMember: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(28),
        marginRight: Mixins.scale(8),
        width: Mixins.scale(28)
    },
    input: {
        alignItems: 'center',
        flexDirection: 'row',
        flex: 1,
        height: Mixins.scale(56),
        paddingHorizontal: 10
    },

    stl_input: {
        color: Colors.BLACK,
        flex: 1
    },

    toastSuccess: {
        alignItems: 'center',
        backgroundColor: bg_color,
        borderColor: Colors.GRAYF7,
        borderRadius: Mixins.scale(12),
        borderWidth: 1,
        flexDirection: 'row',
        marginHorizontal: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        paddingVertical: Mixins.scale(4)
    },
    viewImage: {
        alignItems: 'center',
        backgroundColor: bg_color,
        borderBottomLeftRadius: 12,
        borderTopLeftRadius: 12,
        height: Mixins.scale(44),
        justifyContent: 'center',
        width: Mixins.scale(40)
    },

    viewSearch: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF5,
        borderRadius: 12,
        flexDirection: 'row',
        height: Mixins.scale(56),
        marginVertical: Mixins.scale(16),
        width: '100%'
    }
});
