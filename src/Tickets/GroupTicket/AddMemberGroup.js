import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, DropdownComponent } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    FlatList,
    Image,
    StyleSheet,
    View,
    TextInput,
    TouchableOpacity
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import Toast from 'react-native-toast-message';
import { ThemeXwork } from '@mwg-sdk/styles';
import { CONST_API } from '../../constant';
const { translate } = global.props.getTranslateConfig();

class MemberGroupTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            listMemberGroup: [],
            txtSearch: '',
            editMember: false
        };
    }

    componentDidMount() {}

    componentWillFocus() {}

    renderHeaderMember = () => {
        const { memberSelected } = this.props;
        const { editMember } = this.state;

        return (
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: Mixins.scale(16),
                    marginBottom: Mixins.scale(16)
                }}>
                <MyText
                    text={`${translate('members')} (${
                        memberSelected?.length || 0
                    })`}
                    addSize={2}
                    typeFont="semiBold"
                    style={{ flex: 1, color: Colors.BLACK }}
                />
                <TouchableOpacity
                    onPress={() => {
                        if (editMember) {
                            Toast.show({
                                type: 'success',
                                text1: translate('save_success'),
                                position: 'bottom'
                            });
                        }
                        this.setState({
                            editMember: !editMember
                        });
                    }}>
                    <MyText
                        text={
                            this.state.editMember
                                ? translate('finished')
                                : translate('edit')
                        }
                        addSize={2}
                        style={{ color: Colors.DARK_BLUE_60 }}
                    />
                </TouchableOpacity>
            </View>
        );
    };
    retryMember = () => {
        this.setState({
            txtSearch: ''
        });
        this.props.handleRetryMember();
    };

    renderMembers = () => {
        const { memberSelected } = this.props;

        return (
            <FlatList
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={this.renderSearchMember}
                ListEmptyComponent={() => {
                    return (
                        <View
                            style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                marginTop: Mixins.scale(100)
                            }}>
                            <Image
                                style={{
                                    height: Mixins.scale(200),
                                    width: Mixins.scale(267)
                                }}
                                source={{ uri: 'ic_empty_member' }}
                            />
                            <MyText
                                text={translate('no_member')}
                                style={{
                                    color: Colors.BLACK,
                                    marginTop: Mixins.scale(16)
                                }}
                            />
                        </View>
                    );
                }}
                data={this.sortListMember(memberSelected)}
                renderItem={this.renderItem}
                extraData={this.state || this.props}
            />
        );
    };
    onChangeText = (text) => {
        this.setState(
            {
                txtSearch: text
            },
            () => {
                this.props.handleSearch(text);
            }
        );
    };
    renderSearchMember = () => {
        return (
            <View>
                <View style={style.viewSearch}>
                    <View style={style.input}>
                        <Image
                            style={style.icon}
                            resizeMode="contain"
                            source={{ uri: 'ic_search_ticket' }}
                        />
                        <TextInput
                            value={this.state.txtSearch}
                            style={style.stl_input}
                            placeholder={translate('search')}
                            placeholderTextColor={Colors.GRAY_PLACEHODER}
                            onChangeText={this.onChangeText}
                        />
                        {this.state.txtSearch.length > 0 && (
                            <TouchableOpacity
                                hitSlop={{
                                    top: 5,
                                    right: 5,
                                    left: 5,
                                    bottom: 5
                                }}
                                style={{ height: 20, width: 20 }}
                                onPress={() => {
                                    this.retryMember();
                                }}>
                                <Image
                                    resizeMode="contain"
                                    source={{ uri: 'ic_error_ticket' }}
                                    style={{
                                        height: Mixins.scale(20),
                                        width: Mixins.scale(20)
                                    }}
                                />
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
                {this.renderHeaderMember()}
            </View>
        );
    };

    sortListMember = (listMember) => {
        const { fullProfile } = this.props.xworkData;
        let createdMember = null;
        const sortedList = [];
        listMember.forEach((item) => {
            if (item.user?.username === fullProfile.username) {
                createdMember = item;
            } else {
                if (item?.role?.name !== 'Thành viên') {
                    sortedList.unshift(item);
                } else {
                    sortedList.push(item);
                }
            }
        });
        if (helper.IsValidateObject(createdMember)) {
            sortedList.unshift(createdMember);
        }
        return sortedList;
    };
    confirmRemove = (item) => {
        global.props.alert({
            show: true,
            title: translate('delete_member'),
            message: `${translate('ask_delete_member')} @bold:{"${
                item?.user?.lastName
            } ${item?.user?.firstName}"} ${translate('from_this_group')}`,
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: ThemeXwork.red.$500
            },
            titleColor: { color: ThemeXwork.red.$500 },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },
            onConfirmPressed: () => {
                global.props.alert({ show: false });
                this.props.removeMember(item);
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };

    renderItem = ({ item }) => {
        const { memberSelected } = this.props;
        const { fullProfile } = this.props.xworkData;
        const { editMember } = this.state;
        const checkUserCreate = item.user?.username === fullProfile.username;
        // let nameUserCreateGroup = `${item?.user?.username} - ${item?.user?.lastName} ${item?.user?.firstName}`;
        ////API mới
        // let nameUserAdd = `${item?.user?.username} - ${item?.user?.lastName} ${item?.user?.firstName}`;
        let nameUserAdd = `${item?.user?.username} - ${item?.user?.profile?.lastName} ${item?.user?.profile?.firstName}`;
        let imgUserAdd = `${CONST_API.baseAvatarURI}${item?.user?.profile?.image}`;

        return (
            <View
                style={{
                    alignItems: 'center',
                    flexDirection: 'row',
                    height: 50
                }}>
                {editMember &&
                    memberSelected.length > 1 &&
                    !checkUserCreate && (
                        <TouchableOpacity
                            onPress={() => {
                                this.confirmRemove(item);
                            }}
                            hitSlop={{ top: 5, bottom: 5, right: 5, left: 5 }}
                            style={{
                                marginRight: Mixins.scale(8)
                            }}>
                            <Image
                                style={{ height: 20, width: 20 }}
                                source={{ uri: 'ic_remove' }}
                            />
                        </TouchableOpacity>
                    )}
                {item?.user?.profile?.image?.length > 0 ||
                helper.IsValidateObject(item?.user?.profile?.image) ? (
                    <View style={style.imgMember}>
                        <Image
                            style={style.imgMember}
                            source={{
                                uri: imgUserAdd
                            }}
                        />
                        {checkUserCreate && (
                            <View
                                style={{
                                    position: 'absolute',
                                    bottom: -2,
                                    right: 0,
                                    height: Mixins.scale(10),
                                    width: Mixins.scale(10),
                                    borderRadius: Mixins.scale(5),
                                    backgroundColor: Colors.DARK_BLUE_60,
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <Image
                                    style={{
                                        height: Mixins.scale(6),
                                        width: Mixins.scale(6)
                                    }}
                                    resizeMode="contain"
                                    source={{
                                        uri: 'ic_home_ticket'
                                    }}
                                />
                            </View>
                        )}
                    </View>
                ) : (
                    <View
                        style={[
                            style.imgMember,
                            {
                                borderWidth: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                borderColor: Colors.DARK_BLUE_60
                            }
                        ]}>
                        <Image
                            style={{
                                height: Mixins.scale(18),
                                width: Mixins.scale(18),
                                tintColor: Colors.DARK_BLUE_60
                            }}
                            source={{
                                uri: 'ic_profile'
                            }}
                        />
                    </View>
                )}
                <MyText
                    numberOfLines={1}
                    text={nameUserAdd}
                    addSize={1}
                    style={{ flex: 1, color: Colors.BLACK }}
                />
                <DropdownComponent
                    disable={!editMember}
                    showsVerticalScrollIndicator={false}
                    search={false}
                    keyboardAvoiding={false}
                    selectedTextStyle={{
                        fontSize: 14,
                        textAlign: 'right',
                        color: editMember ? Colors.DARK_BLUE_60 : Colors.GRAYF9
                    }}
                    selectedTextProps={{ numberOfLines: 1 }}
                    placeholder={item?.role?.name}
                    placeholderStyle={{
                        fontSize: 14,
                        textAlign: 'right',
                        color: editMember ? Colors.DARK_BLUE_60 : Colors.GRAYF9
                    }}
                    data={this.props.listRole?.data}
                    style={{
                        backgroundColor: Colors.WHITE,
                        alignItems: 'center',
                        height: Mixins.scale(30),
                        width: Mixins.scale(150)
                    }}
                    maxHeight={Mixins.scale(150)}
                    itemTextStyle={{
                        fontSize: 13,
                        textAlign: 'left',
                        color: editMember ? Colors.DARK_BLUE_60 : Colors.GRAYF9
                    }}
                    containerStyle={{
                        backgroundColor: Colors.WHITE,
                        borderRadius: Mixins.scale(12)
                    }}
                    renderRightIcon={() => {
                        return null;
                    }}
                    renderItem={(item1) => {
                        return (
                            <View
                                style={{
                                    height: Mixins.scale(40),
                                    paddingHorizontal: Mixins.scale(8),
                                    justifyContent: 'center'
                                }}>
                                <MyText
                                    numberOfLines={1}
                                    style={{ color: Colors.BLACK }}
                                    addSize={-1}
                                    text={item1.name}
                                />
                            </View>
                        );
                    }}
                    onChange={(role) => this.props.changeRoleMember(role, item)}
                    value={this.state.selectedRole}
                    labelField="name"
                    valueField="id"
                />
            </View>
        );
    };

    render() {
        return (
            <View
                style={{
                    flex: 1,
                    paddingHorizontal: Mixins.scale(16)
                }}>
                {this.renderMembers()}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listRole: state.groupTicketReducer.listRole
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(MemberGroupTicket);
const style = StyleSheet.create({
    icon: {
        height: Mixins.scale(16),
        marginLeft: Mixins.scale(4),
        marginRight: Mixins.scale(12),
        width: Mixins.scale(16)
    },
    imgMember: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(28),
        marginRight: Mixins.scale(8),
        width: Mixins.scale(28)
    },
    input: {
        alignItems: 'center',
        flexDirection: 'row',
        flex: 1,
        height: Mixins.scale(56),
        paddingHorizontal: 10
    },
    stl_input: {
        color: Colors.BLACK,
        flex: 1
    },
    viewSearch: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF5,
        borderRadius: 12,
        flexDirection: 'row',
        height: Mixins.scale(56),
        marginVertical: Mixins.scale(16),
        width: '100%'
    }
});
