import React, { PureComponent } from 'react';
import {
    View,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Image,
    Switch
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import Toast from 'react-native-toast-message';
import TooltipComponent, {
    TooltipCopyPaste,
    TooltipService
} from './CompoentTooltip';
import { CONST_API } from '../../constant';
const { translate } = global.props.getTranslateConfig();

class InformationGroupTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            descpriction: '',
            showModalGroup: false,
            imageBackground: '',
            privacy: false,
            showReadMore: false,
            isVisible: false,
            isShowMoreTitle: false
        };
    }

    componentDidMount() {}

    renderTextDescription = () => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;

        if (
            data?.description?.length > 90 &&
            this.state.showReadMore !== true
        ) {
            return (
                <View style={{ marginTop: Mixins.scale(16) }}>
                    <TooltipCopyPaste
                        dataCopy={data?.description}
                        children={() => {
                            return (
                                <MyText
                                    numberOfLines={2}
                                    addSize={1}
                                    style={{ color: Colors.BLACK }}
                                    text={`${data?.description?.slice(
                                        0,
                                        90
                                    )}... `}
                                />
                            );
                        }}
                    />

                    <TouchableOpacity
                        onPress={() => {
                            this.setState({
                                showReadMore: true
                            });
                        }}
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            zIndex: 99,
                            paddingHorizontal: Mixins.scale(4),
                            backgroundColor: Colors.WHITE
                        }}>
                        <MyText
                            style={{
                                color: Colors.DARK_BLUE_60
                            }}
                            numberOfLines={1}
                            text={translate('more')}
                        />
                    </TouchableOpacity>
                </View>
            );
        }
        if (
            data?.description?.length > 90 &&
            this.state.showReadMore === true
        ) {
            return (
                <View
                    style={{
                        marginTop: Mixins.scale(16),
                        maxHeight: Mixins.scale(100)
                    }}>
                    <ScrollView nestedScrollEnabled={true}>
                        <TooltipCopyPaste
                            dataCopy={data?.description}
                            children={() => {
                                return (
                                    <MyText
                                        addSize={1}
                                        style={{
                                            color: Colors.BLACK
                                        }}
                                        text={data?.description}
                                    />
                                );
                            }}
                        />
                    </ScrollView>
                </View>
            );
        }
        return (
            <View
                style={{
                    flexDirection: 'row',
                    marginTop: Mixins.scale(16)
                }}>
                <TooltipCopyPaste
                    dataCopy={data?.description}
                    children={() => {
                        return (
                            <MyText
                                addSize={1}
                                style={{
                                    color: Colors.BLACK
                                }}
                                text={
                                    helper.IsEmptyString(data?.description)
                                        ? translate('no_detail_descip')
                                        : data?.description
                                }
                            />
                        );
                    }}
                />
            </View>
        );
    };

    componentDidUpdate() {}

    renderTitleGroup = () => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;

        return (
            <View style={{ paddingTop: Mixins.scale(20), flex: 1 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        flex: 1
                    }}>
                    <Image
                        style={style.imgAvatar}
                        resizeMode="stretch"
                        source={{
                            uri: data?.avatar || 'ic_image_create'
                        }}
                    />
                    <View style={{ flex: 1, flexDirection: 'row' }}>
                        <TooltipCopyPaste
                            dataCopy={detailGroupTicket?.data?.name}
                            children={() => {
                                return (
                                    <TouchableOpacity
                                        onPress={() =>
                                            this.setState((prevState) => ({
                                                isShowMoreTitle:
                                                    !prevState.isShowMoreTitle
                                            }))
                                        }>
                                        <MyText
                                            text={detailGroupTicket?.data?.name}
                                            addSize={4}
                                            numberOfLines={
                                                this.state.isShowMoreTitle
                                                    ? 0
                                                    : 1
                                            }
                                            typeFont="medium"
                                            style={{
                                                color: Colors.BLACK
                                            }}
                                        />
                                    </TouchableOpacity>
                                );
                            }}
                        />
                    </View>
                </View>
                <View
                    style={{
                        marginTop: Mixins.scale(16)
                    }}>
                    <MyText
                        text={translate('contribute_description_input')}
                        addSize={2}
                        typeFont="medium"
                        style={{
                            color: Colors.BLACK
                        }}
                    />
                </View>

                {this.renderTextDescription()}
            </View>
        );
    };

    renderGroupCreator = () => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;
        return (
            <View
                style={{
                    marginTop: Mixins.scale(16),
                    paddingBottom: Mixins.scale(16)
                }}>
                <View style={style.viewLine} />
                <MyText
                    text={translate('group_creator')}
                    addSize={2}
                    typeFont="medium"
                    style={{ color: Colors.BLACK, marginTop: Mixins.scale(16) }}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <View
                        style={{
                            height: Mixins.scale(26),
                            width: Mixins.scale(26),
                            borderRadius: Mixins.scale(13),
                            marginRight: Mixins.scale(8)
                        }}>
                        <Image
                            style={{
                                height: Mixins.scale(26),
                                width: Mixins.scale(26),
                                borderRadius: Mixins.scale(13)
                            }}
                            source={{
                                uri: `${CONST_API.baseAvatarURI}${data?.creatorImage}`
                            }}
                        />
                        <View
                            style={{
                                position: 'absolute',
                                bottom: -2,
                                right: 0,
                                height: Mixins.scale(10),
                                width: Mixins.scale(10),
                                borderRadius: Mixins.scale(5),
                                backgroundColor: Colors.DARK_BLUE_60,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <Image
                                style={{
                                    height: Mixins.scale(6),
                                    width: Mixins.scale(6)
                                }}
                                resizeMode="contain"
                                source={{
                                    uri: 'ic_home_ticket'
                                }}
                            />
                        </View>
                    </View>
                    <MyText
                        text={` ${data?.creatorUserName} - ${data?.creatorLastName} ${data?.creatorFirstName}`}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                    <View style={{ marginLeft: 12 }}>
                        <TooltipComponent />
                    </View>
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        flex: 1,
                        marginTop: Mixins.scale(24),
                        marginBottom: Mixins.scale(24)
                    }}>
                    <Image
                        style={style.icPermission}
                        source={{ uri: 'ic_list_view' }}
                    />
                    <View style={{ marginRight: Mixins.scale(8) }}>
                        <MyText
                            style={{
                                color: Colors.BLACK
                            }}
                            numberOfLines={1}
                            text={translate('job_type')}
                            addSize={2}
                            typeFont="medium"
                        />
                    </View>
                    <View
                        style={{
                            flex: 1
                        }}>
                        <TooltipService text={data?.serviceName}>
                            <MyText
                                style={{
                                    textAlign: 'right',
                                    color: Colors.BLACK
                                }}
                                text={data?.serviceName}
                                addSize={1}
                                numberOfLines={1}
                            />
                        </TooltipService>
                    </View>
                </View>
                {/* <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <Image
                        style={style.icPermission}
                        source={{ uri: 'ic_lock1' }}
                    />
                    <MyText
                        text="Quyền riêng tư"
                        addSize={2}
                        typeFont="medium"
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.DARK_BLUE_60
                        }}
                        thumbColor={Colors.WHITE}
                        onValueChange={() => {}}
                        disabled
                        value={data?.isPublic}
                        style={{
                            height: Mixins.scale(24),
                            width: Mixins.scale(44),
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View> */}
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(8)
                    }}>
                    <Image
                        style={style.icPermission}
                        source={{ uri: 'ic_notification' }}
                    />
                    <MyText
                        text={translate('notify')}
                        addSize={2}
                        typeFont="medium"
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(18)
                    }}>
                    <MyText
                        text={translate('notify_no_recipient')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor={Colors.WHITE}
                        disabled
                        value={data?.isNotifyAssignAll}
                        style={{
                            height: Mixins.scale(24),
                            width: Mixins.scale(44),
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(18)
                    }}>
                    <MyText
                        text={translate('notify_adding_member')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor="#f4f3f4"
                        disabled
                        value={data?.isNotifyInviteAll}
                        style={{
                            height: Mixins.scale(24),
                            width: Mixins.scale(44),
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
            </View>
        );
    };

    removeGroup = () => {
        global.props.alert({
            show: true,
            title: translate('delete_group'),
            message: translate('confirm_delete_group'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({ show: false });
                this.handleRemove();
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };

    handleRemove = async () => {
        try {
            const { detailGroupTicket } = this.props;
            global.props.showLoader();

            const data = {
                supportServiceId: detailGroupTicket?.data?.id,
                action: 'DELETE'
            };
            const successRemove = await this.props.actionHome.deleteGroup(data);
            global.props.hideLoader();

            if (successRemove) {
                this.props.actionHome.getListGroupTicket();
                Toast.show({
                    type: 'success',
                    text1: translate('success_delete_group'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.navigation.navigate('Xticket');
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('fail_delete_group'),
                position: 'bottom'
            });
        }
    };
    confirmOutGroup = async () => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;
        const { fullProfile } = this.props.xworkData;

        const body = {
            supportServiceId: data.id,
            userId: fullProfile.id
        };

        const responeRemoveMember =
            await this.props.actionHome.removeMemberGroup(body);

        if (!responeRemoveMember.error) {
            Toast.show({
                type: 'success',
                text1: translate('success_leave_group'),
                position: 'bottom'
            });
            setTimeout(() => {
                Toast.hide();
                this.props.navigation.navigate('Xticket');
            }, 1000);
        } else {
            Toast.show({
                type: 'error',
                text1: translate('fail_leave_group'),
                position: 'bottom'
            });
        }
    };

    handleOutGroup = async () => {
        global.props.alert({
            show: true,
            title: translate('notify'),
            message: translate('confirm_leave_group'),
            confirmText: translate('leave_group'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },
            onConfirmPressed: () => {
                global.props.alert({ show: false });
                this.confirmOutGroup();
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };
    render() {
        const { xworkData } = this.props;
        const { component, fullProfile } = xworkData;
        const { detailGroupTicket, listMemberGroup } = this.props;
        if (detailGroupTicket?.isFetching) {
            return (
                <component.BaseViewWithLoading
                    isLoading
                    messageLoading={translate('getting_data_group')}
                />
            );
        }

        let checkUserCreateGroup =
            fullProfile.username === detailGroupTicket.data?.creatorUserName;

        return (
            <View style={{ flex: 1 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{
                        paddingHorizontal: Mixins.scale(16)
                    }}>
                    {this.renderTitleGroup()}
                    {this.renderGroupCreator()}
                </ScrollView>
                <View>
                    {!checkUserCreateGroup ||
                        (checkUserCreateGroup &&
                            listMemberGroup?.data?.length === 1 && (
                                <View
                                    style={{
                                        backgroundColor: Colors.GRAYF4,
                                        height: 1,
                                        width: '100%'
                                    }}
                                />
                            ))}
                    {checkUserCreateGroup &&
                        listMemberGroup?.data?.length === 1 && (
                            <TouchableOpacity
                                onPress={this.removeGroup}
                                style={style.btnRemoveGroup}>
                                <Image
                                    style={{
                                        height: Mixins.scale(20),
                                        width: Mixins.scale(20),
                                        marginRight: 8,
                                        tintColor: Colors.DARK_RED_30
                                    }}
                                    source={{ uri: 'ic_trash' }}
                                />
                                <MyText
                                    addSize={1}
                                    style={{ color: Colors.DARK_RED_30 }}
                                    text={translate('delete_group')}
                                />
                            </TouchableOpacity>
                        )}
                    {checkUserCreateGroup &&
                        listMemberGroup?.data?.length > 1 && <View></View>}
                    {!checkUserCreateGroup && (
                        <TouchableOpacity
                            onPress={this.handleOutGroup}
                            style={style.btnRemoveGroup}>
                            <Image
                                style={{
                                    height: Mixins.scale(20),
                                    width: Mixins.scale(20),
                                    marginRight: 8,
                                    tintColor: Colors.DARK_RED_30
                                }}
                                source={{ uri: 'ic_out_group' }}
                            />
                            <MyText
                                addSize={1}
                                style={{ color: Colors.DARK_RED_30 }}
                                text={translate('leave_group')}
                            />
                        </TouchableOpacity>
                    )}
                </View>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(
    mapStateToProps,
    mapDispatchToProps
)(InformationGroupTicket);
const style = StyleSheet.create({
    btnRemoveGroup: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(56),
        paddingHorizontal: Mixins.scale(16)
    },
    icPermission: {
        height: Mixins.scale(28),
        marginRight: Mixins.scale(12),
        tintColor: Colors.DARK_BLUE_40,
        width: Mixins.scale(28)
    },
    imgAvatar: {
        borderRadius: Mixins.scale(26),
        height: Mixins.scale(52),
        marginRight: Mixins.scale(16),
        width: Mixins.scale(52)
    },

    viewLine: {
        backgroundColor: Colors.GRAYF4,
        height: 1,
        marginVertical: Mixins.scale(16),
        width: '100%'
    }
});
