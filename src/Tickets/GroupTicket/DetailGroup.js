import React, { PureComponent } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as _actionHome from '../action';
import InformationGroupTicket from './InformationGroupTicket';
import MemberGroupTicket from './MemberGroupTicket';
import Toast from 'react-native-toast-message';
import { GlobalStore } from 'redux-micro-frontend';
const { translate } = global.props.getTranslateConfig();

class DetailGroup extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            indexHeader: 0,
            dataHeader: this.getDataHeader(),
            memberSelected: [],
            showModalUser: false,
            groupRole: [],
            isFetchingData: false,
            isLengthUser: 10
        };
        this.isNotification = false;
        this.globalStore = GlobalStore.Get();
    }

    componentDidMount() {
        this.init();
        this.focusListener = this.props.navigation.addListener('focus', () => {
            this.componentWillFocus();
        });
    }

    componentWillFocus() {
        this.init();
    }
    getDataHeader = () => {
        return [translate('information'), translate('members')];
    };

    init = async () => {
        this.setState({ isFetchingData: true });
        const paramsXticket = await AsyncStorage.getItem('PARAMS_XTICKET');
        if (paramsXticket) {
            this.isNotification = true;
            const parseResult = JSON.parse(paramsXticket);
            await this.props.actionHome.getDetailGroupTicket(
                parseResult?.id,
                this.props.navigation
            );
            if (this.props.detailGroupTicket.isSuccess) {
                AsyncStorage.removeItem('PARAMS_XTICKET');
            }
        } else {
            await this.props.actionHome.getDetailGroupTicket(
                this.props.route?.params?.id
            );
        }
        this.setState({
            isFetchingData: this.props.detailGroupTicket.isFetching
        });
        this.initMemberGroup();
        this.initListRole();
    };

    componentDidUpdate(prevProps) {
        const { xworkData } = this.props;
        if (xworkData?.constants !== prevProps.xworkData?.constants) {
            this.init();
        }
    }
    retryData = () => {
        const { detailGroupTicket } = this.props;
        this.props.actionHome.getDetailGroupTicket(detailGroupTicket?.data?.id);
        this.initMemberGroup();
    };
    initMemberGroup = () => {
        const { detailGroupTicket } = this.props;
        if (detailGroupTicket.isSuccess) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 20,
                    search: ''
                },
                supportServiceId: detailGroupTicket?.data?.id
            };
            this.props.actionHome.getListMemberGroup(body);
        }
    };
    initListRole = () => {
        const { detailGroupTicket } = this.props;
        if (detailGroupTicket.isSuccess) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 99,
                    search: ''
                },
                supportServiceId: detailGroupTicket?.data?.id
            };
            this.props.actionHome.listGroupRole(body);
        }
    };

    renderHeader = () => {
        const { indexHeader, dataHeader } = this.state;
        return (
            <View style={style.viewHeader}>
                {dataHeader.map((item, index) => {
                    const checkIndex = indexHeader === index;
                    return (
                        <TouchableOpacity
                            key={item}
                            onPress={() => {
                                this.setState({
                                    indexHeader: index
                                });
                            }}
                            style={[
                                style.btnHeader,
                                {
                                    borderBottomWidth: checkIndex ? 1 : 0,
                                    borderColor: checkIndex
                                        ? Colors.DARK_BLUE_60
                                        : Colors.GRAYF5
                                }
                            ]}>
                            <MyText
                                style={{
                                    color: checkIndex
                                        ? Colors.DARK_BLUE_60
                                        : Colors.BLACK
                                }}
                                text={item}
                                addSize={2}
                            />
                        </TouchableOpacity>
                    );
                })}
            </View>
        );
    };

    renderTextDescription = () => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;
        const { common } = this.props.xworkData;

        if (data?.description?.length > 80 && !this.state.showReadMore) {
            return (
                <View
                    style={{
                        marginVertical: Mixins.scale(16)
                    }}>
                    <MyText
                        numberOfLines={2}
                        style={{ color: Colors.BLACK }}
                        text={`${data?.description?.slice(0, 80)}... `}
                    />
                    <TouchableOpacity
                        onPress={(prevState) => {
                            this.setState({
                                showReadMore: !prevState.showReadMore
                            });
                        }}
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            right: 0
                        }}>
                        <MyText
                            style={{
                                color: Colors.DARK_BLUE_60
                            }}
                            numberOfLines={1}
                            text={translate('more')}
                        />
                    </TouchableOpacity>
                </View>
            );
        }
        if (data?.description?.length > 80 && this.state.showReadMore) {
            return (
                <View
                    style={{
                        height: Mixins.scale(100),
                        marginVertical: Mixins.scale(16)
                    }}>
                    <ScrollView nestedScrollEnabled={true}>
                        <MyText
                            style={{
                                color: Colors.BLACK
                            }}
                            text={data?.description}
                        />
                    </ScrollView>
                </View>
            );
        }
        return (
            <MyText
                numberOfLines={2}
                style={{ marginTop: Mixins.scale(16), color: Colors.BLACK }}
                text={
                    common.helper.IsEmptyString(data?.description)
                        ? translate('no_detail_descip')
                        : `${data?.description} `
                }
            />
        );
    };

    onPressEditTicket = () => {
        const { detailGroupTicket } = this.props;
        this.props.navigation.navigate(
            'EditGroupXticket',
            detailGroupTicket?.data
        );
    };

    handleSearchUser = async (text) => {
        // this.setState({ textSearch: text });
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
    };

    handleModalUser = () => {
        this.setState({
            showModalUser: true
        });
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_GROUP',
            payload: { getListGroup: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    handleAddMember = async (listMember = []) => {
        const { detailGroupTicket } = this.props;
        const { data } = detailGroupTicket;
        const arrayMember = [];
        try {
            this.setState({
                showModalUser: false
            });

            listMember.forEach((item) => {
                const member = {
                    id: item.user.id,
                    roleId: item.role.id
                };
                arrayMember.push(member);
            });

            const body = {
                supportServiceId: data.id,
                arrayUserInvitation: arrayMember
            };
            const parmas = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: ''
                },
                requestId: '',
                supportServiceId: data?.id
            };
            const responeAddMember = await this.props.actionHome.addMemberGroup(
                body
            );

            this.props.actionHome.getListGroupTicket();
            this.handleGlobalState();
            if (!responeAddMember.error) {
                const responeGetListMember =
                    await this.props.actionHome.getListMemberGroup(parmas);

                if (responeGetListMember) {
                    setTimeout(() => {
                        Toast.show({
                            type: 'success',
                            text1: translate('add_member_success'),
                            position: 'bottom'
                        });
                    }, 500);
                }
            } else {
                Toast.show({
                    type: 'error',
                    text1: translate('add_member_fail'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('add_member_fail'),
                position: 'bottom'
            });
        }
    };
    handleLoadMoreUser = async (text) => {
        const { isLengthUser } = this.state;

        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: isLengthUser + 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
        this.setState({
            isLengthUser: isLengthUser + 10
        });
    };
    actionBack = async () => {
        const groupId = await AsyncStorage.getItem('PARAMS_XTICKET');
        if (helper.IsValidateObject(groupId)) {
            await AsyncStorage.removeItem('PARAMS_XTICKET');
            this.props.navigation.navigate('Home');
        } else if (this.isNotification) {
            this.props.navigation.navigate('Home');
        } else {
            this.props.navigation.goBack();
        }
    };
    render() {
        const { xworkData } = this.props;
        const { component, profile } = xworkData;
        const { indexHeader } = this.state;
        if (component === undefined) {
            return null;
        }
        const { detailGroupTicket } = this.props;
        const { WrapperContainerTicket, ModalAddUser } = component;
        const checkIndex = indexHeader === 0;
        const checkUserCreateGroup =
            profile?.userName === detailGroupTicket?.data?.creatorUserName;

        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('group_detail_title')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={this.actionBack}
                actionRetry={this.init}
                isSuccess={!detailGroupTicket?.isError}
                messageLoading={translate('getting_group_detail')}
                messageError={detailGroupTicket.msgError}
                isError={detailGroupTicket.isError}
                isLoading={this.state.isFetchingData}
                onPressEditTicket={
                    checkIndex
                        ? checkUserCreateGroup
                            ? this.onPressEditTicket
                            : null
                        : null
                }
                buttonRightOutsize={!checkIndex ? this.handleModalUser : null}
                imgButtonOutSize="ic_add_user"
                colorTitle>
                {this.renderHeader()}
                <View
                    style={{
                        flex: 1,
                        marginTop: Mixins.scale(12),
                        paddingHorizontal: 16
                    }}>
                    {this.state.indexHeader === 0 ? (
                        <InformationGroupTicket
                            navigation={this.props.navigation}
                            props={this.props}
                        />
                    ) : (
                        <MemberGroupTicket
                            props={this.props}
                            retryData={this.retryData}
                        />
                    )}
                </View>
                {this.state.showModalUser && (
                    <ModalAddUser
                        isVisible={this.state.showModalUser}
                        titleModal={translate('add_member')}
                        onPressDimiss={() => {
                            this.setState({ showModalUser: false });
                        }}
                        handleLoadMoreUser={this.handleLoadMoreUser}
                        checkUserCreateGroup={checkUserCreateGroup}
                        groupRole={this.props.listRole.data}
                        listMemberGroup={this.props.listMemberGroup?.data}
                        handleSearchUser={this.handleSearchUser}
                        listUser={this.props.listUser.data}
                        memberSelected={this.state.memberSelected}
                        handleAddMember={this.handleAddMember}
                    />
                )}
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listUser: state.groupTicketReducer.listUser,
        listRole: state.groupTicketReducer.listRole
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(DetailGroup);
const style = StyleSheet.create({
    btnHeader: {
        alignItems: 'center',
        flex: 1,
        height: 50,
        justifyContent: 'center'
    },
    viewHeader: {
        alignItems: 'center',
        borderBottomWidth: 1,
        borderColor: Colors.GRAYF5,
        flexDirection: 'row',
        height: 50
    }
});
