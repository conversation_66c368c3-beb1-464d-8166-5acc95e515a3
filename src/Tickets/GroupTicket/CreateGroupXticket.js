import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    Dimensions,
    Image,
    ScrollView,
    StyleSheet,
    Switch,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import AddMemberGroup from './AddMemberGroup';
import Toast from 'react-native-toast-message';
import { stylesTicket } from '../Ticket/stylesTicket';
import { GlobalStore } from 'redux-micro-frontend';
const { translate } = global.props.getTranslateConfig();

class CreateGroupXticket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            descpriction: '',
            imageBackground: [],
            supportServiceIsPublic: true,
            supportServiceIsNotifyAssignAll: true,
            supportServiceIsNotifyInviteAll: true,
            serviceSelected: null,
            indexHeader: 0,
            dataHeader: [translate('information'), translate('members')],
            memberSelected: [],
            dataMember: [],
            showModalUser: false,
            isShowModalListService: false,
            isLengthUser: 10,
            showCreateButton: false,
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        };
        this.globalStore = GlobalStore.Get();
    }

    componentDidMount() {
        this.props.actionHome.getListService();
        this.initListRole();
        const { fullProfile } = this.props.xworkData;

        if (fullProfile) {
            let newDataMember = [
                {
                    user: {
                        id: fullProfile?.id,
                        username: fullProfile?.username,
                        profile: { ...fullProfile }
                    },
                    role: { name: translate('group_administration'), id: 18 }
                }
            ];
            this.setState({
                memberSelected: newDataMember,
                dataMember: newDataMember
            });
        }
    }
    componentDidUpdate(prevProps) {
        const { xworkData } = this.props;
        if (xworkData?.fullProfile !== prevProps.xworkData?.fullProfile) {
            const { fullProfile } = xworkData;
            if (fullProfile) {
                let newDataMember = [
                    {
                        user: {
                            id: fullProfile?.id,
                            username: fullProfile?.username,
                            profile: { ...fullProfile }
                        },
                        role: {
                            name: translate('group_administration'),
                            id: 18
                        }
                    }
                ];
                this.setState({
                    memberSelected: newDataMember,
                    dataMember: newDataMember
                });
            }
            this.props.actionHome.getListService();
            this.initListRole();
        }
    }
    initListRole = () => {
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 99,
                search: ''
            },
            supportServiceId: -1
        };
        this.props.actionHome.listGroupRole(body);
    };
    handleSearch = (txt) => {
        const { xworkData } = this.props;
        const { common } = xworkData;
        const { memberSelected } = this.state;

        const txtSearch = common.helper
            .removeVietnameseTones(txt.trim())
            .toUpperCase();
        let arr = [];
        if (txt.length > 0) {
            arr = memberSelected.filter((ele) => {
                let fullUser = `${ele?.user?.username} ${ele?.user?.lastName} ${ele?.user?.firstName}`;
                return (
                    common.helper
                        .removeVietnameseTones(fullUser)
                        .toUpperCase()
                        .search(txtSearch) !== -1
                );
            });
        } else {
            arr = [...memberSelected];
        }
        this.setState({
            dataMember: arr
        });
    };
    renderHeader = () => {
        const { serviceSelected } = this.state;
        const checkService = helper.IsValidateObject(serviceSelected);
        return (
            <View style={{ paddingTop: Mixins.scale(20) }}>
                <MyText
                    text={`${translate('group_name')} `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>

                <TextInput
                    value={this.state.title}
                    onChangeText={(text) => {
                        this.setState({
                            title: text
                        });
                    }}
                    style={stylesTicket.inputTitle}
                    placeholder={`${translate('enter_group_name')} `}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />

                <MyText
                    text={`${translate('job_type')} `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>

                <TouchableOpacity
                    onPress={() => {
                        this.setState({
                            isShowModalListService: true
                        });
                    }}
                    style={{
                        height: Mixins.scale(56),
                        borderRadius: Mixins.scale(16),
                        borderWidth: 0.5,
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingHorizontal: 16,
                        borderColor: checkService
                            ? Colors.DARK_BLUE_60
                            : Colors.DARK_ORANGE_15,
                        marginTop: Mixins.scale(16)
                    }}>
                    <MyText
                        numberOfLines={1}
                        text={
                            checkService
                                ? serviceSelected?.name
                                : translate('select_job_type')
                        }
                        addSize={1}
                        typeFont={'medium'}
                        style={{
                            color: checkService
                                ? Colors.BLACK_HEADER_TITLE
                                : Colors.GRAYF9
                        }}
                    />
                    <AntDesign name="down" size={15} color={Colors.GRAYF9} />
                </TouchableOpacity>

                <MyText
                    text={`${translate('detail_descrip')} `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}
                />
                <TextInput
                    onChangeText={(text) => {
                        this.setState({
                            descpriction: text
                        });
                    }}
                    value={this.state.descpriction}
                    multiline
                    textAlignVertical="top"
                    style={stylesTicket.txtDescription}
                    placeholder={`${translate('enter_content')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />
            </View>
        );
    };

    onShowPicker = async () => {
        try {
            await ImagePicker.openPicker({
                maxFiles: 1,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                mediaType: 'photo'
            }).then((image) => {
                this.setState({
                    imageBackground: [image]
                });
            });
        } catch (e) {
            console.log(e, '12312312321');
        }
    };

    renderImage = () => {
        const { imageBackground } = this.state;
        return (
            <View
                style={{
                    paddingTop: Mixins.scale(32)
                }}>
                <MyText
                    style={{
                        color: Colors.GRAYF10
                    }}
                    text={translate('contribute_image_selection')}
                    addSize={2}
                    typeFont="medium"
                />
                <TouchableOpacity
                    onLongPress={() => {
                        if (imageBackground?.length > 0) {
                            this.setState({
                                viewMedia: {
                                    index: 0,
                                    visible: true,
                                    uris: this.state.imageBackground
                                }
                            });
                        } else {
                            return null;
                        }
                    }}
                    onPress={() => {
                        this.onShowPicker();
                    }}
                    style={style.btnImage}>
                    {imageBackground?.length === 0 ? (
                        <Image
                            style={style.icImage}
                            source={{ uri: 'ic_image' }}
                        />
                    ) : (
                        <View style={style.imgSelect}>
                            <TouchableOpacity
                                onPress={() => {
                                    this.setState({
                                        imageBackground: []
                                    });
                                }}
                                hitSlop={{
                                    top: 10,
                                    bottom: 10,
                                    right: 10,
                                    left: 10
                                }}
                                style={{
                                    position: 'absolute',
                                    top: -5,
                                    right: -5,
                                    height: 15,
                                    width: 15,
                                    zIndex: 10
                                }}>
                                <Image
                                    style={{ height: 20, width: 20 }}
                                    source={{
                                        uri: 'ic_error_ticket'
                                    }}
                                />
                            </TouchableOpacity>
                            <Image
                                resizeMode="cover"
                                style={style.imgSelect}
                                source={{
                                    uri: this.state.imageBackground[0]?.path
                                }}
                            />
                        </View>
                    )}
                </TouchableOpacity>
            </View>
        );
    };

    sendMedias = (medias) => {
        const { constants } = this.props.xworkData;
        return new Promise(async (resolve) => {
            try {
                const responeImage = await constants._UploadFile(medias);
                resolve(responeImage.images[0]);
            } catch (error) {
                resolve([]);
            }
        });
    };
    renderHeaderTab = () => {
        const { indexHeader, dataHeader, title, serviceSelected } = this.state;
        const checkService = helper.IsValidateObject(serviceSelected);
        const checkShowButton = title?.length === 0 || !checkService;
        return (
            <View style={style.viewHeader}>
                {dataHeader.map((item, index) => {
                    const checkIndex = indexHeader === index;
                    return (
                        <TouchableOpacity
                            key={item}
                            onPress={() => {
                                if (!checkShowButton && index === 1) {
                                    this.setState({
                                        showCreateButton: true
                                    });
                                }
                                this.setState({
                                    indexHeader: index
                                });
                            }}
                            style={[
                                style.btnHeader,
                                {
                                    borderBottomWidth: checkIndex ? 1 : 0.5,
                                    borderColor: checkIndex
                                        ? Colors.DARK_BLUE_60
                                        : Colors.GRAYF5
                                }
                            ]}>
                            <MyText
                                style={{
                                    color: checkIndex
                                        ? Colors.DARK_BLUE_60
                                        : Colors.BLACK
                                }}
                                text={item}
                                addSize={2}
                            />
                        </TouchableOpacity>
                    );
                })}
            </View>
        );
    };
    renderGroupCreator = () => {
        const {
            supportServiceIsNotifyAssignAll,
            supportServiceIsNotifyInviteAll
        } = this.state;
        return (
            <View style={{ marginTop: 16, paddingBottom: 16 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <Image
                        style={style.icPermission}
                        source={{ uri: 'ic_notification' }}
                    />

                    <MyText
                        text={translate('notify')}
                        addSize={2}
                        typeFont="medium"
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <MyText
                        text={translate('notify_no_recipient')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor={Colors.WHITE}
                        ios_backgroundColor={
                            this.state.supportServiceIsNotifyAssignAll
                                ? Colors.WHITE
                                : Colors.DARK_ORANGE_15
                        }
                        onValueChange={() => {
                            this.setState({
                                supportServiceIsNotifyAssignAll:
                                    !supportServiceIsNotifyAssignAll
                            });
                        }}
                        value={supportServiceIsNotifyAssignAll}
                        style={{
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(18)
                    }}>
                    <MyText
                        text={translate('notify_adding_member')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor={Colors.WHITE}
                        ios_backgroundColor={
                            this.state.supportServiceIsNotifyInviteAll
                                ? Colors.WHITE
                                : Colors.DARK_ORANGE_15
                        }
                        onValueChange={() => {
                            this.setState({
                                supportServiceIsNotifyInviteAll:
                                    !supportServiceIsNotifyInviteAll
                            });
                        }}
                        value={this.state.supportServiceIsNotifyInviteAll}
                        style={{
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
            </View>
        );
    };

    handleButtonSave = async () => {
        try {
            const { fullProfile } = this.props.xworkData;
            const {
                title,
                descpriction,
                supportServiceIsPublic,
                supportServiceIsNotifyAssignAll,
                supportServiceIsNotifyInviteAll,
                imageBackground
            } = this.state;

            const checkService = helper.IsValidateObject(
                this.state.serviceSelected
            );
            if (!checkService) {
                return global.props.alert({
                    show: true,
                    title: translate('notify'),
                    message: `${translate('alert_select_job')}`,
                    type: 'info',
                    confirmText: translate('close'),
                    onConfirmPressed: () => {
                        global.props.alert({ show: false });
                    }
                });
            }
            global.props.showLoader();
            ////// lấy avatar group
            let avatar = '';
            if (imageBackground?.length > 0) {
                avatar = await this.sendMedias(imageBackground);
            }
            ////// lấy quyền user
            let memberCreate = this.state.memberSelected.find(
                (element) => element.user.username === fullProfile.username
            );
            /// filter member khác với user tạo
            let dataListMember = [];
            dataListMember = this.state.memberSelected.filter(
                (element) => element.user.username !== fullProfile.username
            );
            const data = {
                supportServiceId: -1,
                nameSupportService: title,
                serviceId: this.state.serviceSelected?.id,
                joinRoleId: memberCreate.role.id,
                supportServiceIsPublic,
                supportServiceIsNotifyAssignAll,
                supportServiceIsNotifyInviteAll,
                supportServiceDescription: descpriction,
                avatar
            };

            const response = await this.props.actionHome.createGroupTicket(
                data
            );

            if (response) {
                this.props.actionHome.getListGroupTicket();
                let action = {
                    type: 'GET_LIST_GROUP',
                    payload: { getListGroup: true }
                };
                this.globalStore.DispatchAction('XworkStore', action);

                if (!helper.IsEmptyArray(dataListMember)) {
                    this.addMemberGroup(response.id, dataListMember);
                } else {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('create_group_success'),
                        position: 'bottom'
                    });
                    setTimeout(() => {
                        Toast.hide();

                        this.props.navigation.goBack();
                    }, 1000);
                }
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('create_group_fail'),
                position: 'bottom'
            });
            setTimeout(() => {
                Toast.hide();
            }, 1000);
        }
    };
    addMemberGroup = async (idGroup, dataListMember) => {
        try {
            const arrayMember = [];
            dataListMember.forEach((item) => {
                const member = {
                    id: item.user.id,
                    roleId: item.role.id
                };
                arrayMember.push(member);
            });
            const body = {
                supportServiceId: idGroup,
                arrayUserInvitation: arrayMember
            };
            const responeAddMember = await this.props.actionHome.addMemberGroup(
                body
            );
            global.props.hideLoader();
            if (!responeAddMember.error) {
                global.props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: translate('create_group_success'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.navigation.goBack();
                }, 1000);
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('adding_group_member_fail'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.navigation.goBack();
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('adding_group_member_fail'),
                position: 'bottom'
            });
            setTimeout(() => {
                Toast.hide();
                this.props.navigation.goBack();
            }, 1000);
        }
    };
    renderButtonSave = () => {
        const { title, serviceSelected, showCreateButton } = this.state;
        const checkService = helper.IsValidateObject(serviceSelected);
        const checkShowButton = title?.length === 0 || !checkService;

        if (!checkShowButton && !showCreateButton) {
            return (
                <TouchableOpacity
                    onPress={() => {
                        if (!showCreateButton) {
                            this.setState({
                                indexHeader: 1
                            });
                        }
                        this.setState({
                            showCreateButton: true
                        });
                    }}
                    style={style.btnSave}>
                    <MyText
                        text={translate('next')}
                        addSize={3}
                        typeFont="semiBold"
                        style={{ color: Colors.WHITE }}
                    />
                </TouchableOpacity>
            );
        }
        return (
            <TouchableOpacity
                onPress={() => {
                    this.handleButtonSave();
                }}
                style={[
                    style.btnSave,
                    {
                        backgroundColor: checkShowButton
                            ? Colors.GRAYF6
                            : Colors.DARK_BLUE_60
                    }
                ]}>
                <MyText
                    text={translate('next')}
                    addSize={3}
                    typeFont="semiBold"
                    style={{ color: Colors.WHITE }}
                />
            </TouchableOpacity>
        );
    };
    handleSearchUser = async (text) => {
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
    };
    handleModalUser = () => {
        this.setState({
            showModalUser: true
        });
    };
    handleLoadMoreUser = async (text) => {
        const { isLengthUser } = this.state;

        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: isLengthUser + 10,
                search: text
            }
        };
        await this.props.actionHome.searchAllUser(body);
        this.setState({
            isLengthUser: isLengthUser + 10
        });
    };
    handleAddMember = (listMember = []) => {
        let dataMember = [...this.state.memberSelected, ...listMember];
        this.setState({
            memberSelected: dataMember,
            dataMember: dataMember,
            showModalUser: false
        });
    };
    changeRoleMember = (roleSelect, userSelect) => {
        const { memberSelected } = this.state;
        const index = memberSelected?.findIndex(
            (ele) => ele.user.username === userSelect.user.username
        );
        let dataMember = [...memberSelected];
        dataMember[index] = {
            ...dataMember[index],
            role: roleSelect
        };
        this.setState({
            memberSelected: dataMember,
            dataMember: dataMember
        });
    };
    removeMember = (userSelect) => {
        let newData = this.state.memberSelected?.filter(
            (ele) => ele.user.username !== userSelect.user.username
        );
        this.setState({
            memberSelected: newData,
            dataMember: newData
        });
    };
    _getUrisImage = () => {
        const { imageBackground } = this.state;
        const deviceWidth = Dimensions.get('window').width;
        const deviceHeight = Dimensions.get('window').height;
        let uris = [
            {
                ...imageBackground[0],
                url: imageBackground[0]?.path,
                width: deviceWidth,
                height: deviceHeight
            }
        ];
        return uris;
    };
    onRequestClose = () => {
        this.setState({
            viewMedia: {
                visible: false,
                uris: []
            }
        });
    };
    handleRetryMember = () => {
        this.setState({
            dataMember: this.state.memberSelected
        });
    };
    render() {
        const { xworkData, listService } = this.props;
        const { component, common } = xworkData;
        const { indexHeader, viewMedia } = this.state;
        if (component === undefined) {
            return null;
        }
        const checkIndex = indexHeader === 0;
        const {
            WrapperContainerTicket,
            ModalAddUser,
            // ModalListService,
            ModalListServiceV2
        } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('create_group')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    this.props.navigation.goBack();
                }}
                colorTitle
                actionRetry={() => {
                    this.props.actionHome.getListService();
                }}
                buttonRightOutsize={!checkIndex ? this.handleModalUser : null}
                imgButtonOutSize="ic_add_user">
                {this.renderHeaderTab()}
                {this.state.indexHeader === 0 ? (
                    <ScrollView
                        style={{
                            paddingHorizontal: 16,
                            paddingBottom: 16
                        }}>
                        {this.renderHeader()}
                        {this.renderImage()}
                        {this.renderGroupCreator()}
                    </ScrollView>
                ) : (
                    <AddMemberGroup
                        props={this.props}
                        removeMember={this.removeMember}
                        memberSelected={this.state.dataMember}
                        changeRoleMember={this.changeRoleMember}
                        handleSearch={this.handleSearch}
                        handleRetryMember={this.handleRetryMember}
                    />
                )}

                {this.renderButtonSave()}
                {this.state.showModalUser && (
                    <ModalAddUser
                        isVisible={this.state.showModalUser}
                        titleModal={translate('add_member')}
                        onPressDimiss={() => {
                            this.setState({ showModalUser: false });
                        }}
                        handleLoadMoreUser={this.handleLoadMoreUser}
                        checkUserCreateGroup={true}
                        groupRole={this.props.listRole.data}
                        handleSearchUser={this.handleSearchUser}
                        listUser={this.props.listUser.data}
                        memberSelected={this.state.memberSelected}
                        listMemberGroup={this.state.memberSelected}
                        handleAddMember={this.handleAddMember}
                    />
                )}

                <ModalListServiceV2
                    isVisible={this.state.isShowModalListService}
                    props={this.props}
                    titleModal={translate('select_job_type')}
                    listData={listService?.data}
                    serviceSelected={this.state.serviceSelected}
                    hideModalSynch={() => {
                        this.setState({
                            isShowModalListService: false
                        });
                        this.props.actionHome.getListService();
                    }}
                    onPressSave={(item) =>
                        this.setState({
                            isShowModalListService: false,
                            serviceSelected: item
                        })
                    }
                    actionSearch={(txt) => {
                        this.props.actionHome.getListService(true, txt);
                    }}
                    onEndReached={() => {}}
                />
                <common.ImagesViewer
                    visible={viewMedia.visible}
                    imageUrls={this._getUrisImage()}
                    index={viewMedia.index}
                    onPress={this.onRequestClose}
                    onSwipeDown={() => this.onRequestClose()}
                    onRequestClose={this.onRequestClose}
                />
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listService: state.groupTicketReducer.listService,
        listUser: state.groupTicketReducer.listUser,
        listRole: state.groupTicketReducer.listRole
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(CreateGroupXticket);
const style = StyleSheet.create({
    btnHeader: {
        alignItems: 'center',
        flex: 1,
        height: Mixins.scale(50),
        justifyContent: 'center'
    },
    btnImage: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(100),
        justifyContent: 'center',
        marginTop: Mixins.scale(12),
        width: Mixins.scale(100)
    },
    btnSave: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: 12,
        height: Mixins.scale(50),
        justifyContent: 'center',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginVertical: Mixins.scale(10),
        width: '90%'
    },

    icImage: {
        height: Mixins.scale(21),
        tintColor: Colors.BLACK_HEADER_TITLE,
        width: Mixins.scale(21)
    },
    icPermission: {
        height: Mixins.scale(22),
        marginRight: Mixins.scale(8),
        tintColor: Colors.DARK_BLUE_40,
        width: Mixins.scale(22)
    },
    imgSelect: {
        borderRadius: Mixins.scale(12),
        height: '100%',
        width: '100%'
    },

    viewHeader: {
        alignItems: 'center',
        flexDirection: 'row',
        height: 50
    }
});
