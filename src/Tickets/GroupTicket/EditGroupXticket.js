import React, { PureComponent } from 'react';
import {
    View,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    ScrollView,
    Image,
    Switch,
    Dimensions
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ImagePicker from 'react-native-image-crop-picker';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import * as _actionHome from '../action';
import Toast from 'react-native-toast-message';
import TooltipComponent from './CompoentTooltip';
import { stylesTicket } from '../Ticket/stylesTicket';
import { CONST_API } from '../../constant';
const { translate } = global.props.getTranslateConfig();

class CreateGroupXticket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            descpriction: '',
            imageBackground: [],
            supportServiceIsPublic: true,
            supportServiceIsNotifyAssignAll: true,
            supportServiceIsNotifyInviteAll: true,
            isVisible: false,
            positionScroll: '',
            avatar: '',
            viewMedia: {
                visible: false,
                uris: []
            }
        };
    }

    componentDidMount() {
        const { params } = this.props.route;
        this.setState({
            title: params?.name,
            descpriction: params?.description,
            supportServiceIsPublic: params?.isPublic,
            supportServiceIsNotifyAssignAll: params?.isNotifyAssignAll,
            supportServiceIsNotifyInviteAll: params?.isNotifyInviteAll,
            avatar: params?.avatar
        });
        this.props.actionHome.getListService();
    }

    componentDidUpdate() {}

    renderHeader = () => {
        return (
            <View style={{ paddingTop: Mixins.scale(20) }}>
                <MyText
                    text={`${translate('group_name')}`}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>
                <TextInput
                    value={this.state.title}
                    onChangeText={(text) => {
                        this.setState({
                            title: text
                        });
                    }}
                    style={stylesTicket.inputTitle}
                    placeholder={`${translate('enter_group_name')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />

                <MyText
                    text={`${translate('detail_descrip')} `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}
                />
                <TextInput
                    onChangeText={(text) => {
                        this.setState({
                            descpriction: text
                        });
                    }}
                    value={this.state.descpriction}
                    multiline
                    textAlignVertical="top"
                    style={stylesTicket.txtDescription}
                    placeholder={`${translate('enter_content')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />
            </View>
        );
    };

    onShowPicker = async () => {
        try {
            await ImagePicker.openPicker({
                maxFiles: 1,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                mediaType: 'photo'
            }).then((image) => {
                this.setState({
                    avatar: '',
                    imageBackground: [image]
                });
            });
        } catch (e) {
            console.log(e, '**********');
        }
    };

    renderImage = () => {
        const { params } = this.props.route;
        const { imageBackground } = this.state;
        return (
            <View
                style={{
                    paddingTop: Mixins.scale(32)
                }}>
                <MyText
                    style={{
                        color: Colors.GRAYF10
                    }}
                    text={`${translate('contribute_image_selection')}`}
                    addSize={2}
                    typeFont="medium"
                />
                <TouchableOpacity
                    onPress={() => {
                        this.onShowPicker();
                    }}
                    onLongPress={() => {
                        if (
                            imageBackground?.length === 0 &&
                            this.state.avatar?.length === 0
                        ) {
                            return null;
                        } else {
                            this.setState({
                                viewMedia: {
                                    index: 0,
                                    visible: true,
                                    uris: []
                                }
                            });
                        }
                    }}
                    style={style.btnImage}>
                    {this.state?.avatar?.length > 0 ||
                    imageBackground?.length > 0 ? (
                        <View style={style.imgSelect}>
                            <TouchableOpacity
                                onPress={() => {
                                    this.setState({
                                        imageBackground: [],
                                        avatar: ''
                                    });
                                }}
                                hitSlop={{
                                    top: 10,
                                    bottom: 10,
                                    right: 10,
                                    left: 10
                                }}
                                style={{
                                    position: 'absolute',
                                    top: -5,
                                    right: -5,
                                    height: 15,
                                    width: 15,
                                    zIndex: 10
                                }}>
                                <Image
                                    style={{ height: 20, width: 20 }}
                                    source={{
                                        uri: 'ic_error_ticket'
                                    }}
                                />
                            </TouchableOpacity>
                            <Image
                                resizeMode="cover"
                                style={style.imgSelect}
                                source={{
                                    uri:
                                        imageBackground?.length > 0
                                            ? this.state.imageBackground[0]
                                                  ?.path
                                            : this.state?.avatar
                                }}
                            />
                        </View>
                    ) : (
                        <Image
                            style={style.icImage}
                            source={{ uri: 'ic_image' }}
                        />
                    )}
                </TouchableOpacity>
                <MyText
                    text={translate('group_creator')}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}
                />
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <View
                        style={{
                            height: Mixins.scale(26),
                            width: Mixins.scale(26),
                            borderRadius: Mixins.scale(13),
                            marginRight: Mixins.scale(10)
                        }}>
                        <Image
                            style={{
                                height: Mixins.scale(26),
                                width: Mixins.scale(26),
                                borderRadius: Mixins.scale(13)
                            }}
                            source={{
                                uri: `${CONST_API.baseAvatarURI}${params?.creatorImage}`
                            }}
                        />
                        <View
                            style={{
                                position: 'absolute',
                                bottom: -2,
                                right: 0,
                                height: Mixins.scale(10),
                                width: Mixins.scale(10),
                                borderRadius: Mixins.scale(5),
                                backgroundColor: Colors.DARK_BLUE_60,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <Image
                                style={{
                                    height: Mixins.scale(6),
                                    width: Mixins.scale(6)
                                }}
                                resizeMode="contain"
                                source={{
                                    uri: 'ic_home_ticket'
                                }}
                            />
                        </View>
                    </View>
                    <MyText
                        text={`${params?.creatorUserName} - ${params?.creatorLastName} ${params?.creatorFirstName}`}
                        addSize={1}
                        style={{ color: Colors.BLACK }}
                    />
                    <View style={{ marginLeft: 12 }}>
                        <TooltipComponent />
                    </View>
                </View>
            </View>
        );
    };

    renderGroupCreator = () => {
        const {
            supportServiceIsNotifyInviteAll,
            supportServiceIsNotifyAssignAll
        } = this.state;
        return (
            <View style={{ marginTop: 16, paddingBottom: 16 }}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <Image
                        style={{
                            height: Mixins.scale(22),
                            width: Mixins.scale(22),
                            marginRight: Mixins.scale(8),
                            tintColor: Colors.DARK_BLUE_40
                        }}
                        source={{ uri: 'ic_notification' }}
                    />

                    <MyText
                        text={translate('notification')}
                        addSize={2}
                        typeFont="medium"
                        style={{ flex: 1, color: Colors.GRAYF10 }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(16)
                    }}>
                    <MyText
                        text={translate('notify_no_recipient')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor={Colors.WHITE}
                        onValueChange={() => {
                            this.setState({
                                supportServiceIsNotifyAssignAll:
                                    !supportServiceIsNotifyAssignAll
                            });
                        }}
                        value={this.state.supportServiceIsNotifyAssignAll}
                        style={{
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(18)
                    }}>
                    <MyText
                        text={translate('notify_adding_member')}
                        addSize={2}
                        style={{ flex: 1, color: Colors.BLACK }}
                    />
                    <Switch
                        trackColor={{
                            false: Colors.DARK_ORANGE_15,
                            true: Colors.GREEN_SWITCH
                        }}
                        thumbColor={Colors.WHITE}
                        onValueChange={() => {
                            this.setState({
                                supportServiceIsNotifyInviteAll:
                                    !supportServiceIsNotifyInviteAll
                            });
                        }}
                        value={supportServiceIsNotifyInviteAll}
                        style={{
                            transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }]
                        }}
                    />
                </View>
            </View>
        );
    };

    sendMedias = (medias) => {
        const { constants } = this.props.xworkData;
        return new Promise(async (resolve) => {
            try {
                const responeImage = await constants._UploadFile(medias);
                resolve(responeImage.images[0]);
            } catch (error) {
                resolve([]);
            }
        });
    };

    handleButtonSave = async () => {
        const { params } = this.props.route;

        try {
            const {
                title,
                descpriction,
                supportServiceIsPublic,
                supportServiceIsNotifyAssignAll,
                supportServiceIsNotifyInviteAll,
                imageBackground
            } = this.state;
            if (title?.length === 0) {
                return global.props.alert({
                    title: translate('notify'),
                    show: true,
                    message: translate('alert_group_name'),
                    type: 'info',
                    confirmText: translate('close'),
                    onConfirmPressed: () => {
                        global.props.alert({ show: false });
                    }
                });
            }
            global.props.showLoader();
            let avatar = '';
            if (this.state.avatar?.length > 0) {
                avatar = params.avatar;
            }
            if (imageBackground?.length > 0) {
                avatar = await this.sendMedias(imageBackground);
            }
            const data = {
                supportServiceId: this.props.route.params?.id,
                nameSupportService: title,
                serviceId: -1,
                joinRoleId: -1,
                supportServiceIsPublic,
                supportServiceIsNotifyAssignAll,
                supportServiceIsNotifyInviteAll,
                supportServiceDescription: descpriction,
                avatar: avatar || ''
            };

            const response = await this.props.actionHome.updateGroup(data);
            global.props.hideLoader();

            if (response) {
                global.props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: translate('edit_group_success'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.navigation.navigate(
                        'DetailGroup',
                        this.props.route.params
                    );
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('edit_group_fail'),
                position: 'bottom'
            });
        }
    };

    renderButtonSave = () => {
        const { title } = this.state;
        const checkShowButton = title?.length === 0;

        return (
            <TouchableOpacity
                onPress={() => {
                    this.handleButtonSave();
                }}
                style={[
                    style.btnSave,
                    {
                        backgroundColor: checkShowButton
                            ? Colors.GRAYF6
                            : Colors.DARK_BLUE_60
                    }
                ]}>
                <MyText
                    text={translate('save')}
                    addSize={3}
                    typeFont="semiBold"
                    style={{ color: Colors.WHITE }}
                />
            </TouchableOpacity>
        );
    };
    onRequestClose = () => {
        this.setState({
            viewMedia: {
                visible: false,
                uris: []
            }
        });
    };
    _getUrisImage = () => {
        const { imageBackground, avatar } = this.state;
        const deviceWidth = Dimensions.get('window').width;
        const deviceHeight = Dimensions.get('window').height;
        let uris = [];
        if (this.state.imageBackground?.length > 0) {
            uris = [
                {
                    ...imageBackground[0],
                    url: imageBackground[0]?.path,
                    width: deviceWidth,
                    height: deviceHeight
                }
            ];
        } else {
            uris = [
                {
                    url: avatar,
                    width: deviceWidth,
                    height: deviceHeight
                }
            ];
        }

        return uris;
    };
    render() {
        const { xworkData } = this.props;
        const { component, common } = xworkData;
        const { params } = this.props.route;
        const { viewMedia } = this.state;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('change_group_info')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    this.props.navigation.navigate('DetailGroup', params);
                }}
                colorTitle>
                <ScrollView
                    onScroll={(event) => {
                        const yOffSet = event.nativeEvent.contentOffset.y;
                        this.setState({
                            positionScroll: yOffSet
                        });
                    }}
                    style={{
                        paddingHorizontal: 16
                    }}>
                    {this.renderHeader()}
                    {this.renderImage()}
                    {this.renderGroupCreator()}
                </ScrollView>

                {this.renderButtonSave()}
                <common.ImagesViewer
                    visible={viewMedia.visible}
                    imageUrls={this._getUrisImage()}
                    index={viewMedia.index}
                    onPress={this.onRequestClose}
                    onSwipeDown={() => this.onRequestClose()}
                    onRequestClose={this.onRequestClose}
                />
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listService: state.groupTicketReducer.listService,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(CreateGroupXticket);
const style = StyleSheet.create({
    btnImage: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(100),
        justifyContent: 'center',
        marginTop: Mixins.scale(12),
        width: Mixins.scale(100)
    },
    btnSave: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: 12,
        height: Mixins.scale(50),
        justifyContent: 'center',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginVertical: Mixins.scale(10),
        width: '90%'
    },
    icImage: {
        height: Mixins.scale(21),
        tintColor: Colors.BLACK_HEADER_TITLE,
        width: Mixins.scale(21)
    },

    imgSelect: {
        borderRadius: Mixins.scale(12),
        height: '100%',
        width: '100%'
    }
});
