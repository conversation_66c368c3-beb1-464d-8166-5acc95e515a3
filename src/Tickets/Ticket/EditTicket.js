import React, { PureComponent } from 'react';
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import * as _actionHome from '../action';
import * as _actionTicket from './action';
import { toastConfig } from '../GroupTicket/MemberGroupTicket';
import Toast from 'react-native-toast-message';
import ImagePicker from 'react-native-image-crop-picker';
import DocumentPicker, { types } from 'react-native-document-picker';
import { stylesTicket } from './stylesTicket';
import { requestPermission, openSetting } from '@mwg-kits/core';
import * as _actionDetailTicket from './Details/action';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
    RenderLocationGeo,
    RenderPriority,
    RenderStatus,
    RenderUserAssginee
} from './Components/ComponentCreateTicket';
import { RenderFile } from './Details/Component/ComponentTicket';
import { RenderTime } from '../../modal/ComponentModal';
import { GlobalStore } from 'redux-micro-frontend';
import ModalAddFollowers from '../../modal/ModalAddFollowers';
import { ModalListTrouble } from '../../modal/ModalListTrouble';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
const { translate } = global.props.getTranslateConfig();
export const convertUTCDateToLocalDate = (date) => {
    return new Date(
        Date.UTC(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds()
        )
    )
        .toISOString()
        .split('T')[0];
};

class EditTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            descpriction: '',
            locationSelected: null,
            selectedGroup: null,
            showModalUser: false,
            groupUserFollowers: [],
            showModalAddReceiver: false,
            userReceiver: null,
            isShowModalCalendar: false,
            startDate: '',
            solution: '',
            solutionArray: [], // Array to store selected solutions
            endDate: '',
            prioritySelected: null,
            statusSelected: null,
            isShowModalListService: false,
            dataLengthGeoLocation: 20,
            showModalListTrouble: false,
            isLengthUser: 10,
            allUser: false,
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        };
        this.globalStore = GlobalStore.Get();
    }
    removeFile = async (item) => {
        const { detailTicket } = this.props;
        let data = {
            ticketId: detailTicket.data?.ticket.id,
            fileId: item.id
        };
        try {
            global.props.showLoader();
            const response = await this.props.actionTicket.removeFile(data);
            this.initListFile();
            global.props.hideLoader();

            if (response) {
                global.props.hideLoader();

                Toast.show({
                    type: 'success',
                    text1: translate('delete_success'),
                    position: 'bottom'
                });
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('delete_fail'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: error,
                position: 'bottom'
            });
        }
    };
    renderMentionContent = (content) => {
        let contentMention = content;
        let indexContent = contentMention.indexOf('@mentionuser');
        if (indexContent !== -1) {
            contentMention = content?.slice(0, indexContent);
            return contentMention;
        }
        return content;
    };
    async componentDidMount() {
        this.initMemberGroup();
        this.props.actionTicket.getListPriority();
        this.initListTrouble();
        this.props.navigation.addListener('blur', () => {
            this.setState({
                isShowModalListService: false,
                showModalAddReceiver: false,
                showModalUser: false
            });
        });
        const { ticket } = this.props.detailTicket.data;
        let checkQuickTicket =
            helper.hasProperty(ticket, 'asSocial') && ticket.asSocial;

        let contentHashtag = '';
        contentHashtag = ticket?.content?.replace(/#hashtag:{(.*?)}/g, ' ');

        // Parse solution data to array
        const solutionArray = this.parseSolutionData(ticket.extraData4 ?? '');

        this.setState({
            groupUserFollowers: _actionHome.hasProperty(ticket, 'watcherUsers')
                ? ticket.watcherUsers
                : [],

            descpriction: checkQuickTicket
                ? this.renderMentionContent(contentHashtag)
                : ticket?.content,
            title: ticket?.subject,
            solution: ticket.extraData4 ?? '',
            solutionArray: solutionArray,
            startDate: ticket.startTimeLong,
            endDate: ticket.dueTimeLong,
            prioritySelected: {
                priority: ticket.priorityId,
                name: ticket.priorityName
            }
        });

        if (helper.IsValidateObject(ticket.priorityName)) {
            this.setState({
                prioritySelected: {
                    priority: ticket.priorityId,
                    name: ticket.priorityName
                }
            });
        } else {
            this.setState({
                prioritySelected: this.props.listPriority?.data[0]
            });
        }
        if (_actionHome.hasProperty(ticket, 'assigneeUsername')) {
            this.setState({
                userReceiver: {
                    user: {
                        username: ticket.assigneeUsername,
                        id: ticket.assigneeId,
                        profile: {
                            lastName: ticket.assigneeLastName,
                            firstName: ticket.assigneeFirstName,
                            image: ticket.assigneeImage
                        }
                    }
                }
            });
        }
    }

    handeleGroupSelected = () => {
        const { params } = this.props.route;
        let itemSelected = null;
        itemSelected = this.props.listGroupTicket?.data?.filter(
            (item) => item?.id === params
        );
        this.setState({
            selectedGroup: itemSelected
        });
    };

    initMemberGroup = () => {
        const { ticket } = this.props.detailTicket.data;
        const data = {
            supportServiceId: ticket?.supportServiceId,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10, search: '' },
            requestId: ''
        };
        this.props.actionHome.getListMemberGroup(data);
        this.props.actionTicket.getListLocationgeo(data);
    };

    renderHeader = () => {
        return (
            <View
                style={{
                    paddingTop: Mixins.scale(16),
                    paddingHorizontal: Mixins.scale(16)
                }}>
                <MyText
                    text={`${translate('ticket_name')}: `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>
                <TextInput
                    value={this.state.title}
                    onChangeText={(text) => {
                        this.setState({
                            title: text
                        });
                    }}
                    editable={this.checkRoleEditTicket('TICKET_CONTENT') !== -1}
                    style={stylesTicket.inputTitle}
                    placeholder={`${translate('enter_ticket_name')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />

                <MyText
                    text={`${translate('detail_descrip')}`}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>
                <TextInput
                    onChangeText={(text) => {
                        this.setState({
                            descpriction: text
                        });
                    }}
                    editable={this.checkRoleEditTicket('TICKET_CONTENT') !== -1}
                    value={this.state.descpriction}
                    multiline
                    textAlignVertical="top"
                    style={stylesTicket.txtDescription}
                    placeholder={`${translate('enter_content')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />
            </View>
        );
    };

    renderChip = (text, index) => {
        return (
            <View
                key={index}
                style={{
                    backgroundColor: Colors.GRAYF5,
                    borderRadius: Mixins.scale(8),
                    paddingHorizontal: Mixins.scale(12),
                    paddingVertical: Mixins.scale(10),
                    marginRight: Mixins.scale(8),
                    marginBottom: Mixins.scale(8),
                    borderWidth: 1,
                    borderColor: Colors.GRAYF4,
                    flexDirection: 'row',
                    alignItems: 'center',
                    minHeight: Mixins.scale(36)
                }}>
                <MyText
                    text={text}
                    style={{
                        color: Colors.BLACK
                    }}
                    numberOfLines={2}
                />
            </View>
        );
    };

    renderChipWithDelete = (text, index, onDelete) => {
        return (
            <View
                key={index}
                style={{
                    backgroundColor: Colors.GRAYF5,
                    borderRadius: Mixins.scale(8),
                    paddingHorizontal: Mixins.scale(12),
                    paddingVertical: Mixins.scale(10),
                    marginRight: Mixins.scale(8),
                    marginBottom: Mixins.scale(8),
                    borderWidth: 1,
                    borderColor: Colors.GRAYF4,
                    flexDirection: 'row',
                    alignItems: 'center',
                    minHeight: Mixins.scale(36)
                }}>
                <MyText
                    text={text}
                    style={{
                        color: Colors.BLACK
                    }}
                    numberOfLines={2}
                />
                <TouchableOpacity
                    onPress={() => onDelete(index)}
                    style={{
                        marginLeft: Mixins.scale(8),
                        padding: Mixins.scale(4)
                    }}>
                    <AntDesign
                        name="close"
                        size={14}
                        color={Colors.GRAY_PLACEHODER}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    parseSolutionData = (extraData4) => {
        try {
            // If it's a string and looks like JSON array
            if (typeof extraData4 === 'string') {
                // Try to parse JSON
                if (
                    extraData4.trim().startsWith('[') &&
                    extraData4.trim().endsWith(']')
                ) {
                    const parsed = JSON.parse(extraData4);
                    if (Array.isArray(parsed)) {
                        return parsed.map(
                            (item) => item.name || item.toString()
                        );
                    }
                }
                // If it's a plain string, split by /
                return [extraData4.toString()];
            }
            // If it's already an array
            if (Array.isArray(extraData4)) {
                return extraData4.map((item) => item.name || item.toString());
            }
            // Fallback: convert to string
            return [extraData4.toString()];
        } catch (error) {
            console.log('Error parsing solution data:', error);
            // Fallback: treat as string
            if (typeof extraData4 === 'string') {
                return [extraData4.toString()];
            }
            return [extraData4.toString()];
        }
    };

    // Function to remove solution from array
    removeSolution = (index) => {
        const { solutionArray } = this.state;
        const newSolutionArray = solutionArray.filter((_, i) => i !== index);
        this.setState({
            solutionArray: newSolutionArray,
            solution: JSON.stringify(
                newSolutionArray.map((item) => ({ name: item }))
            )
        });
    };

    renderSolution = () => {
        const { solutionArray } = this.state;

        return (
            <View style={{ marginBottom: 16 }}>
                <View style={stylesTicket.viewRowBottom}>
                    <MyText
                        text={`${translate('solution')}: `}
                        addSize={2}
                        typeFont="medium"
                        style={{
                            color: Colors.GRAYF10,
                            marginTop: Mixins.scale(32)
                        }}
                    />
                </View>
                <View
                    style={{
                        marginTop: 16,
                        flexDirection: 'row',
                        flexWrap: 'wrap'
                    }}>
                    {solutionArray.map((item, index) =>
                        this.renderChipWithDelete(
                            item,
                            index,
                            this.removeSolution
                        )
                    )}
                </View>
            </View>
        );
    };

    renderContent = () => {
        const { detailTicket, listFileTicket } = this.props;
        const { ticket, allowedStatus } = detailTicket.data;
        const { statusSelected } = this.state;
        const { userReceiver } = this.state;

        return (
            <View
                style={{
                    marginHorizontal: Mixins.scale(16),
                    marginTop: Mixins.scale(16)
                }}>
                <View style={stylesTicket.viewRowBetween_16}>
                    <View style={stylesTicket.viewRowAlignItems}>
                        <Image
                            style={stylesTicket.imgBlue_20}
                            source={{ uri: 'ic_calendar' }}
                        />
                        <MyText
                            text={translate('date')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                </View>
                <RenderTime
                    startDate={this.state.startDate}
                    hideTitle={true}
                    endDate={this.state.endDate}
                    onPressShowModal={(item) => {
                        if (
                            this.checkRoleEditTicket('TICKET_START_TIME') !== -1
                        ) {
                            this.setState({
                                chooseDate: item,
                                isShowModalCalendar: true
                            });
                        } else {
                            Toast.show({
                                type: 'error',
                                text1: translate('no_right_edit'),
                                position: 'bottom'
                            });
                        }
                    }}
                />

                <RenderUserAssginee
                    handleShowModal={this.handleShowModal}
                    userReceiver={userReceiver}
                    groupUserFollowers={this.state.groupUserFollowers}
                />
                <RenderFile
                    handleUpFile={this.handleUpFile}
                    listFileTicket={listFileTicket.data}
                    handleRemoveFile={this.handleRemoveFile}
                    setViewMedia={this.setViewMedia}
                    isShowRemove={true}
                />

                <RenderLocationGeo
                    onPressShowModal={this.handleModalLocationGeo}
                    locationSelected={
                        helper.IsValidateObject(this.state.locationSelected)
                            ? this.state.locationSelected?.name
                            : helper.IsValidateObject(
                                  detailTicket?.data?.ticket?.locationGeoName
                              )
                            ? `${detailTicket?.data?.ticket?.locationGeoName}`
                            : translate('please_choose_supermarket')
                    }
                />
                <RenderStatus
                    ticket={ticket}
                    allowedStatus={allowedStatus}
                    statusSelected={statusSelected}
                    onChangeItem={this.onChangeItem}
                />
                <RenderPriority
                    prioritySelected={this.state.prioritySelected}
                    listPriority={this.props.listPriority.data}
                    onChangeComponent={this.onChangePriority}
                    placeholder={ticket.priorityName || translate('low')}
                />
                {this.renderSolution()}

                <TouchableOpacity
                    onPress={() => {
                        this.setState({
                            showModalListTrouble: true
                        });
                    }}
                    style={{
                        backgroundColor: Colors.WHITE,
                        borderColor: Colors.GRAYF6,
                        borderRadius: Mixins.scale(12),
                        borderWidth: 0.5,
                        height: Mixins.scale(44),
                        justifyContent: 'center',
                        marginVertical: Mixins.scale(16),
                        paddingHorizontal: Mixins.scale(12),
                        width: '100%'
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                        <MyText style={{}} text={translate('workaround')} />
                        <AntDesign
                            name="down"
                            color={Colors.GRAYF9}
                            size={16}
                            style={{
                                marginRight: Mixins.scale(4)
                            }}
                        />
                    </View>
                </TouchableOpacity>
            </View>
        );
    };

    onChangePriority = (item) => {
        if (this.checkRoleEditTicket('TICKET_PRIORITY') !== -1) {
            this.setState({
                prioritySelected: item
            });
        } else {
            Toast.show({
                type: 'error',
                text1: translate('no_right_edit'),
                position: 'bottom'
            });
        }
    };
    onChangeItem = (item) => {
        if (this.checkRoleEditTicket('TICKET_CHANGE_STATUS') !== -1) {
            this.setState({
                statusSelected: item
            });
        } else {
            Toast.show({
                type: 'error',
                text1: translate('no_right_edit'),
                position: 'bottom'
            });
        }
    };

    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };

    handleChangeTicket = (item) => {
        const { listTicket } = this.props;
        let newData = [...listTicket.data];

        let index = this.props.listTicket.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            this.props.actionTicket.stop_get_list_ticket({
                data: newData
            });
        }
    };
    handleNewFeed = (item) => {
        const { listNewFeed } = this.props;
        let newData = [...this.props.listNewFeed.data];

        let index = listNewFeed?.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );

        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };

            this.props.actionHome.stop_get_new_feed({
                data: newData
            });
        }
    };
    handleButtonSave = async () => {
        try {
            const {
                title,
                descpriction,
                userReceiver,
                groupUserFollowers,
                locationSelected,
                startDate,
                endDate,
                prioritySelected,
                statusSelected,
                solutionArray
            } = this.state;
            const { ticket } = this.props.detailTicket.data;
            let arrayWatcher = [];
            groupUserFollowers?.forEach((e) => {
                if (!helper.IsValidateObject(e.user)) {
                    arrayWatcher.push(e.userId);
                } else {
                    arrayWatcher.push(e.user?.id);
                }
            });

            // Convert solutionArray to JSON string for API
            const solutionString =
                solutionArray.length > 0
                    ? JSON.stringify(
                          solutionArray.map((item) => ({ name: item }))
                      )
                    : '';

            const params = {
                source: 'phone',
                id: ticket.id,
                supportServiceId: ticket?.supportServiceId,
                subject: title,
                solution: solutionString,
                content: descpriction,
                startTime: new Date(startDate).getTime(),
                dueTime: new Date(endDate).getTime(),
                createdUserId: ticket?.creatorId,
                assigneeUserId: userReceiver?.user?.id || -1,
                watcherUserId: !helper.IsEmptyArray(arrayWatcher)
                    ? arrayWatcher
                    : [],
                statusId: helper.IsValidateObject(statusSelected)
                    ? statusSelected?.id
                    : helper.IsValidateObject(ticket?.statusId)
                    ? ticket.statusId
                    : -1,
                priorityId: helper.IsValidateObject(prioritySelected)
                    ? prioritySelected?.priority
                    : helper.IsValidateObject(ticket?.priorityId)
                    ? ticket.priorityId
                    : -1,
                locationGeoId: helper.IsValidateObject(locationSelected?.id)
                    ? locationSelected?.id
                    : helper.IsValidateObject(ticket?.locationGeoId)
                    ? ticket?.locationGeoId
                    : -1,
                approver: []
            };
            let msgError = '';
            if (title?.length === 0) {
                msgError = translate('alert_enter_name');
            }

            if (msgError?.length > 0) {
                return global.props.alert({
                    title: translate('notify'),
                    show: true,
                    message: msgError,
                    type: 'info',
                    confirmText: 'Đóng',
                    onConfirmPressed: () => {
                        global.props.alert({ show: false });
                    }
                });
            }
            global.props.showLoader();
            const reponse = await this.props.actionTicket.createTicket(params);
            this.props.actionTicket.getDetailTicket(ticket.id);
            global.props.hideLoader();

            if (reponse) {
                this.handleNewFeed(reponse);
                this.handleChangeTicket(reponse);
                this.handleGlobalState();

                global.props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: translate('edit_ticket_success'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.navigation.goBack();
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: helper.IsValidateObject(error)
                    ? error
                    : translate('edit_ticket_fail'),
                position: 'bottom'
            });
        }
    };

    renderButtonSave = () => {
        const { title, descpriction } = this.state;
        const checkShowButton =
            title?.length === 0 || descpriction?.length === 0;
        // if (checkShowButton) {
        //     return null;
        // }
        return (
            <TouchableOpacity
                onPress={this.handleButtonSave}
                disabled={checkShowButton}
                style={[
                    style.btnSave,
                    {
                        backgroundColor: checkShowButton
                            ? Colors.GRAYF6
                            : Colors.DARK_BLUE_60
                    }
                ]}>
                <MyText
                    text={translate('save_ticket')}
                    addSize={3}
                    typeFont="semiBold"
                    style={{ color: Colors.WHITE }}
                />
            </TouchableOpacity>
        );
    };

    actionSearch = (txt) => {
        const { detailTicket } = this.props;
        const data = {
            supportServiceId:
                detailTicket?.data?.ticket?.supportServiceId || -1,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10, search: txt },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
    };
    initListFile = () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10 }
        };

        this.props.actionTicket.getListFile(params);
    };

    upLoadFile = async () => {
        try {
            const response = await DocumentPicker.pick({
                allowMultiSelection: true,
                presentationStyle: 'fullScreen',
                type: [
                    types.pdf,
                    types.doc,
                    types.xls,
                    types.pptx,
                    types.ppt,
                    types.docx,
                    types.xlsx
                ]
            });
            const { detailTicket } = this.props;

            if (response) {
                const body = {
                    files: response,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };
                const sendFile = await this.props.actionTicket.uploadFile(body);
                try {
                    if (sendFile) {
                        this.initListFile();
                        global.props.hideLoader();
                        Toast.show({
                            type: 'success',
                            text1: translate('upload_file_success'),
                            position: 'bottom'
                        });
                    }
                } catch (error) {
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_file_fail'),
                        position: 'bottom'
                    });
                }
            }
        } catch (e) {
            console.log(e);
        }
    };
    handleUpFile = async (item) => {
        setTimeout(() => {
            this.pickerGallery(item);
        }, 1000);
    };
    pickerGallery = async (item) => {
        try {
            await requestPermission('photo');
            if (item.id === 0) {
                this.onShowPicker();
            } else {
                this.upLoadFile();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('contribute_no_gallery_right'),
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };
    onShowPicker = async () => {
        const { detailTicket } = this.props;
        try {
            await ImagePicker.openPicker({
                maxFiles: 5,
                multiple: true,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                maximumVideoDuration: 60000,
                compressVideoPreset: 'HighestQuality'
            }).then(async (image) => {
                try {
                    const body = {
                        files: image,
                        ticketId: detailTicket?.data?.ticket?.id,
                        comment: false
                    };
                    const sendImage =
                        await this.props.actionTicket.uploadImageComment(body);

                    global.props.hideLoader();
                    this.initListFile();
                    if (sendImage) {
                        global.props.hideLoader();
                        Toast.show({
                            type: 'success',
                            text1: translate('upload_image_success'),
                            position: 'bottom'
                        });
                    }
                } catch (error) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_image_failed'),
                        position: 'bottom'
                    });
                }
            });
        } catch (e) {
            console.log(e, '12312312321');
        }
    };
    handleShowVideo = async () => {
        const { detailTicket } = this.props;
        const respone = await ImagePicker.openPicker({
            mediaType: 'video',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            durationLimit: 60,
            maximumVideoDuration: 60000,
            compressVideoPreset: 'HighestQuality'
        });
        try {
            if (respone) {
                const body = {
                    files: respone,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };
                const sendVideo =
                    await this.props.actionTicket.uploadImageComment(body);
                global.props.hideLoader();
                this.initListFile();

                if (sendVideo) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_video_success'),
                        position: 'bottom'
                    });
                }
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('upload_video_failed'),
                position: 'bottom'
            });
        }
    };
    handleRemoveFile = (item) => {
        global.props.alert({
            show: true,
            title: translate('delete_file'),
            titleColor: { color: Colors.DARK_RED_30 },
            message: translate('ask_delete_file'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
                this.removeFile(item);
            },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
            }
        });
    };
    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };

    handleModalLocationGeo = async () => {
        if (this.checkRoleEditTicket('TICKET_START_TIME') === -1) {
            return Toast.show({
                type: 'error',
                text1: translate('no_right_edit'),
                position: 'bottom'
            });
        } else {
            this.setState({
                isShowModalListService: true
            });
        }
    };

    onPressSaveModal = (item) => {
        this.setState({
            showModalUser: false,
            groupUserFollowers: item
        });
        this.handleSearchUser('');
    };
    handleLoadMoreUser = async (txt, allUser) => {
        const { isLengthUser } = this.state;
        const { ticket } = this.props.detailTicket.data;

        if (allUser) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: isLengthUser + 10,
                    search: txt || ''
                }
            };
            await this.props.actionHome.searchAllUser(body, true);
        } else {
            const data = {
                supportServiceId: ticket?.supportServiceId,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: isLengthUser + 10,
                    search: txt || ''
                },
                requestId: ''
            };
            this.props.actionHome.getListMemberGroup(data);
        }
        this.setState({
            isLengthUser: isLengthUser + 10
        });
    };
    handleSearchUser = async (txt, allUser) => {
        const { ticket } = this.props.detailTicket.data;
        if (allUser) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: txt || ''
                }
            };
            await this.props.actionHome.searchAllUser(body, true);
        } else {
            const data = {
                supportServiceId: ticket?.supportServiceId,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: txt || ''
                },
                requestId: ''
            };
            this.props.actionHome.getListMemberGroup(data);
        }
        this.setState({
            allUser: allUser,
            isLengthUser: 10
        });
    };
    onPressSaveReceiver = (item) => {
        this.setState({
            userReceiver: item,
            showModalAddReceiver: false
        });
        this.handleSearchUser();
    };

    handleShowModal = (item) => {
        try {
            if (item?.id === 0) {
                if (this.checkRoleEditTicket('TICKET_ASSIGN') !== -1) {
                    return this.setState({
                        showModalAddReceiver: true
                    });
                } else {
                    return Toast.show({
                        type: 'error',
                        text1: translate('no_right_edit'),
                        position: 'bottom'
                    });
                }
            }
            if (item?.id === 1) {
                if (this.checkRoleEditTicket('TICKET_WATCHER') !== -1) {
                    return this.setState({
                        showModalUser: true
                    });
                } else {
                    return Toast.show({
                        type: 'error',
                        text1: translate('no_right_edit'),
                        position: 'bottom'
                    });
                }
            }
        } catch (error) {
            console.log(error);
        }
    };

    onChangeSelectedDate = (startDate, endDate) => {
        if (startDate && endDate) {
            this.setState({
                endDate: endDate,
                startDate: startDate
            });
        }
        this.onPressDimissModalCalendar();
    };

    retryData = async () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10 }
        };
        await this.props.actionTicket.getDetailTicket(
            detailTicket?.data?.ticket?.id
        );
        this.props.actionTicket.getListCommentTicket(params);
        this.props.actionTicket.getListTask(params);
        this.props.actionTicket.getListFile(params);
    };

    onPressDimissModalCalendar = () => {
        this.setState({
            isShowModalCalendar: false
        });
    };
    handleOffModal = () => {
        this.setState({ showModalUser: false, showModalAddReceiver: false });
        this.handleSearchUser('');
    };

    checkRoleEditTicket = (item) => {
        const { ticket } = this.props.detailTicket.data;
        const { roleFunctions } = ticket;
        let isExist = (element) => element === item;
        return roleFunctions?.findIndex(isExist);
    };

    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };
    loadMoreLocation = (text) => {
        const { dataLengthGeoLocation } = this.state;
        const { detailTicket } = this.props;
        const body = {
            supportServiceId:
                detailTicket?.data?.ticket?.supportServiceId || -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: dataLengthGeoLocation + 10,
                search: text || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(body);
        this.setState({
            dataLengthGeoLocation: dataLengthGeoLocation + 10
        });
    };
    checkClickVideo = (item) => {
        return item?.filemime.includes('video');
    };
    loadMoreTrouble = (text) => {
        const { dataLengthTrouble } = this.state;

        const data = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: dataLengthTrouble + 10,
                search: text || ''
            }
        };
        this.props.actionDetailTicket.getListTrouble(data);
        this.setState({
            dataLengthGeoLocation: dataLengthTrouble + 10
        });
    };
    handleAddStore = (listStore = []) => {
        this.setState({
            showModalListTrouble: false
        });

        // Replace the entire solutionArray with selected items
        // This ensures that unchecked items are removed
        const newSolutions = listStore.map((item) => item?.name || item?.code);

        this.setState({
            solutionArray: newSolutions,
            solution: JSON.stringify(
                newSolutions.map((item) => ({ name: item }))
            )
        });
    };
    initListTrouble = (text) => {
        const data = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: text || ''
            }
        };
        this.props.actionDetailTicket.getListTrouble(data);
    };

    getSelectedStoreItems = () => {
        const { solutionArray } = this.state;
        const { listTrouble } = this.props;

        if (!listTrouble || !listTrouble.data) {
            return solutionArray.map((name) => ({ name, id: name }));
        }

        // Match existing solutions with items in the store list
        const matchedItems = [];

        solutionArray.forEach((solution) => {
            // Try to find matching item by name or id
            const matchedItem = listTrouble.data.find(
                (item) =>
                    item.name === solution ||
                    item.id === solution ||
                    item.id?.toString() === solution ||
                    item.code === solution
            );

            if (matchedItem) {
                matchedItems.push({
                    name: matchedItem.name,
                    id: matchedItem.id,
                    code: matchedItem.code
                });
            } else {
                // If no match found, keep the original solution as fallback
                matchedItems.push({ name: solution, id: solution });
            }
        });

        return matchedItems;
    };
    render() {
        const {
            xworkData,
            listLocationGeo,
            listFileTicket,
            streamToken,
            detailTicket
        } = this.props;

        const { component, common } = xworkData;
        const { viewMedia } = this.state;
        if (component === undefined) {
            return null;
        }
        let checkMinDate =
            new Date(detailTicket.data?.ticket?.startTimeLong).getTime() >
            new Date().getTime()
                ? new Date()
                : convertUTCDateToLocalDate(
                      new Date(detailTicket.data?.ticket?.startTimeLong)
                  );

        const {
            WrapperContainerTicket,
            ModalAddReceiver,
            ModalCalendarNew,
            ModalListServiceV2
        } = component;
        const selectedStartDate = this.state.startDate
            ? convertUTCDateToLocalDate(new Date(this.state.startDate))
            : '';
        const selectedEndDate = this.state.endDate
            ? convertUTCDateToLocalDate(new Date(this.state.endDate))
            : '';

        return (
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1 }}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={translate('edit_ticket')}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    onPressBack={() => {
                        this.props.navigation.goBack();
                    }}
                    // isSuccess={!detailTicket?.isError}
                    messageLoading={translate('getting_job_ticket')}
                    messageError={translate('something_wrong_server')}
                    colorTitle>
                    <KeyboardAwareScrollView
                        behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
                        extraHeight={120}
                        showsVerticalScrollIndicator={false}
                        keyboardShouldPersistTaps="never">
                        {this.renderHeader()}
                        {this.renderContent()}
                    </KeyboardAwareScrollView>
                    {this.renderButtonSave()}

                    <ModalListServiceV2
                        isVisible={this.state.isShowModalListService}
                        props={this.props}
                        isShowCodeService={true}
                        titleModal={translate('choose_supermarket')}
                        listData={listLocationGeo?.data}
                        serviceSelected={this.state.locationSelected}
                        hideModalSynch={() => {
                            this.setState({
                                isShowModalListService: false
                            });
                        }}
                        onPressSave={(item) =>
                            this.setState({
                                isShowModalListService: false,
                                locationSelected: item
                            })
                        }
                        onEndReached={(text) => {
                            this.loadMoreLocation(text);
                        }}
                        actionSearch={this.actionSearch}
                    />
                    <ModalAddReceiver
                        isVisible={this.state.showModalAddReceiver}
                        titleModal={translate('add_user')}
                        onPressDimiss={this.handleOffModal}
                        handleSearchUser={this.handleSearchUser}
                        onPressSave={this.onPressSaveReceiver}
                        userReceiver={this.state.userReceiver}
                        listUser={this.props.listMemberGroup.data}
                        handleLoadMoreUser={this.handleLoadMoreUser}
                    />
                    {this.state.showModalUser && (
                        <ModalAddFollowers
                            isVisible={this.state.showModalUser}
                            xworkData={this.props.xworkData}
                            titleModal={translate('add_watcher')}
                            onPressDimiss={this.handleOffModal}
                            handleSearchUser={this.handleSearchUser}
                            onPressSave={this.onPressSaveModal}
                            groupUserFollowers={this.state.groupUserFollowers}
                            listUser={
                                this.state.allUser
                                    ? this.props.listUser.data
                                    : this.props.listMemberGroup.data
                            }
                            allUser={this.state.allUser}
                            handleLoadMoreUser={this.handleLoadMoreUser}
                        />
                    )}

                    {this.state.isShowModalCalendar && (
                        <ModalCalendarNew
                            isVisible={this.state.isShowModalCalendar}
                            onChangeSelectedDate={this.onChangeSelectedDate}
                            onPressDimiss={this.onPressDimissModalCalendar}
                            endDate={selectedEndDate}
                            startDate={selectedStartDate}
                            minDate={checkMinDate}
                        />
                    )}

                    {viewMedia.visible && (
                        <common.ViewFile
                            visible={viewMedia.visible}
                            imageUrls={listFileTicket?.data}
                            index={viewMedia.index}
                            onPress={this.onRequestClose}
                            onSwipeDown={() => this.onRequestClose()}
                            onRequestClose={this.onRequestClose}
                            streamToken={streamToken?.data?.token}
                            toastConfig={toastConfig}
                            isClickVideo={this.checkClickVideo(
                                listFileTicket?.data[viewMedia.index]
                            )}
                        />
                    )}
                    {this.state.showModalListTrouble && (
                        <ModalListTrouble
                            isVisible={this.state.showModalListTrouble}
                            titleModal={translate('workaround')}
                            onPressDimiss={() => {
                                this.setState({
                                    showModalListTrouble: false
                                });
                            }}
                            handleLoadMoreUser={this.loadMoreTrouble}
                            handleSearchStore={this.initListTrouble}
                            listStore={this.props.listTrouble.data}
                            storeSelected={this.getSelectedStoreItems()}
                            xworkData={this.props.xworkData}
                            handleAddStore={this.handleAddStore}
                        />
                    )}
                </WrapperContainerTicket>
            </KeyboardAvoidingView>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        listPriority: state.ticketReducer.listPriority,
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listTicket: state.ticketReducer.listTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken,
        listNewFeed: state.groupTicketReducer.listNewFeed,
        listUser: state.groupTicketReducer.listUser,
        listTrouble: state.detailTicketReducer.listTrouble
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionDetailTicket: bindActionCreators(_actionDetailTicket, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(EditTicket);
const style = StyleSheet.create({
    btnSave: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: 12,
        height: Mixins.scale(50),
        justifyContent: 'center',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginVertical: Mixins.scale(10),
        width: '90%'
    }
});
