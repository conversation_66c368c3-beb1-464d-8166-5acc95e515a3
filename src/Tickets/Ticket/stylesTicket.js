import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';

import { StyleSheet } from 'react-native';
export const stylesTicket = StyleSheet.create({
    assigneeImage: {
        alignItems: 'flex-end',
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(24),
        width: Mixins.scale(24)
    },
    btnBottom: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(28),
        bottom: Mixins.scale(80),
        height: Mixins.scale(56),
        justifyContent: 'center',
        position: 'absolute',
        right: Mixins.scale(16),
        width: Mixins.scale(56)
    },
    btnDate: {
        alignItems: 'center',
        borderColor: Colors.GRAYF4,
        borderRadius: 12,
        borderWidth: 0.5,
        flexDirection: 'row',
        marginLeft: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(4)
    },
    btnDownGroup: {
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        flexDirection: 'row',
        height: Mixins.scale(44),
        justifyContent: 'space-between',
        paddingHorizontal: 12
    },
    btnDropdownPriority: {
        backgroundColor: Colors.DARK_YELLOW_30,
        borderRadius: Mixins.scale(16),
        height: Mixins.scale(56),
        marginTop: Mixins.scale(8),
        paddingLeft: Mixins.scale(12),
        width: '100%'
    },
    btnLocation: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(12),
        flexDirection: 'row',
        height: Mixins.scale(52),
        justifyContent: 'space-between',
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        width: '100%'
    },
    btnSave: {
        alignItems: 'center',
        borderRadius: 12,
        height: Mixins.scale(50),
        justifyContent: 'center',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginVertical: Mixins.scale(10),
        width: '90%'
    },

    container: {
        flex: 1
    },
    icError: {
        height: Mixins.scale(16),
        marginRight: Mixins.scale(4),
        width: Mixins.scale(16)
    },
    icLeftTicket: {
        height: Mixins.scale(24),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(24)
    },
    icPermission: {
        height: Mixins.scale(28),
        marginRight: Mixins.scale(10),
        tintColor: Colors.DARK_BLUE_40,
        width: Mixins.scale(28)
    },
    iconStyle: {
        height: Mixins.scale(28),
        marginRight: Mixins.scale(8),
        tintColor: Colors.WHITE,
        width: Mixins.scale(28)
    },
    imgBlue_20: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    imgIcChat: {
        height: Mixins.scale(18),
        tintColor: Colors.DARK_YELLOW_40,
        width: Mixins.scale(18)
    },
    imgIcNote: {
        height: Mixins.scale(18),
        marginLeft: Mixins.scale(12),
        width: Mixins.scale(18)
    },

    imgLocationTicket: {
        height: Mixins.scale(22),
        width: Mixins.scale(22)
    },
    imgPlus: {
        height: Mixins.scale(24),
        tintColor: Colors.LIGHT_GREEN_50,
        width: Mixins.scale(24)
    },
    imgUserReceiver: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(28),
        marginLeft: Mixins.scale(14),
        width: Mixins.scale(28)
    },

    inputTitle: {
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(16),
        borderWidth: 1,
        color: Colors.BLACK,
        flex: 1,
        fontSize: 16,
        fontWeight: '400',
        height: Mixins.scale(56),
        marginTop: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(8)
    },
    styleDropdownComponent: {
        alignItems: 'flex-start',
        height: Mixins.scale(24),
        width: Mixins.scale(140)
    },
    styleItem: {
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: 20,
        borderWidth: 0.5,
        marginTop: Mixins.scale(16),
        minHeight: Mixins.scale(74),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(18),
        width: '100%'
    },

    styleViewIconItem: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(8)
    },
    touchItemFile: {
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: Mixins.scale(16),
        marginTop: Mixins.scale(16)
    },

    txtDescription: {
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(16),
        color: Colors.BLACK,
        fontSize: 16,
        height: Mixins.scale(120),
        marginTop: Mixins.scale(8),
        paddingTop: Mixins.scale(10),
        padding: Mixins.scale(16)
    },

    txtLabel: {
        color: Colors.WHITE,
        fontSize: 14,
        textAlign: 'left'
    },
    txtStart: {
        color: Colors.DARK_ORANGE_50,
        marginHorizontal: 10
    },
    viewDescripDetail: {
        alignItems: 'center',
        flexDirection: 'row',
        marginHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(16)
    },
    viewError: {
        alignItems: 'center',
        flexDirection: 'row',
        paddingHorizontal: Mixins.scale(12),
        paddingTop: Mixins.scale(4)
    },
    viewIconDown: {
        alignItems: 'center',
        height: Mixins.scale(44),
        justifyContent: 'center',
        marginLeft: 8
    },
    viewImg: {
        height: Mixins.scale(22),
        tintColor: Colors.GRAYF7,
        width: Mixins.scale(22)
    },
    viewItemDropdown: {
        height: Mixins.scale(40),
        justifyContent: 'center',
        paddingHorizontal: Mixins.scale(8)
    },
    viewLine: {
        backgroundColor: Colors.GRAYF4,
        height: 1,
        marginVertical: Mixins.scale(16),
        width: '100%'
    },
    viewRowAlignItems: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    viewRowBetween_16: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(16)
    },
    viewRowHorizontal_10: {
        alignItems: 'center',
        flexDirection: 'row',
        marginHorizontal: Mixins.scale(10)
    },
    viewRowTop_16: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(16)
    },
    viewSelecGroup: {
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(16),
        borderWidth: 0.5,
        flexDirection: 'row',
        height: Mixins.scale(56),
        justifyContent: 'space-between',
        marginTop: Mixins.scale(8),
        paddingHorizontal: 12
    },
    viewUserAssignee: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(40),
        justifyContent: 'space-between'
    }
});
