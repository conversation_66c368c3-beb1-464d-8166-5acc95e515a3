import { useRef, useState } from 'react';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
    View,
    TextInput,
    StyleSheet,
    Image,
    TouchableOpacity,
    TouchableWithoutFeedback,
    FlatList,
    ScrollView,
    ActivityIndicator
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, DropdownComponent } from '@mwg-kits/components';
import { stylesTicket } from '../../stylesTicket';
import moment from 'moment';
const { translate } = global.props.getTranslateConfig();
import { TooltipCopyPaste } from '../../../GroupTicket/CompoentTooltip';
import ShowMoreText from '../../Components/ShowMoreText';
import Toast from 'react-native-toast-message';
import { checkFileMime } from '../../../../utility';
import { CONST_API } from '../../../../constant';
const InputText = (props) => {
    const textInputRef = useRef(null);
    const handlePress = () => {
        // Focus vào TextInput khi người dùng chọn vào vùng này
        if (textInputRef.current) {
            textInputRef.current.focus();
        }
    };
    return (
        <View style={[props.style, { marginBottom: Mixins.scale(32) }]}>
            {props.title && (
                <MyText
                    text={props.title}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}>
                    {props.showStar && (
                        <MyText
                            text="* "
                            addSize={2}
                            typeFont="medium"
                            numberOfLines={1}
                            style={stylesTicket.txtStart}
                        />
                    )}
                </MyText>
            )}
            {props.multiline ? (
                <TouchableWithoutFeedback onPress={handlePress}>
                    <View
                        style={[
                            styles.viewTextInput,
                            {
                                backgroundColor: Colors.GRAYF5,
                                height: Mixins.scale(120)
                            }
                        ]}>
                        <TextInput
                            keyboardType={props.number ? 'phone-pad' : null}
                            textAlignVertical="top"
                            numberOfLines={5}
                            onFocus={props?.onFocus}
                            onBlur={props?.onBlur}
                            ref={textInputRef}
                            value={props.value}
                            multiline={true}
                            style={{
                                fontSize: 16,
                                marginHorizontal: Mixins.scale(16),
                                marginVertical: Mixins.scale(8)
                            }}
                            placeholder={
                                props.placeholder
                                    ? props.placeholder
                                    : translate('enter_content')
                            }
                            onChangeText={(text) => {
                                props.onChangeText(text);
                            }}
                            placeholderTextColor={Colors.GRAY_PLACEHODER}
                        />
                    </View>
                </TouchableWithoutFeedback>
            ) : (
                <View
                    style={[
                        styles.viewTextInput,
                        {
                            borderWidth: 1,
                            height: Mixins.scale(56),
                            borderColor: Colors.DARK_ORANGE_15
                        }
                    ]}>
                    <TextInput
                        keyboardType={props.number ? 'number-pad' : null}
                        value={props.value}
                        onChangeText={(text) => {
                            props.onChangeText(text);
                        }}
                        style={{
                            marginHorizontal: Mixins.scale(16),
                            flex: 1,
                            fontSize: 16
                        }}
                        placeholder={
                            props.placeholder
                                ? props.placeholder
                                : translate('enter_content')
                        }
                        placeholderTextColor={Colors.GRAY_PLACEHODER}
                    />
                </View>
            )}
        </View>
    );
};
const DropdownInput = (props) => {
    return (
        <View
            style={[
                props.style,
                {
                    marginBottom: Mixins.scale(32)
                }
            ]}>
            <MyText
                text={props.title}
                addSize={2}
                typeFont="medium"
                style={{
                    color: Colors.GRAYF10
                }}>
                {props.showStar && (
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        numberOfLines={1}
                        style={stylesTicket.txtStart}
                    />
                )}
            </MyText>
            <TouchableOpacity
                onPress={() => {
                    props.onPress();
                }}
                style={[
                    styles.viewTextInput,
                    {
                        flexDirection: 'row',
                        borderWidth: 1,
                        height: Mixins.scale(56),
                        borderColor: Colors.DARK_ORANGE_15,
                        justifyContent: 'space-between',
                        alignItems: 'center'
                    }
                ]}>
                <MyText
                    text={
                        props.text
                            ? props.text
                            : `${translate('please_choose')} ${props.title}`
                    }
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: props.text
                            ? Colors.GRAYF10
                            : Colors.GRAY_PLACEHODER,
                        marginLeft: Mixins.scale(16),
                        marginRight: Mixins.scale(8),
                        flex: 9
                    }}
                />
                <Image
                    source={{ uri: 'ic_down' }}
                    style={{
                        flex: 1,
                        marginRight: Mixins.scale(12),
                        height: Mixins.scale(24),
                        width: Mixins.scale(24),
                        resizeMode: 'contain'
                    }}
                />
            </TouchableOpacity>
        </View>
    );
};
const ViewText = (props) => {
    return (
        <View
            style={[
                props.style,
                {
                    marginBottom: Mixins.scale(32)
                }
            ]}>
            <MyText
                text={props.title}
                addSize={2}
                typeFont="medium"
                style={{
                    color: Colors.GRAYF10
                }}></MyText>
            {props.multiline ? (
                <View
                    style={[
                        styles.viewTextInput,
                        {
                            flexDirection: 'row',
                            //height: Mixins.scale(56),
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }
                    ]}>
                    <ScrollView nestedScrollEnabled={true}>
                        <TooltipCopyPaste
                            dataCopy={props.text}
                            children={() => {
                                return (
                                    <ShowMoreText
                                        content={props.text}
                                        maxLine={5}
                                        styleText={{ fontSize: 16 }}
                                        lineHeight={18}
                                        onPress={() => {
                                            console.log('PRess');
                                            // this.setState((prevState) => ({
                                            //     showReadMore:
                                            //         !prevState.showReadMore
                                            // }));
                                        }}
                                    />
                                );
                            }}
                        />
                    </ScrollView>
                </View>
            ) : (
                <View
                    style={[
                        styles.viewTextInput,
                        {
                            flexDirection: 'row',
                            borderWidth: 1,
                            paddingVertical: Mixins.scale(14),
                            borderColor: Colors.DARK_ORANGE_15,
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }
                    ]}>
                    <MyText
                        addSize={2}
                        multiline={true}
                        text={props.text}
                        style={{
                            color: Colors.GRAYF10,
                            marginLeft: Mixins.scale(16),
                            marginRight: Mixins.scale(8)
                        }}
                    />
                </View>
            )}
        </View>
    );
};
const ChooseClock = (props) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                marginBottom: Mixins.scale(32),
                width: '100%',
                justifyContent: 'space-between',
                alignItems: 'center'
            }}>
            <View style={{ flexDirection: 'row' }}>
                <Image
                    source={{ uri: 'ic_sync' }}
                    style={{
                        height: Mixins.scale(24),
                        width: Mixins.scale(24),
                        resizeMode: 'contain'
                    }}
                />
                <MyText
                    text={translate('time')}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}
                />
            </View>
            <View style={{ flexDirection: 'row' }}>
                <TouchableOpacity onPress={() => props.onChangeTimeFrom()}>
                    <MyText text={moment(props.timeFrom).format('HH:mm')} />
                </TouchableOpacity>
                <MyText text={' - '} />
                <TouchableOpacity onPress={() => props.onChangeTimeTo()}>
                    <MyText text={moment(props.timeTo).format('HH:mm')} />
                </TouchableOpacity>
            </View>
        </View>
    );
};
const RadioButton = (props) => {
    return (
        <TouchableOpacity
            onPress={() => props.onTick(props.index)}
            style={{
                flexDirection: 'row',
                marginLeft: Mixins.scale(32),
                alignItems: 'center'
            }}>
            <View
                style={{
                    height: Mixins.scale(16),
                    width: Mixins.scale(16),
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderWidth: 1,
                    borderColor: Colors.DARK_BLUE_60,
                    borderRadius: Mixins.scale(8),
                    marginRight: Mixins.scale(8)
                }}>
                {props.isTick && (
                    <View
                        style={{
                            borderRadius: Mixins.scale(12),
                            backgroundColor: Colors.DARK_BLUE_60,
                            height: Mixins.scale(10),
                            width: Mixins.scale(10)
                        }}></View>
                )}
                <View
                    style={{
                        backgroundColor: Colors.DARK_BLUE_60
                    }}></View>
            </View>
            <MyText
                text={props.text}
                addSize={2}
                typeFont="medium"
                style={{
                    color: Colors.GRAYF10
                }}
            />
        </TouchableOpacity>
    );
};
const ChooseForm = (props) => {
    return (
        <View
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginBottom: Mixins.scale(32)
            }}>
            <MyText
                text={translate('store_type')}
                addSize={2}
                typeFont="medium"
                style={{
                    color: Colors.GRAYF10
                }}>
                <MyText
                    text="* "
                    addSize={2}
                    typeFont="medium"
                    numberOfLines={1}
                    style={stylesTicket.txtStart}
                />
            </MyText>
            <RadioButton
                text={translate('supermarket')}
                isTick={props.choose === 0}
                index={0}
                onTick={props.onTick}
            />
            <RadioButton
                text={translate('warehouse')}
                isTick={props.choose === 1}
                index={1}
                onTick={props.onTick}
            />
        </View>
    );
};
const DropdownType = (props) => {
    const { listTemplateTask } = props;
    return (
        <View
            style={[
                props.style,
                {
                    marginBottom: Mixins.scale(32)
                }
            ]}>
            <MyText
                text={props.title}
                addSize={2}
                typeFont="medium"
                style={{
                    color: Colors.GRAYF10
                }}>
                {props.showStar && (
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        numberOfLines={1}
                        style={stylesTicket.txtStart}
                    />
                )}
            </MyText>
            <DropdownComponent
                maxHeight={160}
                onChange={(item) => props.onChangeItem(item)}
                style={{
                    borderRadius: Mixins.scale(16),
                    borderColor: Colors.GRAYF4,
                    borderWidth: 1,
                    height: Mixins.scale(56),
                    marginTop: Mixins.scale(16),
                    paddingHorizontal: Mixins.scale(16)
                }}
                placeholderStyle={{
                    color: Colors.GRAYF9
                }}
                value={props.selectedTemplate}
                data={listTemplateTask}
                search={false}
                placeholder={translate('alert_select_job')}
                isKeyboardShow={false}
                selectedTextProps={{
                    numberOfLines: 1
                }}
                renderItem={(item1) => {
                    return (
                        <View
                            style={{
                                height: Mixins.scale(56),
                                paddingHorizontal: Mixins.scale(8),
                                justifyContent: 'center'
                            }}>
                            <MyText
                                numberOfLines={1}
                                style={{
                                    color: Colors.BLACK
                                }}
                                addSize={2}
                                text={item1.name}
                            />
                        </View>
                    );
                }}
                itemTextStyle={{
                    fontSize: 14,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    borderColor: helper.IsValidateObject(props.selectedTemplate)
                        ? Colors.DARK_BLUE_60
                        : Colors.GRAYF4,
                    borderRadius: Mixins.scale(12),
                    borderWidth: 1,
                    backgroundColor: Colors.WHITE
                }}
                itemContainerStyle={{
                    borderColor: helper.IsValidateObject(props.selectedTemplate)
                        ? Colors.DARK_BLUE_60
                        : Colors.GRAYF4,
                    borderRadius: Mixins.scale(12)
                }}
                labelField="name"
                valueField="id"
            />
        </View>
    );
};
const RenderTagDetail = (props) => {
    const { data } = props;
    console.log(data);
    return (
        <View
            style={{
                flexDirection: 'row',
                marginTop: Mixins.scale(16),
                marginBottom: Mixins.scale(32)
            }}>
            <Image
                style={{
                    height: Mixins.scale(24),
                    width: Mixins.scale(24),
                    resizeMode: 'center',
                    marginRight: Mixins.scale(16),
                    tintColor: Colors.DARK_BLUE_60
                }}
                source={{ uri: 'tag' }}
            />
            <View
                style={{
                    borderRadius: Mixins.scale(8),

                    backgroundColor: data.done
                        ? Colors.LIGHT_GREEN_10
                        : Colors.DARK_BLUE_10,
                    alignItems: 'center'
                }}>
                <MyText
                    text={
                        data.done ? translate('finish') : translate('unfinish')
                    }
                    addSize={-1}
                    style={{
                        paddingVertical: Mixins.scale(4),
                        paddingHorizontal: Mixins.scale(8),
                        alignSelf: 'center',
                        color: data.done
                            ? Colors.LIGHT_GREEN_50
                            : Colors.DARK_BLUE_50
                    }}
                />
            </View>
        </View>
    );
};
const RenderFile = (props) => {
    const [loadingMap, setLoadingMap] = useState({});
    const handleLoadStart = (itemIndex) => {
        setLoadingMap((prevLoadingMap) => ({
            ...prevLoadingMap,
            [itemIndex]: true
        }));
    };

    const handleLoadEnd = (itemIndex) => {
        setLoadingMap((prevLoadingMap) => ({
            ...prevLoadingMap,
            [itemIndex]: false
        }));
    };
    const renderImage = ({ item, index }) => {
        return (
            <View>
                {loadingMap[index] && (
                    <ActivityIndicator
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                        size="large"
                        color={Colors.BLUE_MAIN}
                    />
                )}
                <Image
                    style={[
                        style.imgFile,
                        { backgroundColor: Colors.GRAY_LIGHT }
                    ]}
                    source={{
                        uri: props.createTask
                            ? item.path
                            : `${CONST_API.TICKET_URL}task/file/${props.streamToken?.data?.token}/${item?.id}?original=true&download=false`
                    }}
                    onError={() => {
                        Toast.show({
                            type: 'error',
                            text1: translate('error_connect'),
                            position: 'bottom'
                        });
                    }}
                    onLoadStart={() => handleLoadStart(index)}
                    onLoadEnd={() => handleLoadEnd(index)}
                />
            </View>
        );
    };
    return (
        <View style={{ marginBottom: Mixins.scale(32) }}>
            <View style={style.viewRow}>
                <View style={style.viewRowBottom}>
                    <Image
                        style={{ height: 20, width: 20 }}
                        resizeMode="contain"
                        source={{ uri: 'ic_attatch' }}
                    />

                    <MyText
                        text={translate('attached_file')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginLeft: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                {!props.hideAddFile && !props.onlyImage && (
                    <DropdownComponent
                        data={[
                            {
                                name: translate('upload'),
                                id: 0
                            },
                            {
                                name: translate('upload_file'),
                                id: 2
                            }
                        ]}
                        disable={props.disable}
                        activeColor={Colors.WHITE}
                        showsVerticalScrollIndicator={false}
                        itemTextStyle={{
                            fontSize: 14,
                            color: Colors.BLACK
                        }}
                        maxHeight={Mixins.scale(140)}
                        itemContainerStyle={{
                            justifyContent: 'center'
                        }}
                        renderItem={(item1) => {
                            return (
                                <View
                                    style={{
                                        height: Mixins.scale(40),
                                        paddingHorizontal: Mixins.scale(8),
                                        justifyContent: 'center',
                                        borderRadius: Mixins.scale(12),
                                        width:
                                            props.currentLang === 'vi'
                                                ? Mixins.scale(130)
                                                : Mixins.scale(150)
                                    }}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        containerStyle={{
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        style={{
                            alignItems: 'flex-start',
                            height: Mixins.scale(24),
                            width:
                                props.currentLang === 'vi'
                                    ? Mixins.scale(130)
                                    : Mixins.scale(150)
                        }}
                        // isOnlyIcon={true}
                        placeholder=""
                        renderRightIcon={() => {
                            return props.disable ? null : (
                                <View
                                    style={{
                                        justifyContent: 'flex-end'
                                    }}>
                                    <Image
                                        style={style.imgPlus}
                                        resizeMode="contain"
                                        source={{ uri: 'ic_plus_square' }}
                                    />
                                </View>
                            );
                        }}
                        valueField="id"
                        onChange={props.handleUpFile}
                    />
                )}
                {props.onlyImage && (
                    <DropdownComponent
                        data={[
                            {
                                name: props.translate('upload_video'),
                                id: 0
                            },
                            {
                                name: props.translate('takepic'),
                                id: 2
                            }
                        ]}
                        activeColor={Colors.WHITE}
                        showsVerticalScrollIndicator={false}
                        itemTextStyle={{
                            fontSize: 14,
                            color: Colors.BLACK
                        }}
                        maxHeight={Mixins.scale(140)}
                        itemContainerStyle={{
                            justifyContent: 'center'
                        }}
                        renderItem={(item1) => {
                            return (
                                <View
                                    style={{
                                        height: Mixins.scale(40),
                                        paddingHorizontal: Mixins.scale(8),
                                        justifyContent: 'center',
                                        borderRadius: Mixins.scale(12),
                                        width:
                                            props.currentLang === 'vi'
                                                ? Mixins.scale(130)
                                                : Mixins.scale(150)
                                    }}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        containerStyle={{
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        style={{
                            alignItems: 'flex-start',
                            height: Mixins.scale(24),
                            width:
                                props.currentLang === 'vi'
                                    ? Mixins.scale(130)
                                    : Mixins.scale(150)
                        }}
                        // isOnlyIcon={true}
                        placeholder=""
                        renderRightIcon={() => {
                            return props.disable ? null : (
                                <View
                                    style={{
                                        justifyContent: 'flex-end'
                                    }}>
                                    <Image
                                        style={style.imgPlus}
                                        resizeMode="contain"
                                        source={{ uri: 'ic_plus_square' }}
                                    />
                                </View>
                            );
                        }}
                        valueField="id"
                        onChange={props.handleUpFile}
                    />
                )}
            </View>

            <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={props.listFileTicket}
                renderItem={({ item, index }) => {
                    const checkMime = (item1, type) => {
                        if (
                            item1?.filemime?.includes(type) ||
                            item1?.type?.includes(type) ||
                            item1?.mime?.includes(type)
                        )
                            return true;
                        return false;
                    };
                    return (
                        <TouchableOpacity
                            onPress={() => {
                                props.setViewMedia(index);
                            }}
                            key={item.id}
                            style={stylesTicket.touchItemFile}>
                            {props.isShowRemove && (
                                <TouchableOpacity
                                    onPress={() => {
                                        props.handleRemoveFile(item);
                                    }}
                                    style={{
                                        position: 'absolute',
                                        right: -5,
                                        top: -5,
                                        zIndex: 10
                                    }}>
                                    <Image
                                        style={{
                                            height: Mixins.scale(16),
                                            width: Mixins.scale(16)
                                        }}
                                        source={{ uri: 'ic_error_ticket' }}
                                    />
                                </TouchableOpacity>
                            )}
                            {checkMime(item, 'image') ? (
                                renderImage({ item, index })
                            ) : (
                                <View
                                    style={[
                                        style.imgFile,
                                        {
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            paddingBottom: 10
                                        }
                                    ]}>
                                    {checkMime(item, 'video') ? (
                                        <View style={style.btnVideo}>
                                            <AntDesign
                                                name="caretright"
                                                size={20}
                                                color={Colors.DARK_BLUE_60}
                                            />
                                        </View>
                                    ) : (
                                        <Image
                                            style={{
                                                height: 60,
                                                width: 60,
                                                marginBottom: 8
                                            }}
                                            source={{
                                                uri: checkFileMime(item)
                                            }}
                                        />
                                    )}

                                    <MyText
                                        numberOfLines={1}
                                        addSize={-2}
                                        text={
                                            item.filename
                                                ? item.filename
                                                : item.name
                                        }
                                    />
                                </View>
                            )}
                        </TouchableOpacity>
                    );
                }}
                keyExtractor={(item, index) => index.toString()}
            />
        </View>
    );
};
const RenderStatus = (props) => {
    console.log('isdone', props.isDone);
    const data = [
        { id: 0, name: translate('unfinish') },
        { id: 1, name: translate('finish') }
    ];
    return (
        <View style={{ marginVertical: Mixins.scale(32) }}>
            <MyText
                text={translate('status')}
                addSize={1}
                style={{
                    color: Colors.BLACK_HEADER_TITLE
                }}
                typeFont="medium"
            />
            <DropdownComponent
                selectedTextProps={{
                    numberOfLines: 1
                }}
                dropdownPosition={'top'}
                iconStyle={{
                    height: Mixins.scale(28),
                    marginRight: Mixins.scale(8),
                    tintColor: helper.IsValidateObject(props.isDone)
                        ? Colors.LIGHT_GREEN_50
                        : Colors.DARK_BLUE_50,
                    width: Mixins.scale(28)
                }}
                selectedTextStyle={{
                    color: helper.IsValidateObject(props.isDone)
                        ? Colors.LIGHT_GREEN_50
                        : Colors.DARK_BLUE_50,
                    fontSize: 16,
                    textAlign: 'left'
                }}
                showsVerticalScrollIndicator={false}
                data={data}
                search={false}
                keyboardAvoiding={false}
                isKeyboardShow={false}
                style={[
                    stylesTicket.btnDropdownPriority,
                    {
                        backgroundColor: helper.IsValidateObject(
                            props.isDone.id === 1
                        )
                            ? Colors.LIGHT_GREEN_10
                            : Colors.DARK_BLUE_10
                    }
                ]}
                onChange={(item) => {
                    props.onChangeItem(item);
                }}
                placeholderStyle={stylesTicket.txtLabel}
                maxHeight={Mixins.scale(140)}
                itemContainerStyle={{
                    justifyContent: 'center'
                }}
                value={props.isDone}
                itemTextStyle={{
                    fontSize: 16,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    backgroundColor: Colors.WHITE,
                    borderRadius: Mixins.scale(12)
                }}
                labelField="name"
                valueField="id"
            />
        </View>
    );
};
export {
    RenderFile,
    InputText,
    DropdownInput,
    ChooseClock,
    ChooseForm,
    ViewText,
    DropdownType,
    RenderTagDetail,
    RenderStatus
};
const styles = StyleSheet.create({
    viewTextInput: {
        borderRadius: Mixins.scale(16),
        marginVertical: Mixins.scale(8),
        width: '100%'
    }
});
const style = StyleSheet.create({
    btnVideo: {
        alignItems: 'center',
        borderColor: Colors.DARK_BLUE_60,
        borderRadius: 8,
        borderWidth: 1,
        height: Mixins.scale(35),
        justifyContent: 'center',
        marginBottom: 12,
        width: Mixins.scale(45)
    },

    imgFile: {
        borderRadius: 8,
        height: Mixins.scale(86),
        width: Mixins.scale(86)
    },

    imgPlus: {
        height: Mixins.scale(24),
        width: Mixins.scale(24)
    },
    viewRow: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    }
});
