import {
    FlatList,
    Image,
    StyleSheet,
    View,
    TouchableOpacity,
    Dimensions,
    Platform
} from 'react-native';
import { RNCamera } from 'react-native-camera';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { dateHelper, helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
const { translate } = global.props.getTranslateConfig();
import { ThemeXwork } from '@mwg-sdk/styles';
import { useState } from 'react';
import { formatMoney } from '../../../../utility';
import { useDispatch, useSelector } from 'react-redux';
import Toast from 'react-native-toast-message';
import {
    actionApproveTicket,
    actionRatingUser,
    createTicket,
    uploadImage
} from '../../action';
import { Modal } from '../../../../modal';
import { actionCancelRequestApprove } from '../action';
import { HOST_MEDIA_FILE } from '../../../../constant/apiConstant';
import BaseModal from '../../Components/BaseModal';
import { DecryptAES } from '../../Components/DecryptAES';

const GET_WIDTH = Dimensions.get('window').width;
const CAM_VIEW_HEIGHT = Mixins.scale(300);
const CAM_VIEW_WIDTH = Mixins.scale(300);

const InformationPaymentApprove = (props) => {
    const { data } = props;
    return (
        <View>
            <View style={style.styleRow}>
                <View style={style.viewRowBottom}>
                    <Image
                        style={[
                            style.imgLocation,
                            { tintColor: Colors.DARK_BLUE_60 }
                        ]}
                        source={{ uri: 'ic_calendar' }}
                    />
                    <MyText
                        text={translate('date_actual_submission')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginLeft: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                <MyText
                    text={`${dateHelper.getToday(
                        data?.ticket?.extraDataJsonObject?.paymentDateTime
                    )}`}
                    addSize={1}
                    style={style.txtDefault}
                />
            </View>
            <View style={style.styleRow}>
                <View style={style.viewRowBottom}>
                    <Image
                        style={style.imgLocation}
                        resizeMode="contain"
                        source={{ uri: 'ic_location_ticket' }}
                    />
                    <MyText
                        text={translate('location')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginHorizontal: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                    <MyText
                        text={`${data?.ticket?.locationGeoName || ''}`}
                        numberOfLines={1}
                        addSize={1}
                        style={style.txtDefault}
                    />
                </View>
            </View>
            <View style={style.styleRow}>
                <View style={style.viewRowBottom}>
                    <Image
                        style={style.imgLocation}
                        resizeMode="contain"
                        source={require('../../../../assets/images/ic_money.png')}
                    />
                    <MyText
                        text={translate('amount_paid')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginLeft: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                <MyText
                    text={`${formatMoney(
                        data?.ticket?.extraDataJsonObject?.totalAmount
                    )}`}
                    addSize={1}
                    style={{ color: Colors.RED }}
                />
            </View>
            <View
                style={[
                    style.viewRowBottom,
                    {
                        marginTop: Mixins.scale(24),
                        marginBottom: Mixins.scale(8)
                    }
                ]}>
                <MyText
                    text={translate('list_attachments')}
                    addSize={1}
                    style={style.txtDefault}
                    typeFont="medium"
                />
            </View>
            <FlatList
                data={props.listFileTicket}
                renderItem={({ item, index }) => {
                    return (
                        <TouchableOpacity
                            onPress={() => {
                                props.setViewMedia(index);
                            }}
                            key={item.id}
                            style={style.btnFile}>
                            <Image
                                style={[
                                    style.imgLocation,
                                    { tintColor: Colors.BLACK, marginRight: 8 }
                                ]}
                                source={{ uri: 'ic_attatch' }}
                            />
                            <MyText
                                numberOfLines={1}
                                addSize={0}
                                text={item?.filename}
                            />
                        </TouchableOpacity>
                    );
                }}
                keyExtractor={(item, index) => index.toString()}
            />
            <View style={style.viewLine} />
        </View>
    );
};

const ButtonApprove = (props) => {
    const { lableLeft, lableRight, onPress } = props;
    return (
        <View
            style={[
                style.viewRowBottom,
                {
                    justifyContent: 'space-between',
                    marginTop: Mixins.scale(44),
                    marginHorizontal: 16,
                    paddingBottom:
                        global.props.insets.bottom === 0
                            ? 16
                            : global.props.insets.bottom
                }
            ]}>
            <TouchableOpacity
                onPress={() => {
                    onPress(0);
                }}
                style={[
                    style.styleBtnApprove,
                    {
                        borderColor: Colors.DARK_BLUE_60,
                        borderWidth: 0.5,
                        marginRight: Mixins.scale(16)
                    }
                ]}>
                <MyText
                    text={lableLeft || ''}
                    addSize={2}
                    style={{ color: Colors.DARK_BLUE_60 }}
                />
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => {
                    onPress(1);
                }}
                style={[
                    style.styleBtnApprove,
                    {
                        backgroundColor: Colors.DARK_BLUE_60
                    }
                ]}>
                <MyText
                    text={lableRight || ''}
                    addSize={2}
                    style={{ color: Colors.WHITE }}
                />
            </TouchableOpacity>
        </View>
    );
};
export const RenderRating = (props) => {
    const { showRating, xworkData, detailTicket, actionRatingGetInfo } = props;
    const { imageAssets, component } = xworkData;
    const dispatch = useDispatch();
    const [showModalDetalRating, setShowModalDetalRating] = useState(false);
    const [isShowModalAssess, setShowModalAssess] = useState(false);
    const [assessSelected, setAssessSelected] = useState(null);
    const onPressSendRating = async (text) => {
        let body = {
            ticketID: detailTicket?.data?.ticket?.id,
            reason: text || '',
            satisfy: assessSelected.id === 1 ? true : false
        };
        const sendRating = await dispatch(actionRatingUser(body));
        actionRatingGetInfo();
        try {
            if (sendRating && sendRating?.object) {
                Toast.show({
                    type: 'success',
                    text1: translate('success_send_evaluation')
                });
                setShowModalAssess(false);
            } else {
                Toast.show({
                    type: 'error',
                    text1:
                        sendRating.errorReason ||
                        translate('error_send_evaluation')
                });
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('error_send_evaluation')
            });
        }
    };

    const data = [
        {
            name: translate('very_satisfied'),
            id: 1,
            img: imageAssets?.emoji
        },
        {
            name: translate('dissatisfaction'),
            id: 0,
            img: imageAssets?.emoji_1
        }
    ];

    const checkUser =
        detailTicket?.data?.ticket?.creatorId === xworkData?.fullProfile?.id ||
        detailTicket?.data?.ticket?.watcherUsers?.some(
            (e) => e?.userId === xworkData?.fullProfile?.id
        );

    /* Nếu là phiếu hỗ trợ hoàn thành và hoàn thành offline, có thời gian hoàn thành và người tạo or người liên quan đến phiếu thì hiển thị đánh giá    */
    if (
        (detailTicket?.data?.ticket?.statusId === 10 ||
            detailTicket?.data?.ticket?.statusId === 136818237) &&
        (detailTicket?.data?.ticket?.completeTime !== undefined ||
            detailTicket?.data?.ticket?.completeTimeLong !== undefined) &&
        showRating
    ) {
        return (
            <>
                {helper.hasProperty(showRating, 'object') ? (
                    <TouchableOpacity
                        onPress={() => {
                            setShowModalDetalRating(true);
                        }}
                        style={style.viewEvaluate}>
                        <View
                            style={{
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                            <View
                                hitSlop={{
                                    top: 20,
                                    bottom: 20,
                                    right: 20,
                                    left: 20
                                }}>
                                <Image
                                    style={{
                                        height: 32,
                                        width: 32
                                    }}
                                    source={
                                        showRating?.object?.isSatisfy
                                            ? imageAssets?.emoji
                                            : imageAssets?.emoji_1
                                    }
                                />
                            </View>
                            <MyText
                                text={
                                    showRating?.object?.isSatisfy
                                        ? translate('very_satisfied')
                                        : translate('dissatisfaction')
                                }
                                style={{ marginTop: 8 }}
                            />
                            {!showRating?.object?.isSatisfy && (
                                <MyText
                                    numberOfLines={2}
                                    text={`${translate('contribute_reason')}: ${
                                        showRating?.object?.reason
                                    }`}
                                    style={{ marginTop: 8 }}
                                />
                            )}
                        </View>
                    </TouchableOpacity>
                ) : (
                    <>
                        {checkUser && (
                            <View style={style.viewEvaluate}>
                                {data.map((item, index) => {
                                    return (
                                        <TouchableOpacity
                                            // disabled={
                                            //     !this.props
                                            //         .showRating
                                            // }
                                            onPress={() => {
                                                setShowModalAssess(true);
                                                setAssessSelected(item);
                                            }}
                                            key={index}
                                            style={{
                                                flex: 1,
                                                justifyContent: 'center',
                                                alignItems: 'center'
                                            }}>
                                            <Image
                                                style={{
                                                    height: 24,
                                                    width: 24
                                                }}
                                                source={item.img}
                                            />
                                            <MyText
                                                text={item.name}
                                                style={{
                                                    marginTop: 8
                                                }}
                                            />
                                        </TouchableOpacity>
                                    );
                                })}
                            </View>
                        )}
                    </>
                )}
                {isShowModalAssess && (
                    <component.ModalAssess
                        isVisible={isShowModalAssess}
                        props={props}
                        assessSelected={assessSelected}
                        onPressDimiss={() => {
                            setShowModalAssess(!isShowModalAssess);
                        }}
                        onPressSend={onPressSendRating}
                    />
                )}
                {showModalDetalRating && (
                    <Modal.ModalDetailRating
                        isVisible={showModalDetalRating}
                        data={showRating?.object}
                        imageAssets={imageAssets}
                        onPressDimiss={() => {
                            setShowModalDetalRating(false);
                        }}
                    />
                )}
            </>
        );
    }
    return null;
};
export const RenderButtonWithoutAssignee = (props) => {
    const { detailTicket, typeListData, xworkData } = props;
    const [showModalRequirements, setShowModalRequirements] = useState(false);
    const [showModalPaymentTicket, setShowModalPaymentTicket] = useState(false);
    const [
        showModalApprovePurchasingPrice,
        setShowModalApprovePurchasingPrice
    ] = useState(false);
    const [showModalApproveTMS, setShowModalApproveTMS] = useState(false);
    const [stopScanQRCode, setStopScanQRCode] = useState(false);
    const [username, setUsername] = useState('');
    const dispatch = useDispatch();
    const listCancelReason = useSelector(
        (state) => state.detailTicketReducer.listCancelReason
    );

    const checkTypelist = typeListData.data.includes(
        detailTicket?.data?.ticket?.supportServiceType
    );
    const sendMedias = (medias) => {
        const { constants } = xworkData;
        return new Promise(async (resolve) => {
            try {
                let dataImageQlity = [];
                const responeImage = await dispatch(uploadImage(medias));
                responeImage?.map((img, inddex) => {
                    dataImageQlity.push({
                        imageFilePath:
                            'https://erpapp.tgdd.vn/mwg-app-media-service/api/media/file/' +
                            img,

                        imageFileName: img,
                        filePathPublic: `BHX_IMAGE_${
                            new Date().getTime() + inddex
                        }.jpg`
                    });
                });
                resolve(dataImageQlity);
            } catch (error) {
                resolve([]);
            }
        });
    };
    const handleApproveTicket = async (body) => {
        try {
            console.log(body, '12312321321321311321231');
            let listImage = [];
            if (body.listAddImages?.length > 0) {
                listImage = await sendMedias(body.listAddImages);
            }
            console.log(listImage, 'listImagelistImagelistImage');
            const newData = {
                ...body,
                listAddImages: listImage
            };
            console.log(newData, '1232113211321323211');

            const response = await dispatch(actionApproveTicket(newData));
            if (response && response.object) {
                props.handleGlobalState();
                props.handleNewFeed(response.object, false);
                props.handleChangeTicket(response.object);
                props.updateDetailTicket();
                Toast.show({
                    type: 'success',
                    text1: body?.approve
                        ? translate('approve_ticket_success')
                        : translate('cancel_approve_ticket')
                });
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: error
            });
        }
    };
    const valiDateApprove = (id) => {
        const { data } = props.detailTicket;

        let isApprove = id === 1;
        if (id === 1) {
            global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('ask_approve_ticket'),
                confirmText: translate('skip'),
                onCancelPressed: () => {
                    global.props.alert({ show: false });
                    let body = {
                        ticketId: detailTicket?.data?.ticket.id,
                        approve: isApprove,
                        totalamount: 0
                    };
                    handleApproveTicket(body);
                },
                cancelText: translate('confirm_approve'),
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        } else {
            global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('ask_not_approve'),
                confirmText: translate('confirm_approve'),
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                    let body = {
                        ticketId: detailTicket?.data?.ticket.id,
                        approve: isApprove,
                        totalamount: 0
                    };
                    handleApproveTicket(body);
                },
                cancelText: translate('skip'),
                onCancelPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        }
    };

    const valiDateApproveTMS = (id) => {
        let isApprove = id === 1;
        if (id === 1) {
            setShowModalApproveTMS(true);
        } else {
            global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('ask_not_approve'),
                confirmText: translate('confirm_approve'),
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                    let body = {
                        ticketId: detailTicket?.data?.ticket.id,
                        approve: isApprove,
                        totalamount: 0
                    };
                    handleApproveTicket(body);
                },
                cancelText: translate('skip'),
                onCancelPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        }
    };

    const handleApproveTicketTMS = async () => {
        try {
            const newData = {
                ticketId: detailTicket?.data?.ticket.id,
                approve: true,
                assignUser: username
            };

            const response = await dispatch(actionApproveTicket(newData));
            if (response && response.object) {
                props.handleGlobalState();
                props.handleNewFeed(response.object, false);
                props.handleChangeTicket(response.object);
                Toast.show({
                    type: 'success',
                    text2: translate('approve_ticket_success')
                });
                props.updateDetailTicket();
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text2: error
            });
        }
    };

    const readQRCode = async (e) => {
        setStopScanQRCode(true);
        if (!stopScanQRCode) {
            console.log('firstLog e', e);
            const data = DecryptAES({ encryptedText: e.data });
            setUsername(data?.username);
        }
    };

    const handleShowModal = (id) => {
        if (id === 0) {
            setShowModalRequirements(true);
        } else {
            setShowModalPaymentTicket(true);
        }
    };
    const handleButtonRecieveTicket = async (assigneeUserId = -1) => {
        try {
            const params = {
                source: 'phone',
                id: detailTicket.data?.ticket?.id,
                supportServiceId: detailTicket.data?.ticket?.supportServiceId,
                subject: detailTicket.data?.ticket?.subject,
                content: detailTicket.data?.ticket?.content,
                startTimeLong: detailTicket.data?.ticket?.startTimeLong,
                dueTimeLong: detailTicket.data?.ticket?.dueTimeLong,
                createdUserId: detailTicket.data?.ticket?.creatorId ?? -1,
                assigneeUserId: assigneeUserId,
                watcherUserId:
                    detailTicket.data?.ticket?.watcherUsers &&
                    detailTicket.data?.ticket?.watcherUsers.length > 0
                        ? detailTicket.data?.ticket?.watcherUsers?.map(
                              (e) => e.userId
                          )
                        : [],
                statusId: detailTicket.data?.ticket?.statusId,
                priorityId: detailTicket.data?.ticket?.priorityId,
                locationGeoId: detailTicket.data?.ticket?.locationGeoId ?? -1,
                reasonStatus: '',
                solution: ''
            };
            global.props.showLoader();
            const response = await dispatch(createTicket(params));

            if (response) {
                props.handleNewFeed(response, false);
                props.handleChangeTicket(response);
                Toast.show({
                    type: 'success',
                    text1:
                        assigneeUserId === -1
                            ? translate('refuse_ticket')
                            : translate('accept_ticket')
                });

                await props.updateDetailTicket();
                global.props.hideLoader();
                setTimeout(async () => {
                    Toast.hide();
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1:
                    assigneeUserId === -1
                        ? translate('refuse_ticket_fail')
                        : translate('accept_ticket_fail')
            });
        }
    };
    const confirmInfomation = () => {
        global.props.showPopup({
            show: true,
            type: 'notification',
            title: translate('confirm_approval_information'),
            message: translate('confirm_approve_payment'),
            confirmText: translate('close'),
            cancelText: translate('confirm'),
            onConfirmPressed: () => {
                global.props.hidePopup({ show: false });
            },
            onCancelPressed: () => {
                global.props.hidePopup({ show: false });
                let body = {
                    ticketId: detailTicket?.data?.ticket.id,
                    approve: true
                };
                handleApproveTicket(body);
            }
        });
    };
    const submitNotApprove = (data) => {
        setShowModalApprovePurchasingPrice(false);
        global.props.showPopup({
            show: true,
            type: 'notification',
            title: `${translate('confirm_info_econtract')}!`,
            message: `${translate('reject_prict_ticket')}`,
            confirmText: translate('deny'),
            cancelText: translate('confirm'),
            onConfirmPressed: () => {
                global.props.hidePopup({ show: false });
            },
            onCancelPressed: () => {
                let body = {
                    ticketId: detailTicket?.data?.ticket.id,
                    approve: false,
                    ...data
                };
                handleApproveTicket(body);
                global.props.hidePopup({ show: false });
            }
        });
    };
    const confirmCancelRequest = () => {
        global.props.showPopup({
            show: true,
            type: 'notification',
            title: `${translate('confirm_info_econtract')}!`,
            message: `${translate('reject_prict_ticket')}`,
            confirmText: translate('deny'),
            cancelText: translate('confirm'),
            onConfirmPressed: () => {
                global.props.hidePopup({ show: false });
            },
            onCancelPressed: () => {
                handleCancelRequest();
                global.props.hidePopup({ show: false });
            }
        });
    };
    const handleCancelRequest = async () => {
        try {
            global.props.showLoader();
            const response = await dispatch(
                actionCancelRequestApprove({
                    ticketId: detailTicket?.data?.ticket.id
                })
            );
            if (response && response?.object) {
                props.handleGlobalState();
                props.handleNewFeed(response.object, false);
                props.handleChangeTicket(response.object);
                global.props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: translate('cancel_approve_ticket_2')
                });
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: response?.errorReason
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: error
            });
        }
    };
    const handleApprovePurchasingPrice = (id) => {
        if (id === 1) {
            return setShowModalApprovePurchasingPrice(true);
        } else {
            global.props.showPopup({
                show: true,
                type: 'notification',
                title: `${translate('confirm_info_econtract')}!`,
                message: `${translate('confirm_price_ticket')}`,
                confirmText: translate('deny'),
                cancelText: translate('confirm'),
                onConfirmPressed: () => {
                    global.props.hidePopup({ show: false });
                },
                onCancelPressed: () => {
                    global.props.hidePopup({ show: false });
                    let body = {
                        ticketId: detailTicket?.data?.ticket.id,
                        approve: true
                    };
                    handleApproveTicket(body);
                }
            });
        }
    };

    const clearQRCode = () => {
        setStopScanQRCode(false);
        setUsername('');
    };

    const scanQRCode = () => {
        return Platform.OS === 'android' ? (
            <View
                style={{
                    alignItems: 'center',
                    height: Mixins.scale(300),
                    width: Mixins.scale(300),
                    overflow: 'hidden'
                }}>
                <RNCamera
                    style={{
                        width: CAM_VIEW_WIDTH,
                        height: CAM_VIEW_HEIGHT
                    }}
                    rectOfInterest={{
                        x: 0.3,
                        y: 0.35,
                        width: 0.5,
                        height: 0.4
                    }}
                    cameraViewDimensions={{
                        width: Mixins.scale(300),
                        height: Mixins.scale(300)
                    }}
                    captureAudio={false}
                    zoom={0.1}
                    androidCameraPermissionOptions={{
                        title: 'Permission to use camera',
                        message: 'We need your permission to use your camera',
                        buttonPositive: 'Ok',
                        buttonNegative: 'Cancel'
                    }}
                    barCodeTypes={[
                        RNCamera.Constants.BarCodeType.qr,
                        RNCamera.Constants.BarCodeType.code128,
                        RNCamera.Constants.BarCodeType.code39
                    ]}
                    onBarCodeRead={readQRCode}
                    autoFocus={'on'}
                    defaultVideoQuality={
                        RNCamera.Constants.VideoQuality['1080p']
                    }
                    focusDepth={0.5}>
                    <View
                        style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: CAM_VIEW_WIDTH,
                            height: CAM_VIEW_HEIGHT,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <Image
                            isLocal
                            source={{ uri: 'ic_qrscan' }}
                            style={[
                                Mixins.scaleImage(200, 200),
                                { opacity: 0.7 }
                            ]}
                            resizeMode="contain"
                        />
                    </View>
                </RNCamera>
            </View>
        ) : (
            <View
                style={{
                    alignItems: 'center',
                    marginTop: Mixins.scale(24)
                }}>
                <RNCamera
                    style={{
                        width: CAM_VIEW_WIDTH,
                        height: CAM_VIEW_HEIGHT,
                        overflow: 'hidden'
                    }}
                    rectOfInterest={{
                        x: 0.3,
                        y: 0.35,
                        width: 0.5,
                        height: 0.4
                    }}
                    cameraViewDimensions={{
                        width: CAM_VIEW_WIDTH,
                        height: CAM_VIEW_HEIGHT
                    }}
                    captureAudio={false}
                    whiteBalance={RNCamera.Constants.WhiteBalance.auto}
                    zoom={0.01}
                    barCodeTypes={[
                        RNCamera.Constants.BarCodeType.qr,
                        RNCamera.Constants.BarCodeType.code128,
                        RNCamera.Constants.BarCodeType.code39
                    ]}
                    onBarCodeRead={readQRCode}
                    autoFocus={'on'}
                    defaultVideoQuality={
                        RNCamera.Constants.VideoQuality['1080p']
                    }
                    focusDepth={0.5}>
                    <View
                        style={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            width: CAM_VIEW_HEIGHT,
                            height: CAM_VIEW_HEIGHT,
                            justifyContent: 'center',
                            alignItems: 'center',
                            borderRadius: Mixins.scale(24)
                        }}>
                        <Image
                            isLocal
                            source={{ uri: 'ic_qrscan' }}
                            style={[
                                Mixins.scaleImage(200, 200),
                                { opacity: 0.7 }
                            ]}
                            resizeMode="contain"
                        />
                    </View>
                </RNCamera>
            </View>
        );
    };

    const contentScanQRCode = () => {
        return (
            <View style={{ width: '100%' }}>
                <MyText
                    text={`Mã danh tính: ${username}`}
                    addSize={2}
                    style={{ color: Colors.BLACK, textAlign: 'center' }}
                />
                <View
                    style={{
                        paddingVertical: Mixins.scale(16),
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        gap: Mixins.scale(16)
                    }}>
                    <TouchableOpacity
                        onPress={clearQRCode}
                        style={{
                            flex: 1,
                            height: Mixins.scale(56),
                            borderColor: Colors.DARK_ORANGE_15,
                            borderWidth: 1,
                            borderRadius: Mixins.scale(16),
                            paddingHorizontal: Mixins.scale(16),
                            paddingVertical: Mixins.scale(8),
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                        <MyText
                            text={'Quét lại'}
                            addSize={2}
                            style={{ color: Colors.GRAYF9 }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => {
                            setShowModalApproveTMS(false);
                            handleApproveTicketTMS();
                            clearQRCode();
                        }}
                        style={{
                            flex: 1,
                            height: Mixins.scale(56),
                            backgroundColor: Colors.DARK_BLUE_60,
                            borderRadius: Mixins.scale(16),
                            paddingHorizontal: Mixins.scale(16),
                            paddingVertical: Mixins.scale(8),
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                        <MyText
                            text={'Duyệt phiếu'}
                            addSize={2}
                            style={{ color: Colors.WHITE }}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    const confirmRequest = (data, bank) => {
        global.props.showPopup({
            show: true,
            type: 'notification',
            title: `${translate('confirm_request')}`,
            message: translate('confirm_approve_mb'),
            confirmText: translate('close'),
            cancelText: translate('confirm'),
            onConfirmPressed: () => {
                global.props.hidePopup({ show: false });
            },
            onCancelPressed: () => {
                let body = {
                    ticketId: detailTicket?.data?.ticket.id,
                    approve: data?.cancelReason === 'WRONGBANK' ? true : false,
                    newbankID: bank ? bank?.bankId : '',
                    newAccountBank: bank ? bank?.bankAccount : '',
                    reasonCancel: data?.cancelReason || ''
                };

                handleApproveTicket(body);
                global.props.hidePopup({ show: false });
            }
        });
    };
    /////Render Button Nhận Phiếu

    if (
        !helper.hasProperty(detailTicket.data.ticket, 'assignerId') &&
        !checkTypelist
    ) {
        return (
            <View
                style={{
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    paddingHorizontal: 16,
                    paddingBottom:
                        global.props.insets.bottom === 0
                            ? 16
                            : global.props.insets.bottom
                }}>
                <TouchableOpacity
                    style={style.buttonGetTicket}
                    onPress={() =>
                        handleButtonRecieveTicket(xworkData?.fullProfile?.id)
                    }>
                    <MyText
                        text={translate('recieve_ticket')}
                        addSize={2}
                        style={{ color: Colors.WHITE }}
                        typeFont="semiBold"
                    />
                </TouchableOpacity>
            </View>
        );
    }
    /////Render Button Nhận Phiếu MB
    //////
    if (
        detailTicket?.data.approveType === 'PENDING' &&
        checkTypelist &&
        detailTicket.data?.ticket?.supportServiceType === 'APPROVE_MBBANK'
    ) {
        if (
            helper.hasProperty(
                detailTicket.data?.ticket?.extraDataJsonObject,
                'hiddenButton'
            ) &&
            detailTicket.data?.ticket?.extraDataJsonObject.hiddenButton
        ) {
            return null;
        }
        if (
            helper.hasProperty(
                detailTicket.data?.ticket?.extraDataJsonObject,
                'buttonCancelRequest'
            ) &&
            detailTicket.data?.ticket?.extraDataJsonObject?.buttonCancelRequest
        )
            return (
                <View
                    style={{
                        flexDirection: 'row',
                        justifyContent: 'space-between',
                        paddingHorizontal: 16,
                        paddingBottom:
                            global.props.insets.bottom === 0
                                ? 16
                                : global.props.insets.bottom
                    }}>
                    <TouchableOpacity
                        style={style.buttonGetTicket}
                        onPress={() => confirmCancelRequest()}>
                        <MyText
                            text={translate('cancel_request_bt')}
                            addSize={2}
                            style={{ color: Colors.WHITE }}
                            typeFont="semiBold"
                        />
                    </TouchableOpacity>
                </View>
            );
        return (
            <>
                <ButtonApprove
                    lableLeft={translate('requirements')}
                    lableRight={translate('approve_request')}
                    onPress={handleShowModal}
                />
                {showModalPaymentTicket && (
                    <Modal.ModalPaymentTicket
                        isVisible={showModalPaymentTicket}
                        xworkData={xworkData}
                        titleHeader={translate('header_modal_payment')}
                        content={translate('content_modal_payment')}
                        onPressDimiss={() => {
                            setShowModalPaymentTicket(false);
                        }}
                        totalAmountTicket={
                            detailTicket?.data?.ticket?.extraDataJsonObject
                                ?.totalAmount
                        }
                        onPressConfirm={confirmInfomation}
                    />
                )}
                {showModalRequirements && (
                    <Modal.ModalRequirements
                        isVisible={showModalRequirements}
                        xworkData={xworkData}
                        titleModal={translate('requirements')}
                        onPressDimiss={() => {
                            setShowModalRequirements(false);
                        }}
                        onPressConfirm={confirmRequest}
                        data={listCancelReason?.data}
                    />
                )}
            </>
        );
    }

    if (
        detailTicket?.data.approveType === 'PENDING' &&
        checkTypelist &&
        detailTicket.data?.ticket?.supportServiceType ===
            'APPROVE_PURCHASING_PRICE'
    ) {
        return (
            <>
                <ButtonApprove
                    lableLeft={translate('browse_pricing')}
                    lableRight={translate('price_rejection')}
                    onPress={handleApprovePurchasingPrice}
                />
                {showModalApprovePurchasingPrice && (
                    <Modal.ModalApprovePurchasing
                        isVisible={showModalApprovePurchasingPrice}
                        titleHeader={translate('header_modal_payment')}
                        content={translate('content_modal_payment')}
                        onPressDimiss={() => {
                            setShowModalApprovePurchasingPrice(false);
                        }}
                        totalAmountTicket={
                            detailTicket?.data?.ticket?.extraDataJsonObject
                                ?.totalAmount
                        }
                        onPressConfirm={submitNotApprove}
                    />
                )}
            </>
        );
    }
    // Duyệt phiếu tận tâm (TMS) mở quét mã danh tính
    if (
        detailTicket?.data?.ticket?.supportServiceType === 'APPROVE_TAN_TAM' &&
        detailTicket?.data?.ticket?.extraDataJsonObject?.approveLevel === 2 &&
        detailTicket?.data.approveType === 'PENDING' &&
        checkTypelist
    ) {
        return (
            <>
                <ButtonApprove
                    lableLeft={translate('not_approve')}
                    lableRight={translate('approve_ticket')}
                    onPress={valiDateApproveTMS}
                />
                <BaseModal
                    isVisible={showModalApproveTMS}
                    onClose={() => {
                        setShowModalApproveTMS(false);
                    }}>
                    <View style={style.modalContainer}>
                        <View style={style.contentContainer}>
                            <View
                                style={{
                                    alignItems: 'center',
                                    gap: Mixins.scale(16)
                                }}>
                                <MyText
                                    text={'Quét mã danh tính trên app MWG'}
                                    addSize={2}
                                    style={{ color: Colors.BLACK }}
                                    typeFont="semiBold"
                                />
                                {!stopScanQRCode
                                    ? scanQRCode()
                                    : contentScanQRCode()}
                            </View>
                        </View>
                    </View>
                </BaseModal>
            </>
        );
    }
    /////Render Button Momo
    if (detailTicket?.data.approveType === 'PENDING' && checkTypelist) {
        if (
            helper.hasProperty(
                detailTicket.data?.ticket?.extraDataJsonObject,
                'hiddenButton'
            ) &&
            detailTicket.data?.ticket?.extraDataJsonObject.hiddenButton
        ) {
            return null;
        }
        return (
            <ButtonApprove
                lableLeft={translate('not_approve')}
                lableRight={translate('approve_ticket')}
                onPress={valiDateApprove}
            />
        );
    }
    return null;
};

export { InformationPaymentApprove, ButtonApprove };
const style = StyleSheet.create({
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(24)
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    modalContainer: {
        backgroundColor: Colors.WHITE,
        borderRadius: Mixins.scale(24),
        width: GET_WIDTH - Mixins.scale(32)
    },
    contentContainer: {
        paddingHorizontal: Mixins.scale(24),
        paddingVertical: Mixins.scale(16)
    },
    txtDefault: {
        color: Colors.BLACK
    },
    imgLocation: {
        height: Mixins.scale(20),
        width: Mixins.scale(20)
    },
    btnFile: {
        flexDirection: 'row',
        height: 40,
        backgroundColor: '#F0F9FF',
        borderRadius: 8,
        marginTop: Mixins.scale(8),
        alignItems: 'center'
    },
    styleBtnApprove: {
        alignItems: 'center',
        borderRadius: 12,
        flex: 0.5,
        justifyContent: 'center',
        paddingVertical: Mixins.scale(16)
    },
    viewLine: {
        backgroundColor: Colors.GRAYF4,
        height: 1,
        marginVertical: Mixins.scale(24),
        width: '100%'
    },
    viewEvaluate: {
        backgroundColor: '#F7F7F7',
        borderRadius: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: 'auto',
        marginRight: 'auto',
        marginTop: 16,
        paddingHorizontal: 16,
        paddingVertical: 16,
        width: '90%'
    },
    buttonGetTicket: {
        alignItems: 'center',
        backgroundColor: ThemeXwork.primary.$500,
        borderColor: ThemeXwork.primary.$500,
        borderRadius: 12,
        borderWidth: 0.5,
        flex: 1,
        justifyContent: 'center',
        paddingVertical: Mixins.scale(16)
    }
});
