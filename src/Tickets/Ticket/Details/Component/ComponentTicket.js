import {
    FlatList,
    Image,
    View,
    TouchableOpacity,
    ActivityIndicator,
    ScrollView,
    Dimensions
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import { stylesTicket } from '../../stylesTicket';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { DropdownComponent } from '@mwg-kits/components';
const { translate } = global.props.getTranslateConfig();
import { ThemeXwork } from '@mwg-sdk/styles';
import { useState } from 'react';
import { checkFileMime, defaultTextPriority } from '../../../../utility';
import { useDispatch, useSelector } from 'react-redux';
import { CONST_API, constants } from '../../../../constant';
import { style } from './style';
import { TooltipCopyPaste } from '../../../GroupTicket/CompoentTooltip';
import { ModalSelectedHashtag } from '../../../../modal';
import * as actionTicket from '../../action';
import Toast from 'react-native-toast-message';
const RenderFile = (props) => {
    const listFileTicket = useSelector(
        (state) => state.ticketReducer.listFileTicket
    );
    const detailTicket = useSelector(
        (state) => state.ticketReducer.detailTicket
    );
    const streamToken = useSelector((state) => state.ticketReducer.streamToken);
    const { data } = detailTicket;
    let checkQuickTicket =
        helper.hasProperty(data?.ticket, 'asSocial') && data?.ticket?.asSocial;
    if (checkQuickTicket && listFileTicket?.data?.length === 0) {
        return null;
    }
    return (
        <View
            style={{
                marginTop: props.isTask ? 0 : Mixins.scale(24),
                marginBottom: props.isTask ? Mixins.scale(24) : 0
            }}>
            <View style={style.viewRow}>
                <View style={style.viewRowBottom}>
                    <Image
                        style={{ height: 20, width: 20 }}
                        resizeMode="contain"
                        source={{ uri: 'ic_attatch' }}
                    />

                    <MyText
                        text={translate('attached_file')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginLeft: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                {!props.hideAddFile &&
                    !props.onlyImage &&
                    !checkQuickTicket && (
                        <DropdownComponent
                            data={constants.dropdownUploadFile}
                            activeColor={Colors.WHITE}
                            showsVerticalScrollIndicator={false}
                            itemTextStyle={{
                                fontSize: 14,
                                color: Colors.BLACK
                            }}
                            maxHeight={Mixins.scale(140)}
                            itemContainerStyle={{
                                justifyContent: 'center'
                            }}
                            renderItem={(item1) => {
                                return (
                                    <View style={style.viewDropdownUpload}>
                                        <MyText
                                            numberOfLines={1}
                                            style={{ color: Colors.BLACK }}
                                            text={item1.name}
                                        />
                                    </View>
                                );
                            }}
                            containerStyle={{
                                backgroundColor: Colors.WHITE,
                                borderRadius: Mixins.scale(12)
                            }}
                            style={{
                                alignItems: 'flex-start',
                                height: Mixins.scale(24),
                                width: Mixins.scale(140)
                            }}
                            // isOnlyIcon={true}
                            placeholder=""
                            renderRightIcon={() => {
                                return (
                                    <View
                                        style={{
                                            justifyContent: 'flex-end'
                                        }}>
                                        <Image
                                            style={style.imgPlus}
                                            resizeMode="contain"
                                            source={{ uri: 'ic_plus_square' }}
                                        />
                                    </View>
                                );
                            }}
                            valueField="id"
                            onChange={(item) =>
                                props.handleUpFile(item, props.isCheckIn)
                            }
                        />
                    )}
                {props.onlyImage && (
                    <DropdownComponent
                        data={constants.dropdownTakePic}
                        activeColor={Colors.WHITE}
                        showsVerticalScrollIndicator={false}
                        itemTextStyle={{
                            fontSize: 14,
                            color: Colors.BLACK
                        }}
                        maxHeight={Mixins.scale(140)}
                        itemContainerStyle={{
                            justifyContent: 'center'
                        }}
                        renderItem={(item1) => {
                            return (
                                <View style={style.viewDropdownUpload}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        containerStyle={{
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        style={{
                            alignItems: 'flex-start',
                            height: Mixins.scale(24),
                            width: Mixins.scale(140)
                        }}
                        // isOnlyIcon={true}
                        placeholder=""
                        renderRightIcon={() => {
                            return (
                                <View
                                    style={{
                                        justifyContent: 'flex-end'
                                    }}>
                                    <Image
                                        style={style.imgPlus}
                                        resizeMode="contain"
                                        source={{ uri: 'ic_plus_square' }}
                                    />
                                </View>
                            );
                        }}
                        valueField="id"
                        onChange={(item) =>
                            props.handleUpFile(item, props.isCheckIn)
                        }
                    />
                )}
            </View>

            <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                data={listFileTicket?.data}
                renderItem={({ item, index }) => {
                    const url = `${CONST_API.TICKET_URL}${
                        props.isTask ? 'task/' : ''
                    }file/${streamToken?.data?.token}/${
                        item?.id
                    }?original=true&download=false`;
                    return (
                        <TouchableOpacity
                            onPress={() => {
                                props.setViewMedia(index);
                            }}
                            key={item.id}
                            style={stylesTicket.touchItemFile}>
                            {props.isShowRemove && (
                                <TouchableOpacity
                                    onPress={() => {
                                        props.handleRemoveFile(item);
                                    }}
                                    style={{
                                        position: 'absolute',
                                        right: -5,
                                        top: -5,
                                        zIndex: 10
                                    }}>
                                    <Image
                                        style={{
                                            height: Mixins.scale(16),
                                            width: Mixins.scale(16)
                                        }}
                                        source={{ uri: 'ic_error_ticket' }}
                                    />
                                </TouchableOpacity>
                            )}
                            {item.filemime?.includes('image') ? (
                                <Image
                                    style={style.imgFile}
                                    source={{
                                        uri: url
                                    }}
                                />
                            ) : (
                                <View
                                    style={[
                                        style.imgFile,
                                        {
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            paddingBottom: 10
                                        }
                                    ]}>
                                    {item.filemime?.includes('video') ? (
                                        <View style={style.btnVideo}>
                                            <AntDesign
                                                name="caretright"
                                                size={20}
                                                color={Colors.DARK_BLUE_60}
                                            />
                                        </View>
                                    ) : (
                                        <Image
                                            style={{
                                                height: 60,
                                                width: 60,
                                                marginBottom: 8
                                            }}
                                            source={{
                                                uri: checkFileMime(item)
                                            }}
                                        />
                                    )}

                                    <MyText
                                        numberOfLines={1}
                                        addSize={-2}
                                        text={item?.filename}
                                    />
                                </View>
                            )}
                        </TouchableOpacity>
                    );
                }}
                keyExtractor={(item, index) => index.toString()}
            />
        </View>
    );
};
const RenderFileCheckIn = (props) => {
    const [loading, setLoading] = useState(true);
    const renderItem = () => {
        return (
            <View>
                {loading && (
                    <ActivityIndicator
                        style={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                        size="large"
                        color={Colors.BLUE_MAIN}
                    />
                )}
                <Image
                    style={style.imgFileCheckIn}
                    onLoadEnd={() => setLoading(false)}
                    source={{
                        uri: `${CONST_API.TICKET_URL}file/${props.streamToken?.data?.token}/${props.listFileTicket}?original=true&download=false`
                    }}
                />
            </View>
        );
    };
    return (
        <View>
            <View style={style.viewRow}>
                <View
                    style={[
                        style.viewRowBottom,
                        {
                            justifyContent: 'space-between',
                            position: 'absolute'
                        }
                    ]}>
                    <DropdownComponent
                        disable={props.disable}
                        data={constants.dropdownTakePic}
                        activeColor={Colors.WHITE}
                        showsVerticalScrollIndicator={false}
                        itemTextStyle={{
                            fontSize: 14,
                            color: Colors.BLACK
                        }}
                        maxHeight={Mixins.scale(140)}
                        itemContainerStyle={{
                            justifyContent: 'center'
                        }}
                        renderItem={(item1) => {
                            return (
                                <View style={style.viewDropdownUpload}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        containerStyle={{
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        style={{
                            alignItems: 'flex-start',
                            height: Mixins.scale(24),
                            width: Mixins.scale(140)
                        }}
                        //isOnlyIcon={true}
                        placeholder=""
                        renderLeftIcon={() => {
                            return (
                                <View>
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center'
                                        }}>
                                        <Image
                                            style={{
                                                height: Mixins.scale(20),
                                                width: Mixins.scale(20)
                                            }}
                                            resizeMode="contain"
                                            source={{ uri: 'ic_image_cmt' }}
                                        />

                                        <MyText
                                            text={props.name}
                                            addSize={1}
                                            style={{
                                                color: Colors.BLACK_HEADER_TITLE,
                                                marginLeft: Mixins.scale(8)
                                            }}
                                            typeFont="medium"
                                        />
                                    </View>
                                </View>
                            );
                        }}
                        renderRightIcon={() => {
                            return props.disable ? null : (
                                <View>
                                    <Image
                                        source={{ uri: 'ic_down' }}
                                        style={{ height: 16, width: 16 }}
                                    />
                                </View>
                            );
                        }}
                        valueField="id"
                        onChange={(item) =>
                            props.handleUpFile(item, props.isCheckIn)
                        }
                    />
                </View>
            </View>
            <TouchableOpacity
                onPress={() => {
                    if (props.listFileTicket) props.setViewMedia(props.index);
                    else props.handleUpFile({ id: 0 }, props.isCheckIn);
                }}
                style={{
                    height: Mixins.scale(102),
                    width: Mixins.scale(164),
                    borderRadius: Mixins.scale(8),
                    backgroundColor: Colors.GRAYF5,
                    justifyContent: 'center',
                    marginTop: Mixins.scale(16),
                    overflow: 'hidden'
                }}>
                {props.listFileTicket ? (
                    renderItem()
                ) : (
                    <Image
                        source={{ uri: 'ic_image' }}
                        style={{
                            width: Mixins.scale(48),
                            height: Mixins.scale(48),
                            alignSelf: 'center',
                            tintColor: Colors.GRAYF7
                        }}
                    />
                )}
            </TouchableOpacity>
        </View>
    );
};
const RenderMention = (props) => {
    return (
        <View
            style={[
                {
                    height:
                        props.memberInMention?.length >= 3
                            ? Mixins.scale(150)
                            : Mixins.scale(100)
                },
                style.viewListMention
            ]}>
            <FlatList
                style={{
                    marginTop: Mixins.scale(5)
                }}
                contentContainerStyle={[
                    props.memberInMention.length === 0
                        ? {
                              justifyContent: 'center',
                              flex: 1
                          }
                        : null
                ]}
                data={props.memberInMention}
                keyboardShouldPersistTaps="handled"
                renderItem={({ item }) => {
                    return (
                        <TouchableOpacity
                            onPress={() => props.onTouchMention(item)}
                            style={style.btnOnTouchMention}>
                            <View
                                style={{
                                    flexDirection: 'row',
                                    justifyContent: 'space-between'
                                }}>
                                <MyText
                                    addSize={1}
                                    typeFont="semiBold"
                                    text={`${item.user.username} - ${item.user.profile.lastName} ${item.user.profile.firstName}`}
                                />
                                <Image
                                    source={{
                                        uri: `${CONST_API.baseAvatarURI}${item.user.profile.image}`
                                    }}
                                    style={style.img_user_memtion}
                                />
                            </View>
                        </TouchableOpacity>
                    );
                }}
                ListEmptyComponent={() => {
                    if (props.listMemberGroup.isSuccess) {
                        return (
                            <MyText
                                text={translate('no_member_ticket')}
                                typeFont="medium"
                                style={{
                                    textAlign: 'center',
                                    fontSize: 15
                                }}
                            />
                        );
                    }
                    return <ActivityIndicator size="small" color={'#9FC6FF'} />;
                }}
            />
        </View>
    );
};
const RenderMentionSelected = (props) => {
    const {
        listMemberGroup,
        mentionedMember,
        initMentionCount,
        handleRemoveMention
    } = props;
    if (
        listMemberGroup.isFetching ||
        listMemberGroup.isError ||
        helper.IsEmptyArray(mentionedMember)
    ) {
        return null;
    }
    if (mentionedMember?.length > 0) {
        return (
            <ScrollView
                // scrollEnabled={false}
                keyboardShouldPersistTaps="handled"
                showsHorizontalScrollIndicator={false}
                horizontal>
                <FlatList
                    scrollEnabled={false}
                    // horizontal={true}
                    style={{
                        height: '100%'
                    }}
                    keyboardShouldPersistTaps="handled"
                    numColumns={Math.round(initMentionCount / 2)}
                    data={mentionedMember}
                    renderItem={({ item }) => {
                        return (
                            <View
                                style={{
                                    overflow: 'visible',
                                    padding: 8
                                }}>
                                <View style={style.viewItemMention}>
                                    <MyText
                                        addSize={-1}
                                        style={{
                                            color: Colors.BLUE_MAIN,
                                            margin: 5
                                        }}
                                        text={`${item.user?.username} - ${item.user?.profile?.lastName} ${item.user?.profile?.firstName}`}
                                    />
                                </View>
                                <TouchableOpacity
                                    hitSlop={{
                                        top: 5,
                                        left: 5,
                                        right: 5,
                                        bottom: 5
                                    }}
                                    onPress={() => {
                                        handleRemoveMention(item);
                                    }}
                                    style={{
                                        zIndex: 5,
                                        position: 'absolute',
                                        right: 6,
                                        top: 3
                                    }}>
                                    <AntDesign
                                        name="closecircle"
                                        size={12}
                                        style={{
                                            color: Colors.GRAY_MEDIUM,
                                            borderRadius: 12
                                        }}
                                    />
                                </TouchableOpacity>
                            </View>
                        );
                    }}
                />
            </ScrollView>
        );
    }
    return null;
};
const RenderHeader = (props) => {
    const { onChangeStatus, onFocus, navigation, handleGlobalState } = props;
    const [isShowMoreTitle, setIsSShowMorTitle] = useState(false);
    const [isShowModalHashtag, setIsShowModalHashtag] = useState(false);
    const listHashtag = useSelector((state) => state.ticketReducer.listHashtag);
    const detailTicket = useSelector(
        (state) => state.ticketReducer.detailTicket
    );
    const xworkData = useSelector(
        (state) => state.groupTicketReducer.xworkData
    );
    const { data } = detailTicket;
    const dispatch = useDispatch();

    const handleAddHashtag = async (item) => {
        let body = {
            ticketClonedId: detailTicket?.data?.ticket?.id,
            hashtagId: item.id
        };
        global.props.showLoader();
        const response = await dispatch(actionTicket.actionCopyToGroup(body));
        try {
            if (response && response?.object) {
                handleGlobalState();
                Toast.show({
                    type: 'success',
                    text1: translate('successful_hashtag')
                });
                const data = {
                    supportServiceId:
                        detailTicket?.data?.ticket?.supportServiceId
                };
                dispatch(actionTicket.getListTicket(data));
                navigation.goBack();
                global.props.hideLoader();
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: response.errorReason || translate('Hashtag_failed')
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('Hashtag_failed')
            });
        }
    };
    return (
        <View>
            <View style={{ flexDirection: 'row' }}>
                <TooltipCopyPaste
                    dataCopy={data?.ticket?.subject}
                    children={() => {
                        return (
                            <TouchableOpacity
                                activeOpacity={1}
                                onPress={() =>
                                    setIsSShowMorTitle(!isShowMoreTitle)
                                }>
                                <MyText
                                    addSize={5}
                                    numberOfLines={isShowMoreTitle ? 0 : 2}
                                    typeFont="bold"
                                    style={{
                                        color: Colors.BLACK
                                    }}
                                    text={data?.ticket?.subject}
                                />
                            </TouchableOpacity>
                        );
                    }}
                />
            </View>
            <MyText
                numberOfLines={1}
                style={{
                    color: ThemeXwork.neutrals.$500
                }}
                text={`${translate('in_group')}: `}>
                <MyText
                    numberOfLines={1}
                    style={{
                        marginTop: Mixins.scale(20),
                        color: Colors.BLACK_HEADER_TITLE
                    }}
                    text={`${data?.ticket?.supportServiceName}`}
                />
            </MyText>

            <View style={style.viewTag}>
                <View
                    style={{
                        height: '100%',
                        marginTop: 12
                    }}>
                    <Image
                        style={{
                            height: Mixins.scale(22),
                            tintColor: Colors.DARK_BLUE_60,
                            width: Mixins.scale(22)
                        }}
                        resizeMode="contain"
                        source={{ uri: 'tag' }}
                    />
                </View>
                <View
                    style={{
                        flex: 1,
                        alignItems: 'center',
                        flexDirection: 'row',
                        flexWrap: 'wrap'
                    }}>
                    {helper.IsValidateObject(data.approveType) ? (
                        <View
                            style={{
                                backgroundColor: data.ticket?.statusColor,
                                paddingHorizontal: Mixins.scale(8),
                                marginLeft: Mixins.scale(8),
                                alignItems: 'center',
                                borderRadius: Mixins.scale(12),
                                height: Mixins.scale(30),
                                justifyContent: 'center'
                            }}>
                            <MyText
                                text={data?.ticket?.statusName}
                                style={{
                                    color: Colors.WHITE,
                                    fontWeight: '400'
                                }}
                            />
                        </View>
                    ) : (
                        <DropdownComponent
                            selectedTextProps={{
                                numberOfLines: 1
                            }}
                            disable={
                                !helper.hasProperty(data, 'allowedStatus') ||
                                data?.allowedStatus?.length === 0
                            }
                            onFocus={onFocus}
                            selectedTextStyle={style.txtLabel}
                            showsVerticalScrollIndicator={false}
                            placeholder={data?.ticket?.statusName}
                            data={data?.allowedStatus}
                            search={false}
                            keyboardAvoiding={false}
                            style={[
                                {
                                    backgroundColor: data.ticket?.statusColor
                                },
                                style.styleDropdown
                            ]}
                            onChangeStatus={onChangeStatus}
                            renderRightIcon={() => {
                                return <View></View>;
                            }}
                            placeholderStyle={style.txtLabel}
                            maxHeight={Mixins.scale(150)}
                            itemContainerStyle={{
                                justifyContent: 'center'
                            }}
                            renderItem={(item1) => {
                                return (
                                    <View
                                        style={{
                                            height: Mixins.scale(40),
                                            paddingHorizontal: Mixins.scale(8),
                                            justifyContent: 'center'
                                        }}>
                                        <MyText
                                            numberOfLines={1}
                                            style={{ color: Colors.BLACK }}
                                            addSize={-1}
                                            text={item1.name}
                                        />
                                    </View>
                                );
                            }}
                            itemTextStyle={{
                                fontSize: 14,
                                color: Colors.BLACK
                            }}
                            containerStyle={{
                                paddingVertical: Mixins.scale(4),
                                backgroundColor: Colors.WHITE,
                                borderRadius: Mixins.scale(12)
                            }}
                            value={data?.ticket?.statusName}
                            labelField="name"
                            valueField="id"
                        />
                    )}
                    {helper.hasProperty(data?.ticket, 'priorityName') &&
                        data?.ticket.supportServiceType !== 'GROUND_TASK' && (
                            <View
                                style={{
                                    backgroundColor: defaultTextPriority(
                                        data.ticket.priorityId
                                    ),
                                    paddingHorizontal: 12,
                                    paddingVertical: 6,
                                    borderRadius: 12,
                                    marginLeft: 12
                                }}>
                                <MyText
                                    numberOfLines={1}
                                    text={data?.ticket?.priorityName}
                                    addSize={-1}
                                    style={{
                                        color: Colors.WHITE
                                    }}
                                    typeFont="semiBold"
                                />
                            </View>
                        )}
                    {/* ///// */}
                    {data?.ticket?.asSocial &&
                        !data?.ticket?.completeTime &&
                        !data?.ticket?.completeTimeLong && (
                            <TouchableOpacity
                                onPress={() => {
                                    setIsShowModalHashtag(true);
                                }}
                                style={style.btnHashtags}>
                                <MyText
                                    text={translate('transferhashtags')}
                                    style={{
                                        color: Colors.WHITE
                                    }}
                                />
                            </TouchableOpacity>
                        )}
                </View>
            </View>
            {isShowModalHashtag && (
                <ModalSelectedHashtag
                    isVisible={isShowModalHashtag}
                    titleModal={translate('transferhashtags')}
                    onPressDimiss={() => {
                        setIsShowModalHashtag(false);
                    }}
                    listHashtag={listHashtag.data}
                    xworkData={xworkData}
                    handleAddStore={handleAddHashtag}
                />
            )}
        </View>
    );
};

const RenderTopHeader = (props) => {
    const { indexHeader, handleTouchHeader } = props;

    return (
        <View style={style.viewHeader}>
            {constants.listHeader?.map((item, index) => {
                const checkIndex = indexHeader === item.id;
                return (
                    <TouchableOpacity
                        onPress={() => {
                            handleTouchHeader(item.id);
                        }}
                        key={index}
                        style={[
                            style.btnHeader,
                            {
                                borderBottomWidth: checkIndex ? 1 : 0.5,
                                borderColor: checkIndex
                                    ? Colors.DARK_BLUE_60
                                    : Colors.GRAYF5
                            }
                        ]}>
                        <MyText
                            numberOfLines={1}
                            style={{
                                color: checkIndex
                                    ? Colors.DARK_BLUE_60
                                    : Colors.BLACK_HEADER_TITLE
                            }}
                            text={item.title}
                            addSize={2}
                        />
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};
const RenderListSuggestComment = (props) => {
    const { suggestComment, navigation, setCommentTicket } = props;
    return (
        <View
            style={{
                position: 'absolute',
                bottom: Mixins.scale(global.props.insets.bottom + 116),
                width: Dimensions.get('window').width - 32
            }}>
            <View style={style.viewListSuggest}>
                <ScrollView
                    style={{ maxHeight: Mixins.scale(290) }}
                    keyboardShouldPersistTaps="handled"
                    showsVerticalScrollIndicator={false}>
                    {suggestComment?.data?.map((element, index) => {
                        if (
                            helper.IsValidateObject(element) &&
                            helper.hasProperty(element, 'name') &&
                            element.name.length > 0
                        ) {
                            return (
                                <TouchableOpacity
                                    onPress={() =>
                                        setCommentTicket(element.name)
                                    }
                                    key={`${index}`}
                                    style={style.viewItemSuggest}>
                                    <MyText
                                        text={element.name}
                                        numberOfLines={2}
                                        addSize={1}
                                        style={{
                                            marginHorizontal: 12,
                                            color: ThemeXwork.neutrals.$200
                                        }}
                                    />
                                </TouchableOpacity>
                            );
                        }
                        return null;
                    })}
                </ScrollView>
                <TouchableOpacity
                    style={{
                        flexDirection: 'row',
                        height: Mixins.scale(58),
                        alignItems: 'center'
                    }}
                    onPress={() => navigation.navigate('CreateSuggestComment')}>
                    <Image
                        source={{ uri: 'ic_plus' }}
                        resizeMode="stretch"
                        style={{
                            height: Mixins.scale(20),
                            width: Mixins.scale(20),
                            tintColor: ThemeXwork.primary.$500,
                            marginLeft: 8,
                            marginRight: 8
                        }}
                    />
                    <MyText
                        text={translate('quick_reply')}
                        addSize={1}
                        style={{ color: ThemeXwork.primary.$500 }}
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
};
export const RenderUserApprove = () => {
    const detailTicket = useSelector(
        (state) => state.ticketReducer.detailTicket
    );
    const { data } = detailTicket;
    return (
        <View style={style.styleApprove}>
            <View style={style.styleRow}>
                <Image style={style.imgLeft} source={{ uri: 'ic_add_group' }} />
                <MyText
                    text={translate('approver')}
                    addSize={1}
                    style={{
                        color: Colors.BLACK_HEADER_TITLE,
                        marginHorizontal: Mixins.scale(8)
                    }}
                    typeFont="medium"
                />

                <View style={style.viewRowBottom}>
                    {data?.approver?.map((item, index) => {
                        return (
                            <View
                                style={{
                                    justifyContent: 'center',
                                    alignItems: 'center'
                                }}>
                                <Image
                                    key={index}
                                    style={{
                                        height: Mixins.scale(28),
                                        width: Mixins.scale(28),
                                        borderRadius: Mixins.scale(14)
                                    }}
                                    source={{
                                        uri: `${CONST_API.baseAvatarURI}${item?.approver.profile.image}`
                                    }}
                                />
                            </View>
                        );
                    })}
                </View>
            </View>
        </View>
    );
};
export {
    RenderFile,
    RenderMention,
    RenderMentionSelected,
    RenderHeader,
    RenderTopHeader,
    RenderListSuggestComment,
    RenderFileCheckIn
};
