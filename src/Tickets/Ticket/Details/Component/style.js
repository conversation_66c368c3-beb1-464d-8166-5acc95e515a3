import { FlatList, Image, StyleSheet } from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { ThemeXwork } from '@mwg-sdk/styles';
export const style = StyleSheet.create({
    btnHeader: {
        alignItems: 'center',
        flex: 1,
        height: Mixins.scale(50),
        justifyContent: 'center'
    },
    imgLeft: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    btnOnTouchMention: {
        flex: 1,
        marginHorizontal: 10,
        marginVertical: 5,
        padding: 5
    },

    btnVideo: {
        alignItems: 'center',
        borderColor: Colors.DARK_BLUE_60,
        borderRadius: 8,
        borderWidth: 1,
        height: Mixins.scale(35),
        justifyContent: 'center',
        marginBottom: 12,
        width: Mixins.scale(45)
    },

    imgFile: {
        borderRadius: 8,
        height: Mixins.scale(86),
        width: Mixins.scale(86)
    },
    imgFileCheckIn: {
        height: '100%',
        width: '100%'
    },
    imgPlus: {
        height: Mixins.scale(24),
        width: Mixins.scale(24)
    },
    img_user_memtion: {
        borderRadius: 12,
        height: Mixins.scale(24),
        marginRight: 10,
        paddingHorizontal: 10,
        width: Mixins.scale(24)
    },
    styleDropdown: {
        alignItems: 'center',
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(30),
        marginLeft: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(8),
        width: Mixins.scale(160)
    },
    txtLabel: {
        color: Colors.WHITE,
        fontSize: 13,
        textAlign: 'center'
    },
    viewHeader: {
        alignItems: 'center',
        borderBottomWidth: 1,
        borderColor: Colors.GRAYF5,
        borderTopWidth: 1,
        flexDirection: 'row',
        height: Mixins.scale(50),
        marginBottom: Mixins.scale(4),
        marginTop: Mixins.scale(24)
    },
    viewItemMention: {
        borderColor: Colors.GRAYF7,
        borderRadius: 8,
        borderWidth: 0.5,
        marginBottom: 5,
        marginRight: 5
    },
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(24)
    },
    viewItemSuggest: {
        borderBottomWidth: 1,
        borderColor: ThemeXwork.neutrals.$950,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        height: Mixins.scale(58),
        justifyContent: 'center'
    },
    viewListMention: {
        alignSelf: 'center',
        backgroundColor: Colors.WHITE,
        borderRadius: 12,
        elevation: 10,
        justifyContent: 'center',
        marginBottom: Mixins.scale(5),
        shadowColor: Colors.NOBEL,
        shadowOffset: {
            width: 0,
            height: 2
        },
        shadowOpacity: 0.23,
        shadowRadius: 2.62,
        width: '90%'
    },
    styleApprove: {
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    viewListSuggest: {
        backgroundColor: Colors.WHITE,
        borderColor: ThemeXwork.primary.$600,
        borderRadius: 16,
        borderStyle: 'dashed',
        borderWidth: 1,
        marginHorizontal: 16,
        marginTop: Mixins.scale(24),
        maxHeight: Mixins.scale(348),
        width: '100%'
    },
    viewRow: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between'
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    viewTag: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: 8
    },
    viewDropdownUpload: {
        height: Mixins.scale(40),
        paddingHorizontal: Mixins.scale(8),
        justifyContent: 'center',
        borderRadius: Mixins.scale(12),
        width: Mixins.scale(140)
    },
    btnHashtags: {
        backgroundColor: ThemeXwork.primary.$500,
        borderColor: ThemeXwork.primary.$500,
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(30),
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 12,
        marginLeft: Mixins.scale(12),
        marginTop: 12,
        paddingVertical: Mixins.scale(4)
    }
});
