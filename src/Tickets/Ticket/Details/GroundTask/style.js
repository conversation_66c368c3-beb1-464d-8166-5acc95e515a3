import { StyleSheet } from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';

export const infoSchedule = StyleSheet.create({
    container: {
        // flex: 1,
        // paddingBottom: 60
    },
    imgCalendar: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    imgLeft: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    imgLeftBottom: {
        height: Mixins.scale(20),
        width: Mixins.scale(20)
    },

    imgWatched: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(20),
        width: Mixins.scale(20)
    },
    styleApprove: {
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(16)
    },
    txtDefault: {
        color: Colors.BLACK
    },

    viewCreator: {
        alignItems: 'center',
        backgroundColor: Colors.WHITE,
        flexDirection: 'row',
        marginTop: Mixins.scale(16)
    },
    viewDate: {
        alignItems: 'center',
        borderRadius: 12,
        flex: 5,
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginLeft: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(4)
    },
    viewFile: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(32),
        width: '100%'
    },

    viewRow: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    }
});
export const editSchedule = StyleSheet.create({
    btnSave: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(16),
        height: Mixins.scale(56),
        justifyContent: 'center',
        width: '100%'
    },
    container: {
        flex: 1,
        justifyContent: 'space-between',
        marginHorizontal: Mixins.scale(16),
        marginVertical: Mixins.scale(24)
    },
    creator: {
        alignItems: 'center',
        flexDirection: 'row',
        // marginHorizontal: Mixins.scale(16),
        marginVertical: Mixins.scale(12)
    },
    imgCalendar: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    imgLeft: {
        height: Mixins.scale(22),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(22)
    },
    imgWatched: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(28),
        width: Mixins.scale(28)
    },
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(16)
    },
    txtDefault: {
        color: Colors.BLACK
    },
    viewDate: {
        alignItems: 'center',
        borderRadius: 12,
        flex: 4,
        flexDirection: 'row',
        marginLeft: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(4)
    },
    viewFile: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: Mixins.scale(32),
        width: '100%'
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    }
});
export const detailGroundTask = StyleSheet.create({
    container: {
        flex: 1,
        marginHorizontal: Mixins.scale(16),
        marginVertical: Mixins.scale(24)
    },
    imgClock: {
        height: Mixins.scale(24),
        marginRight: Mixins.scale(16),
        resizeMode: 'contain',
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(24)
    },
    imgLocation: {
        height: Mixins.scale(24),
        marginRight: Mixins.scale(8),
        resizeMode: 'contain',
        width: Mixins.scale(24)
    },
    viewClock: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Mixins.scale(32),
        width: '100%'
    },
    viewStoreType: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Mixins.scale(32)
    }
});
export const createGroundTask = StyleSheet.create({
    btnCreate: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(16),
        height: Mixins.scale(56),
        justifyContent: 'center',
        marginBottom: Mixins.scale(16),
        width: '100%'
    },
    container: {
        flex: 1,
        marginHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(24)
    },
    imgLocation: {
        height: Mixins.scale(24),
        marginRight: Mixins.scale(8),
        resizeMode: 'contain',
        width: Mixins.scale(24)
    },
    styleImgClock: {
        height: Mixins.scale(24),
        marginRight: Mixins.scale(16),
        resizeMode: 'contain',
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(24)
    },
    viewHour: {
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(8),
        paddingHorizontal: Mixins.scale(12),
        paddingVertical: Mixins.scale(6)
    },
    viewTabClock: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: Mixins.scale(32),
        width: '100%'
    }
});
export const chooseLocation = StyleSheet.create({
    container: { flex: 1, marginHorizontal: Mixins.scale(16) },
    styleImgDelete: {
        height: Mixins.scale(20),
        marginHorizontal: Mixins.scale(16),
        marginVertical: Mixins.scale(20),
        resizeMode: 'contain',
        width: Mixins.scale(20)
    },
    styleImgSearch: {
        height: Mixins.scale(20),
        marginHorizontal: Mixins.scale(16),
        marginVertical: Mixins.scale(20),
        resizeMode: 'contain',
        width: Mixins.scale(20)
    },
    viewItem: {
        borderBottomWidth: Mixins.scale(1),
        borderColor: Colors.GRAYF5,
        flexDirection: 'row',
        padding: Mixins.scale(12)
    },
    viewSearch: {
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(10),
        flexDirection: 'row',
        height: Mixins.scale(56),
        marginVertical: Mixins.scale(8)
    }
});
