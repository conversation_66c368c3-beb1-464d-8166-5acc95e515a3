import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';

import React, { PureComponent } from 'react';
import {
    Image,
    ScrollView,
    TouchableOpacity,
    View,
    Platform
} from 'react-native';
const { translate } = global.props.getTranslateConfig();
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import { requestPermission, openSetting } from '@mwg-kits/core';
import { RenderFileCheckIn } from '../Component/ComponentTicket';
import { toastConfig } from '../../../GroupTicket/MemberGroupTicket';
import * as _actionHome from '../../../action';
import * as _actionTicket from '../../../../Tickets/Ticket/action';
import ImagePicker from 'react-native-image-crop-picker';
import ModalUser from '../../../../modal/ModalUser';
import moment from 'moment';
import Toast from 'react-native-toast-message';
import { infoSchedule as style } from './style';
import { CONST_API } from '../../../../constant';

class InfoSchedule extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            showReadMore: false,
            showMoreTaskList: false,
            isShowModalCreateWork: false,
            isVisible: false,
            positionScroll: '',
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            },
            isShowModalUser: false,
            listUser: [],
            titleModal: '',
            isCheckIn: true
        };
    }
    componentDidMount = () => {
        const { ticket } = this.props.detailTicket.data;
        const data = {
            supportServiceId: ticket?.supportServiceId,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 20, search: '' },
            requestId: ''
        };
        this.props.actionHome.getListMemberGroup(data);
    };
    setCheckIn = (isCheckin) => {
        this.setState({ isCheckIn: isCheckin });
    };
    handleShowModalUser = (id) => {
        const { detailTicket } = this.props;
        const { ticket } = detailTicket.data;
        const { leader, manager } = ticket;
        let user = [];
        let title = '';
        if (id === 0) {
            user = [
                {
                    userName: ticket?.creatorUserName,
                    userFirstName: ticket?.creatorFirstName,
                    userLastName: ticket?.creatorLastName,
                    userImage: ticket?.creatorImage
                }
            ];
            title = translate('ticket_creator');
        } else if (id === 1) {
            user = [
                {
                    userName: ticket?.assigneeUsername,
                    userFirstName: ticket?.assigneeFirstName,
                    userLastName: ticket?.assigneeLastName,
                    userImage: ticket?.assigneeImage
                }
            ];
            title = translate('receiver');
        } else {
            user = [leader, manager];
            title = translate('watcher');
        }
        this.setState(
            {
                titleModal: title,
                listUser: user
            },
            () => {
                this.setState({
                    isShowModalUser: true
                });
            }
        );
    };
    renderContent = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        const { leader, manager } = data.ticket;
        return (
            <View style={{ marginTop: Mixins.scale(24) }}>
                <TouchableOpacity
                    onPress={() => {
                        this.handleShowModalUser(0);
                    }}
                    style={{
                        flexDirection: 'column'
                    }}>
                    <View style={{ flexDirection: 'row' }}>
                        <Image
                            style={style.imgLeft}
                            source={{ uri: 'ic_profile' }}
                        />
                        <MyText
                            text={translate('ticket_creator')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginHorizontal: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>

                    <View style={style.viewCreator}>
                        <Image
                            style={style.imgWatched}
                            source={{
                                uri: `${CONST_API.baseAvatarURI}${data?.ticket?.creatorImage}`
                            }}
                        />
                        <MyText
                            addSize={1}
                            text={`${data?.ticket?.creatorUserName}-${data?.ticket?.creatorLastName} ${data?.ticket?.creatorFirstName}`}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: 8
                            }}
                            numberOfLines={1}
                        />
                    </View>
                </TouchableOpacity>
                {/* {data?.ticket?.watcherUsers?.length > 0 && ( */}
                <TouchableOpacity
                    activeOpacity={1}
                    onPress={() => {
                        this.handleShowModalUser(2);
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            marginTop: Mixins.scale(16)
                        }}>
                        <Image
                            style={style.imgLeft}
                            source={{ uri: 'ic_add_group' }}
                        />
                        <MyText
                            text={translate('watcher')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginHorizontal: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>

                    <View>
                        {data?.ticket?.manager && (
                            <MyText
                                style={{
                                    color: Colors.BLACK_HEADER_TITLE,
                                    marginTop: Mixins.scale(16)
                                }}
                                text={`${translate('manager')}: ${
                                    manager?.userName
                                } - ${manager?.userLastName} ${
                                    manager?.userFirstName
                                }`}
                            />
                        )}
                        {data?.ticket?.leader && (
                            <MyText
                                style={{
                                    color: Colors.BLACK_HEADER_TITLE,
                                    marginTop: Mixins.scale(16)
                                }}
                                text={`${translate('leader')}: ${
                                    leader?.userName
                                } - ${leader?.userLastName} ${
                                    leader?.userFirstName
                                }`}
                            />
                        )}
                    </View>
                </TouchableOpacity>
                {/* )} */}
                <View
                    style={[
                        style.styleRow,
                        { justifyContent: 'space-between', marginTop: 16 }
                    ]}>
                    <View style={[style.viewRowBottom, { flex: 6 }]}>
                        <Image
                            style={style.imgCalendar}
                            source={{ uri: 'ic_calendar' }}
                        />
                        <MyText
                            text={translate('creation_time')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                    <View style={style.viewDate}>
                        <MyText
                            text={`${moment(
                                data?.ticket?.createTimeLong
                            ).format('DD/MM/YYYY, HH:mm')}`}
                            addSize={1}
                            style={style.txtDefault}
                        />
                    </View>
                </View>
                {data?.ticket?.completeTimeLong && (
                    <View
                        style={[
                            style.styleRow,
                            { justifyContent: 'space-between', marginTop: 16 }
                        ]}>
                        <View style={[style.viewRowBottom, { flex: 4 }]}>
                            <Image
                                style={style.imgCalendar}
                                source={{ uri: 'ic_calendar' }}
                            />
                            <MyText
                                text={translate('finished_time')}
                                addSize={1}
                                style={{
                                    color: Colors.BLACK_HEADER_TITLE,
                                    marginLeft: Mixins.scale(8)
                                }}
                                typeFont="medium"
                            />
                        </View>
                        <View style={style.viewDate}>
                            <MyText
                                text={`${moment(
                                    data?.ticket?.completeTimeLong
                                ).format('DD/MM/YYYY, HH:mm')}`}
                                addSize={1}
                                style={style.txtDefault}
                            />
                        </View>
                    </View>
                )}
            </View>
        );
    };
    pickerGallery = async (item) => {
        try {
            if (item.id === 0) {
                await requestPermission('photo');
                this.onShowPicker();
            } else {
                await requestPermission('camera');
                this.takePicture();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message:
                    item.id === 0
                        ? translate('access_library')
                        : translate('camera_permission'),
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };
    onShowPicker = async () => {
        const { detailTicket, xworkData } = this.props;

        const { common } = xworkData;

        await ImagePicker.openPicker({
            mediaType: 'photo',
            maxFiles: 5,
            waitAnimationEnd: false,
            forceJpg: true,
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            compressVideoPreset: 'HighestQuality',
            durationLimit: 60
        }).then(async (image) => {
            try {
                let dataFile = {};
                if (Platform.OS === 'android') {
                    let res = await common.resize({
                        path: image.path
                    });
                    if (res !== '') {
                        dataFile = {
                            uri: res.path,
                            path: res.uri,
                            width: res.width,
                            height: res.height,
                            mime: image.mime
                        };
                    }
                } else {
                    dataFile = {
                        uri: image.sourceURL,
                        path: image.path,
                        width: image.width,
                        height: image.height,
                        mime: image.mime
                    };
                }
                const body = {
                    files: dataFile,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };
                const sendImage =
                    await this.props.actionTicket.uploadImageGroundTask(
                        body,
                        this.state.isCheckIn
                    );
                if (sendImage) {
                    setTimeout(() => {
                        this.props.updateDetailTicket();
                        this.props.retryListTicket();
                    }, 1000);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_img_suc'),
                        position: 'bottom'
                    });
                }
            } catch (e) {
                global.props.hideLoader();
                console.log(e);
                Toast.show({
                    type: 'error',
                    text1: translate('upload_only_img_fail'),
                    position: 'bottom'
                });
            }
        });
    };
    removeFile = async (item) => {
        const { detailTicket } = this.props;
        let data = {
            ticketId: detailTicket.data?.ticket.id,
            fileId: item.id
        };
        try {
            global.props.showLoader();
            const response = await this.props.actionTicket.removeFile(data);
            global.props.hideLoader();

            if (response) {
                global.props.hideLoader();

                Toast.show({
                    type: 'success',
                    text1: translate('delete_file_success'),
                    position: 'bottom'
                });
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('delete_file_failed'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: error,
                position: 'bottom'
            });
        }
    };
    takePicture = async () => {
        const { detailTicket, xworkData } = this.props;
        const { common } = xworkData;
        let response = await common.pickSingleWithCamera({
            mediaType: 'photo',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            useFrontCamera: false
        });
        if (response && response.length > 0) {
            const body = {
                files: response[0],
                ticketId: detailTicket?.data?.ticket?.id,
                comment: false
            };
            // global.props.showLoader();
            const sendImage =
                await this.props.actionTicket.uploadImageGroundTask(
                    body,
                    this.state.isCheckIn
                );
            global.props.hideLoader();
            try {
                if (sendImage) {
                    setTimeout(() => {
                        this.props.updateDetailTicket();
                        this.props.retryListTicket();
                    }, 1000);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_img_suc'),
                        position: 'bottom'
                    });
                }
            } catch (error) {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('upload_only_img_fail'),
                    position: 'bottom'
                });
            }
        }
    };
    handleUpFile = async (item, isCheckIn) => {
        const { detailTicket, xworkData } = this.props;
        const { isGroundTaskEdit, supportServiceType } =
            detailTicket.data.ticket;
        let checkTimeEdit =
            !isGroundTaskEdit && supportServiceType === 'GROUND_TASK';
        const { fullProfile } = xworkData;
        this.setCheckIn(isCheckIn);
        let checkUserEdit =
            fullProfile?.username ===
            detailTicket?.data?.ticket?.creatorUserName;
        if (!checkTimeEdit && !checkUserEdit) {
            Toast.show({
                type: 'warning',
                text1: translate('only_creator'),
                position: 'bottom'
            });
            return;
        }
        if (checkTimeEdit) {
            Toast.show({
                type: 'error',
                text1: translate('outtime_update'),
                position: 'bottom'
            });
            return;
        }

        setTimeout(() => {
            this.pickerGallery(item);
        }, 250);
    };
    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };
    renderActivity = () => {
        const { props } = this.props;
        return (
            <View
                style={{
                    paddingBottom: Mixins.scale(16),
                    marginTop: Mixins.scale(32)
                }}>
                <View
                    style={[
                        style.viewRow,
                        { justifyContent: 'space-between' }
                    ]}>
                    <View style={style.viewRowBottom}>
                        <Image
                            style={style.imgLeftBottom}
                            source={{ uri: 'ic_clock' }}
                        />
                        <MyText
                            text={translate('activity')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('TicketActivityHistory');
                        }}
                        style={{}}>
                        <MyText
                            style={{
                                color: Colors.DARK_BLUE_60
                            }}
                            numberOfLines={1}
                            typeFont="semiBold"
                            text={translate('see_detail')}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };
    renderApprove = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        return (
            <View style={style.styleApprove}>
                <View style={style.styleRow}>
                    <Image
                        style={style.imgLeft}
                        source={{ uri: 'ic_add_group' }}
                    />
                    <MyText
                        text={translate('approver')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginHorizontal: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />

                    <View
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row'
                        }}>
                        {data?.approver?.map((item, index) => {
                            return (
                                <View
                                    style={{
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <Image
                                        key={index}
                                        style={{
                                            height: Mixins.scale(28),
                                            width: Mixins.scale(28),
                                            borderRadius: Mixins.scale(14)
                                        }}
                                        source={{
                                            uri: `${CONST_API.baseAvatarURI}${item?.approver.profile.image}`
                                        }}
                                    />
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
        );
    };
    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };
    checkQuickTicket = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        let checkQuickTicket = false;
        if (
            (helper.hasProperty(data?.ticket, 'asSocial') &&
                data.ticket.asSocial) ||
            (helper.hasProperty(data, 'ticketType') &&
                data?.ticketType === 'social')
        ) {
            checkQuickTicket = true;
            return checkQuickTicket;
        }
    };
    checkClickVideo = (item) => {
        return item?.filemime.includes('video');
    };
    handleData = () => {
        const extraDataJsonObject =
            this.props.detailTicket?.data?.ticket?.extraDataJsonObject;
        let listFile = [];
        if (extraDataJsonObject.checkinImageFileID) {
            let bodyCheckIn = {
                id: extraDataJsonObject.checkinImageFileID,
                filename: extraDataJsonObject.checkinImageFileName,
                filemime: 'image/jpeg'
            };
            listFile.push(bodyCheckIn);
        }
        if (extraDataJsonObject.checkoutImageFileID) {
            let bodyCheckOut = {
                id: extraDataJsonObject.checkoutImageFileID,
                filename: extraDataJsonObject.checkoutImageFileName,
                filemime: 'image/jpeg'
            };
            listFile.push(bodyCheckOut);
        }
        return listFile;
    };
    render() {
        const {
            renderHearder,
            renderTopHeader,
            detailTicket,
            xworkData,
            listFileTicket,
            streamToken
        } = this.props;
        const { common, fullProfile } = xworkData;
        const { viewMedia } = this.state;
        const { isGroundTaskEdit } = detailTicket.data.ticket;
        let checkUserEdit =
            fullProfile?.username ===
            detailTicket?.data?.ticket?.creatorUserName;
        return (
            <View style={{ flex: 1 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 70 }}
                    style={style.container}>
                    {renderHearder}
                    {!helper.IsValidateObject(detailTicket?.data.approveType) &&
                        renderTopHeader}
                    {this.renderContent()}
                    {!detailTicket?.data?.ticket?.extraDataJsonObject
                        ?.checkinImageFileID && !isGroundTaskEdit ? null : (
                        <View style={style.viewFile}>
                            <RenderFileCheckIn
                                name="Check-in"
                                translate={translate}
                                listFileTicket={
                                    detailTicket?.data?.ticket
                                        ?.extraDataJsonObject
                                        ?.checkinImageFileID
                                }
                                index={0}
                                streamToken={streamToken}
                                handleUpFile={this.handleUpFile}
                                setViewMedia={this.setViewMedia}
                                disable={!isGroundTaskEdit || !checkUserEdit}
                                isCheckIn
                            />

                            {detailTicket?.data?.ticket?.extraDataJsonObject
                                ?.checkinImageFileID && (
                                <RenderFileCheckIn
                                    name="Check-out"
                                    translate={translate}
                                    listFileTicket={
                                        detailTicket?.data?.ticket
                                            ?.extraDataJsonObject
                                            ?.checkoutImageFileID
                                    }
                                    index={1}
                                    streamToken={streamToken}
                                    handleUpFile={this.handleUpFile}
                                    setViewMedia={this.setViewMedia}
                                    disable={
                                        !isGroundTaskEdit || !checkUserEdit
                                    }
                                />
                            )}
                        </View>
                    )}

                    {detailTicket?.data.approveType === 'PENDING' ? (
                        <View>{this.renderApprove()}</View>
                    ) : (
                        !helper.IsValidateObject(
                            detailTicket?.data.approveType
                        ) && <View>{this.renderActivity()}</View>
                    )}
                </ScrollView>
                {this.state.isShowModalUser && (
                    <ModalUser
                        title={this.state.titleModal}
                        isVisible={this.state.isShowModalUser}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalUser: false
                            });
                        }}
                        detailTicket={this.props.detailTicket.data}
                        listUser={this.state.listUser}
                    />
                )}
                {viewMedia.visible && (
                    <common.ViewFile
                        visible={viewMedia.visible}
                        imageUrls={this.handleData()}
                        index={viewMedia.index}
                        onPress={this.onRequestClose}
                        checkQuickTicket={this.checkQuickTicket()}
                        onSwipeDown={() => this.onRequestClose()}
                        onRequestClose={this.onRequestClose}
                        streamToken={streamToken?.data?.token}
                        toastConfig={toastConfig}
                        isClickVideo={this.checkClickVideo(
                            listFileTicket?.data[viewMedia.index]
                        )}
                    />
                )}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken,
        listTemplateTask: state.ticketReducer.listTemplateTask
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(InfoSchedule);
