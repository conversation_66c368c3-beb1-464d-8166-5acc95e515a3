import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiBase, METHOD } from '@mwg-kits/core';
import { helper } from '@mwg-kits/common';
import { CONST_API } from '../../../../constant';
const START_GET_LOCATION = 'START_GET_LOCATION';
const STOP_GET_LOCATION = 'STOP_GET_LOCATION';
const SET_LOCATION = 'SET_LOCATION';
const GET_MANAGER = 'GET_MANAGER';
const SELECTED_TASK = 'SELECTED_TASK';
const FILES_TASK = 'FILES_TASK';

export const groundTaskAction = {
    START_GET_LOCATION,
    STOP_GET_LOCATION,
    SET_LOCATION,
    GET_MANAGER,
    SELECTED_TASK,
    FILES_TASK
};
const start_get_location = () => {
    return {
        type: START_GET_LOCATION,
        isFetching: true
    };
};
const stop_get_location = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LOCATION,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const setLocation = (data) => (dispatch) => {
    dispatch({
        type: groundTaskAction.SET_LOCATION,
        data
    });
};
export const selectedTask = (data) => (dispatch) => {
    dispatch({
        type: groundTaskAction.SELECTED_TASK,
        data
    });
};
export const filesTask = (data) => (dispatch) => {
    dispatch({
        type: groundTaskAction.FILES_TASK,
        data
    });
};
export const getToken = (key) => AsyncStorage.getItem(key);
const getApiLocation = (type) => {
    if (type === 0) {
        return CONST_API.API_GET_PROVINCE;
    } else if (type === 1) {
        return CONST_API.API_GET_DISTRICT;
    } else if (type === 2) {
        return CONST_API.API_GET_WARD;
    }
};
export const getListLocation = (type, body) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_location());
            const api = getApiLocation(type);
            const response = await apiBase(api, METHOD.POST, body, {
                access_token: await getToken('TOKEN_ACCESS'),
                enableLogger: true
            });
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'data') ||
                helper.IsValidateObject(response?.data) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_location({
                        isSuccess: true,
                        data: response?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_location({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_location({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
export function isArray(obj) {
    return obj !== undefined && obj !== null && obj.constructor === Array;
}
export const removeAccents = (object) => {
    let str = getNameFile(object);
    const AccentsMap = [
        'aàảãáạăằẳẵắặâầẩẫấậ',
        'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
        'dđ',
        'DĐ',
        'eèẻẽéẹêềểễếệ',
        'EÈẺẼÉẸÊỀỂỄẾỆ',
        'iìỉĩíị',
        'IÌỈĨÍỊ',
        'oòỏõóọôồổỗốộơờởỡớợ',
        'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
        'uùủũúụưừửữứự',
        'UÙỦŨÚỤƯỪỬỮỨỰ',
        'yỳỷỹýỵ',
        'YỲỶỸÝỴ'
    ];
    for (var i = 0; i < AccentsMap.length; i++) {
        var re = new RegExp('[' + AccentsMap[i].substr(1) + ']', 'g') || '';
        var char = AccentsMap[i][0] || '';
        if (re && char && str) {
            str = str.replace(re, char);
        }
    }
    return str;
};
const getNameFile = (object) => {
    if (object?.name) return object.name;
    let path = object?.path ? object.path : object.uri;
    let pathSegments = path.split('/');
    return pathSegments[pathSegments.length - 1];
};
export const createGroundTask1 = (body, file) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let fileName = '';
                global.props.showLoader();
                const data = new FormData();
                data.append('payload', JSON.stringify(body));
                if (isArray(file)) {
                    file?.map((item) => {
                        fileName = removeAccents(item);
                        data.append('files', {
                            uri: item?.path ? item.path : item.uri,
                            name: fileName,
                            fileName: fileName,
                            type: item?.mime ? item.mime : item.type
                        });
                    });
                } else {
                    fileName = removeAccents(item);
                    data.append('file', {
                        uri: file?.path ? file.path : file.uri,
                        name: fileName,
                        fileName: fileName,
                        type: file?.mime ? file?.mime : file?.type
                    });
                }
                const res = await fetch(CONST_API.API_CREATE_TASK, {
                    method: 'post',
                    body: data,
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${await getToken(
                            'TOKEN_ACCESS'
                        )}`
                    }
                });
                global.props.hideLoader();

                const response = await res.json();
                if (!response.error) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                global.props.hideLoader();

                reject(error);
            }
        });
    };
};
export const uploadFileGround = (body, file) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let fileName = '';
                global.props.showLoader();
                const data = new FormData();
                data.append('payload', JSON.stringify(body));
                file?.map((item) => {
                    fileName = removeAccents(item);
                    data.append('files', {
                        uri: item?.path ? item.path : item.uri,
                        name: fileName,
                        fileName: fileName,
                        type: item?.mime ? item?.mime : item?.type
                    });
                });
                const res = await fetch(CONST_API.API_CREATE_TASK, {
                    method: 'post',
                    body: data,
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${await getToken(
                            'TOKEN_ACCESS'
                        )}`
                    }
                });
                global.props.hideLoader();

                const response = await res.json();

                if (!response.error) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                global.props.hideLoader();

                reject(error);
            }
        });
    };
};
export const getManager = (userId) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                const response = await apiBase(
                    CONST_API.API_GET_MANAGER,
                    METHOD.POST,
                    { userId: userId },
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (response.error) {
                    const msg = {
                        code: -1,
                        errorReason: 'shipaddress id rỗng'
                    };
                    throw msg;
                }
                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response?.object);
                } else {
                    throw response.errorReason;
                }
            } catch (error) {
                reject(error);
            }
        });
    };
};
export const removeFileTask = (fileId) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                const response = await apiBase(
                    CONST_API.API_REMOVE_FILE_TASK,
                    METHOD.POST,
                    { fileId: fileId },
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );
                console.log(response, '12312312312312');

                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response.object);
                }
            } catch (error) {
                let msg = 'Xoá thất bại';

                if (helper.IsValidateObject(error.message)) {
                    msg = error.message;
                }
                reject(msg);
            }
        });
    };
};
