import Toast from 'react-native-toast-message';
const { translate } = global.props.getTranslateConfig();
//import { translate } from '@mwg-kits/languages';
const isEmpty = (str) => {
    if (str === '' || str === undefined || str === null) return true;
};
const isNumericString = (inputString) => {
    // Sử dụng regular expression để kiểm tra chuỗi chỉ chứa số
    const numericRegex = /^[0-9]+$/;
    return numericRegex.test(inputString);
};
export const checkCategory = (name) => {
    if (name === 'Khác' || name === 'Mở mới') return false;
    return true;
};
export const checkPartner = (name) => {
    if (
        name === 'Mở mới' ||
        name === 'Mở rộng' ||
        name === 'Công việc hành chính'
    )
        return false;
    return true;
};

const setError = (error, type) => {
    let string = error;
    if (type === 0) {
        string = `${translate('warning_select')} ${error}`;
    }
    if (type === 1) {
        string = `${translate('warning_enter')} ${error}`;
    }
    Toast.show({
        type: 'error',
        text1: string,
        position: 'bottom'
    });
    return true;
};
const checkErrorTask = (body) => {
    if (isEmpty(body.groundWroking.workingTitle))
        return setError(translate('alert_enter_ticket'));
    if (body?.ticketTaskStartTime > body?.ticketTaskDueTime)
        return setError(translate('alert_time_higher'));
    if (isEmpty(body.groundWroking.categoryID))
        return setError(translate('job_type'), 0);
    if (
        checkCategory(body.groundWroking.categoryName) &&
        isEmpty(body.groundWroking.houseCode)
    )
        return setError(translate('supermarket'), 0);
    if (
        checkPartner(body.groundWroking.categoryName) &&
        isEmpty(body.groundWroking.partnerName)
    )
        return setError(translate('partner_name'), 1);
    if (
        checkPartner(body.groundWroking.categoryName) &&
        isEmpty(body.groundWroking.partnerPhone)
    )
        return setError(translate('contribute_phone_input'), 1);
    if (
        checkPartner(body.groundWroking.categoryName) &&
        !isNumericString(body.groundWroking.partnerPhone)
    )
        return setError(translate('warning_check_phone'));
    if (
        checkPartner(body.groundWroking.categoryName) &&
        body.groundWroking.partnerPhone.length < 10
    )
        return setError(translate('warning_enough_phone'));
    if (isEmpty(body.groundWroking.workingTarget))
        return setError(translate('target_work'), 1);
};
const removeAccents = (str) => {
    var AccentsMap = [
        'aàảãáạăằẳẵắặâầẩẫấậ',
        'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
        'dđ',
        'DĐ',
        'eèẻẽéẹêềểễếệ',
        'EÈẺẼÉẸÊỀỂỄẾỆ',
        'iìỉĩíị',
        'IÌỈĨÍỊ',
        'oòỏõóọôồổỗốộơờởỡớợ',
        'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
        'uùủũúụưừửữứự',
        'UÙỦŨÚỤƯỪỬỮỨỰ',
        'yỳỷỹýỵ',
        'YỲỶỸÝỴ'
    ];
    for (var i = 0; i < AccentsMap.length; i++) {
        var re = new RegExp('[' + AccentsMap[i].substr(1) + ']', 'g') || '';
        var char = AccentsMap[i][0] || '';
        if (re && char && str) {
            str = str.replace(re, char);
        }
    }
    return str;
};

export { checkErrorTask, removeAccents };
