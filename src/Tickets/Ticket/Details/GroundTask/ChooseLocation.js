import {
    FlatList,
    Image,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { PureComponent } from 'react';
import { bindActionCreators } from 'redux';
import { connect } from 'react-redux';
import * as _actionGroundTask from './action';
import * as _actionTicket from '../../action';
const { translate } = global.props.getTranslateConfig();
import { chooseLocation as styles } from './style';
const removeAccents = (str) => {
    var AccentsMap = [
        'aàảãáạăằẳẵắặâầẩẫấậ',
        'AÀẢÃÁẠĂẰẲẴẮẶÂẦẨẪẤẬ',
        'dđ',
        'DĐ',
        'eèẻẽéẹêềểễếệ',
        'EÈẺẼÉẸÊỀỂỄẾỆ',
        'iìỉĩíị',
        'IÌỈĨÍỊ',
        'oòỏõóọôồổỗốộơờởỡớợ',
        'OÒỎÕÓỌÔỒỔỖỐỘƠỜỞỠỚỢ',
        'uùủũúụưừửữứự',
        'UÙỦŨÚỤƯỪỬỮỨỰ',
        'yỳỷỹýỵ',
        'YỲỶỸÝỴ'
    ];
    for (var i = 0; i < AccentsMap.length; i++) {
        var re = new RegExp('[' + AccentsMap[i].substr(1) + ']', 'g') || '';
        var char = AccentsMap[i][0] || '';
        if (re && char && str) {
            str = str.replace(re, char);
        }
    }
    return str;
};
class ChooseLocation extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            arraySearch: [], //this.getListLocation(),
            id: '',
            name: '',
            isChange: false,
            typeLocation: this.props.route.params.type,
            keyWord: ''
        };
    }

    componentDidMount = async () => {
        const { actionGroundTask, listLocation } = this.props;
        const { type } = this.props.route.params;
        const { typeLocation } = this.state;
        let body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 100
            }
        };
        if (type == 1) {
            body = {
                provinceId: [listLocation.dataLocation.provinceId],
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 100
                }
            };
        }
        if (type == 2) {
            body = {
                districtId: [listLocation.dataLocation.districtId],
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 100
                }
            };
        }
        await actionGroundTask.getListLocation(typeLocation, body);
        this.setState({ arraySearch: this.getListLocation() });
    };
    onChoose = async () => {
        const { actionGroundTask } = this.props;
        const { typeLocation } = this.state;
        const { id, name, isChange } = this.state;
        let data = null;
        if (isChange) {
            if (typeLocation === 1) {
                data = {
                    provinceId: id,
                    provinceName: name,
                    districtId: null,
                    districtName: null,
                    wardId: null,
                    wardName: null
                };
            } else if (typeLocation === 2) {
                data = {
                    districtId: id,
                    districtName: name,
                    wardId: null,
                    wardName: null
                };
            } else if (typeLocation === 3) {
                data = { wardId: id, wardName: name };
            }
        }
        const body = { ...this.props.listLocation.dataLocation, ...data };
        await this.props.actionGroundTask.setLocation(body);
        this.setState({
            arraySearch: [],
            isChange: false
        });

        const bodyDistrict = {
            provinceId: [id],
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 100
            }
        };
        const bodyWard = {
            districtId: [id],
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 100
            }
        };
        if (typeLocation === 1) {
            await actionGroundTask.getListLocation(1, bodyDistrict);
        } else if (typeLocation === 2) {
            await actionGroundTask.getListLocation(2, bodyWard);
        } else if (typeLocation === 3) {
            this.props.navigation.goBack();
        }
        this.setState({ arraySearch: this.getListLocation() });
    };

    checkCurrentTab = (type, item) => {
        if (type === 'id') {
            return item.id;
        } else {
            return item.name;
        }
    };

    getListLocation() {
        const { listLocation } = this.props;
        return listLocation.data;
    }
    search = (text) => {
        const keyword = removeAccents(text?.toUpperCase());
        let data = this.getListLocation().filter((e) => {
            let name = removeAccents(e.name?.toUpperCase());
            return name.includes(keyword);
        });
        this.setState({ arraySearch: data, keyWord: text });
    };

    render() {
        const { listLocation } = this.props;
        const { xworkData } = this.props;
        const { component } = xworkData;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket } = component;
        return (
            <View style={{ flex: 1 }}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={translate('choose_location')}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    onPressBack={() => {
                        this.props.navigation.goBack();
                    }}
                    actionRetry={() => {}}
                    isLoading={this.props.listLocation.isFetching}
                    colorTitle>
                    <View style={styles.container}>
                        <View style={styles.viewSearch}>
                            <Image
                                source={{ uri: 'ic_search_ticket' }}
                                style={styles.styleImgSearch}
                            />
                            <TextInput
                                placeholderTextColor={Colors.GRAYF7}
                                style={{
                                    flex: 1,
                                    marginRight: Mixins.scale(4)
                                }}
                                value={this.state.keyWord}
                                placeholder={translate('searching_placeholder')}
                                onChangeText={(text) => this.search(text)}
                            />
                            <TouchableOpacity
                                onPress={() => {
                                    this.search('');
                                    this.setState({ keyWord: '' });
                                }}>
                                <Image
                                    source={{ uri: 'ic_delete' }}
                                    style={styles.styleImgDelete}
                                />
                            </TouchableOpacity>
                        </View>
                        <View
                            style={{
                                flex: 1
                            }}>
                            {this.state.arraySearch === undefined ||
                            this.state.arraySearch === [] ||
                            this.state.arraySearch.length === 0 ? (
                                <View
                                    style={{
                                        flex: 1,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <Image
                                        style={{
                                            width: 250,
                                            height: 250
                                        }}
                                        resizeMode="contain"
                                        source={{ uri: 'ic_empty' }}
                                    />
                                    <MyText
                                        text={translate('empty_data_warning')}
                                        style={{
                                            color: Colors.BLACK
                                        }}
                                    />
                                </View>
                            ) : (
                                <FlatList
                                    extraData={listLocation.isFetching}
                                    showsVerticalScrollIndicator={false}
                                    initialNumToRender={50}
                                    data={this.state.arraySearch}
                                    renderItem={({ item }) => {
                                        return (
                                            <TouchableOpacity
                                                style={[
                                                    styles.viewItem,
                                                    {
                                                        backgroundColor:
                                                            this.state.id ===
                                                            item.provinceID
                                                                ? Colors.BLUE_MAIN
                                                                : Colors.WHITE
                                                    }
                                                ]}
                                                onPress={async () => {
                                                    await this.setState({
                                                        keyWord: '',
                                                        isChange: true,
                                                        id: this.checkCurrentTab(
                                                            'id',
                                                            item
                                                        ),
                                                        name: this.checkCurrentTab(
                                                            'name',
                                                            item
                                                        ),
                                                        typeLocation:
                                                            this.state
                                                                .typeLocation ===
                                                            0
                                                                ? 1
                                                                : this.state
                                                                      .typeLocation ===
                                                                  1
                                                                ? 2
                                                                : 3
                                                    });
                                                    this.onChoose();
                                                }}>
                                                <MyText
                                                    text={this.checkCurrentTab(
                                                        'name',
                                                        item
                                                    )}
                                                    addSize={1}
                                                    style={{
                                                        color:
                                                            this.state.id ===
                                                            item.provinceID
                                                                ? Colors.WHITE
                                                                : Colors.BLACK
                                                    }}
                                                />
                                            </TouchableOpacity>
                                        );
                                    }}
                                />
                            )}
                        </View>
                    </View>
                </WrapperContainerTicket>
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        detailTicket: state.ticketReducer.detailTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listLocation: state.groundTaskReducer.listLocation,
        listTemplateTask: state.ticketReducer.listTemplateTask,
        xworkData: state.groupTicketReducer.xworkData
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ChooseLocation);
