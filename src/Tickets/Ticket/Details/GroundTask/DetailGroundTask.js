import React from 'react';
import {
    ViewText,
    RenderTagDetail,
    RenderFile
} from '../Component/ComponentGroundTask';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { connect } from 'react-redux';
import { requestPermission, openSetting } from '@mwg-kits/core';
import { toastConfig } from '../../../GroupTicket/MemberGroupTicket';
import { View, Image, ScrollView } from 'react-native';
import * as _actionGroundTask from './action';
import * as _actionTicket from '../../action';
import DocumentPicker, { types } from 'react-native-document-picker';
import ImagePicker from 'react-native-image-crop-picker';
import moment from 'moment';
import Toast from 'react-native-toast-message';
const { translate } = global.props.getTranslateConfig();
import { detailGroundTask as styles } from './style';
class DetailGroundTask extends React.PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            file: [],
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        };
    }
    componentDidMount = () => {
        const { item } = this.props.route.params;
        this.setState({ file: item.files });
    };
    initListTask = () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 20 }
        };
        this.props.actionTicket.getListTask(params);
    };
    onShowPicker = async () => {
        try {
            const { detailTicket } = this.props;
            const { item } = this.props.route.params;
            await ImagePicker.openPicker({
                mediaType: 'photo',
                maxFiles: 5,
                multiple: true,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                maximumVideoDuration: 60000,
                compressVideoPreset: 'HighestQuality'
            }).then(async (image) => {
                global.props.hideLoader();
                const body = {
                    taskId: item.id,
                    ticketId: detailTicket?.data?.ticket?.id,
                    arrayReceivedUserId: [item.creatorId],
                    ticketTaskDone: item?.done,
                    startTime: item?.startTime,
                    dueTime: item?.dueTime,
                    ticketTaskContent: item?.content,
                    ticketTaskSubject: item?.subject
                };
                const sendImage =
                    await this.props.actionGroundTask.createGroundTask1(
                        body,
                        image
                    );
                try {
                    if (sendImage) {
                        if (sendImage.error) {
                            Toast.show({
                                type: 'error',
                                text1: translate('upload_only_img_fail'),
                                position: 'bottom'
                            });
                            return;
                        }
                        this.initListTask();
                        this.props.actionGroundTask.filesTask(
                            sendImage[0].files
                        );
                        global.props.hideLoader();
                        Toast.show({
                            type: 'success',
                            text1: translate('upload_img_suc'),
                            position: 'bottom'
                        });
                    }
                } catch (error) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_only_img_fail'),
                        position: 'bottom'
                    });
                }
            });
        } catch (e) {
            global.props.hideLoader();
            console.log(e);
        }
    };
    upLoadFile = async () => {
        try {
            const { item } = this.props.route.params;
            const response = await DocumentPicker.pick({
                allowMultiSelection: true,
                presentationStyle: 'fullScreen',
                type: [
                    types.pdf,
                    types.doc,
                    types.xls,
                    types.pptx,
                    types.ppt,
                    types.docx,
                    types.xlsx
                ]
            });
            const { detailTicket } = this.props;

            if (response) {
                const body = {
                    taskId: item.id,
                    ticketId: detailTicket?.data?.ticket?.id,
                    arrayReceivedUserId: [item.creatorId],
                    ticketTaskDone: item?.done,
                    startTime: item?.startTime,
                    dueTime: item?.dueTime,
                    ticketTaskContent: item?.content,
                    ticketTaskSubject: item?.subject
                };

                const sendFile =
                    await this.props.actionGroundTask.uploadFileGround(
                        body,
                        response
                    );
                try {
                    if (sendFile) {
                        if (sendFile.error) {
                            Toast.show({
                                type: 'error',
                                text1: translate('upload_only_img_fail'),
                                position: 'bottom'
                            });
                            return;
                        }
                        this.initListTask();
                        this.props.actionGroundTask.filesTask(
                            sendFile[0].files
                        );
                        global.props.hideLoader();
                        Toast.show({
                            type: 'success',
                            text1: translate('upload_file_success'),
                            position: 'bottom'
                        });
                    }
                } catch (error) {
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_file_fail'),
                        position: 'bottom'
                    });
                }
            }
        } catch (e) {
            console.log(e);
        }
    };
    pickerGallery = async (item) => {
        try {
            await requestPermission('photo');
            if (item.id === 0) {
                this.onShowPicker();
            } else {
                this.upLoadFile();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('access_library'),
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };
    handleUpFile = async (item) => {
        setTimeout(() => {
            this.pickerGallery(item);
        }, 1000);
    };

    handleRemoveFile = (item) => {
        global.props.alert({
            show: true,
            title: translate('delete_file'),
            titleColor: {
                color: Colors.DARK_RED_30
            },
            message: translate('ask_delete_file'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
                this.removeFile(item);
            },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
            }
        });
    };
    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };
    onPressEditTicket = () => {
        const { item } = this.props.route.params;
        this.props.navigation.navigate('CreateGroundTask', { item });
    };
    dateTimeToDate = (string) => {
        let date = string.split(' ')[0];
        let time = string.split(' ')[1];
        let datePart = date.split('-');
        let timePart = time.split(':');
        let res = new Date(
            datePart[2],
            datePart[1],
            datePart[0],
            timePart[0],
            timePart[1],
            timePart[2]
        );
        return res;
    };
    isEmptyStr = (strBefore, str) => {
        if (str === '' || str === undefined || str === null) return '';
        if (!this.isEmpty(strBefore)) return `, ${str}`;
        return str;
    };
    isEmpty = (str) => {
        if (str === '' || str === undefined || str === null) return true;
        return false;
    };
    getStringLocation = () => {
        const { item } = this.props.route.params;
        const { extraData } = item;
        let province = extraData?.province;
        let district = extraData?.district;
        let ward = extraData?.wards;
        let result = `${extraData?.workingAddress}${this.isEmptyStr(
            extraData?.workingAddress,
            ward
        )}${this.isEmptyStr(ward, district)}${this.isEmptyStr(
            district,
            province
        )}`;
        return result;
    };
    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };
    checkClickVideo = (item) => {
        return item?.filemime.includes('video');
    };
    render() {
        const { detailTicket, xworkData, streamToken } = this.props;
        const { component, currentLang, common } = xworkData;
        //const { viewMedia } = this.state;
        if (component === undefined) {
            return null;
        }
        const { viewMedia } = this.state;
        const { WrapperContainerTicket } = component;
        const { item } = this.props.route.params;
        const { extraData } = item;
        const { data } = detailTicket;
        const { fullProfile } = xworkData;
        let checkReceiverUsername =
            fullProfile.username === item.receiverUsername;
        return (
            <View style={{ flex: 1 }}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={translate('detail_ticket')}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    onPressBack={() => {
                        this.props.navigation.goBack();
                    }}
                    actionRetry={() => {}}
                    onPressEditTicket={
                        !data?.ticket?.isGroundTaskEdit ||
                        !checkReceiverUsername
                            ? null
                            : this.onPressEditTicket
                    }
                    // isSuccess={!detailTicket?.isError}
                    colorTitle>
                    <ScrollView>
                        <View
                            style={[
                                styles.container,
                                {
                                    marginBottom: this.isEmpty(extraData?.note)
                                        ? Mixins.scale(56)
                                        : null
                                }
                            ]}>
                            {!this.isEmpty(extraData?.workingTitle) && (
                                <MyText
                                    text={extraData?.workingTitle}
                                    addSize={4}
                                    typeFont="medium"
                                    style={{
                                        color: Colors.GRAYF10
                                    }}
                                />
                            )}
                            <RenderTagDetail data={item} />
                            <View style={styles.viewClock}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}>
                                    <Image
                                        source={{ uri: 'ic_clock' }}
                                        style={styles.imgClock}
                                    />
                                    <MyText
                                        text={translate('time')}
                                        addSize={2}
                                        typeFont="medium"
                                        style={{
                                            color: Colors.GRAYF10
                                        }}
                                    />
                                </View>
                                <MyText
                                    text={`${moment(
                                        new Date(
                                            this.dateTimeToDate(item.startTime)
                                        )
                                    ).format('HH:mm')} - ${moment(
                                        new Date(
                                            this.dateTimeToDate(item.dueTime)
                                        )
                                    ).format('HH:mm')}`}
                                    addSize={2}
                                    typeFont="medium"
                                    style={{
                                        color: Colors.GRAYF10
                                    }}
                                />
                            </View>
                            <ViewText
                                title={translate('job_type')}
                                text={extraData?.categoryName}
                                style={{ marginBottom: Mixins.scale(16) }}
                            />
                            <View style={styles.viewStoreType}>
                                <MyText
                                    text={translate('store_type')}
                                    addSize={2}
                                    typeFont="medium"
                                    style={{
                                        color: Colors.GRAYF10
                                    }}
                                />
                                <MyText
                                    text={
                                        extraData?.isStore
                                            ? translate('supermarket')
                                            : translate('warehouse')
                                    }
                                    addSize={2}
                                    typeFont="medium"
                                    style={{
                                        color: Colors.GRAYF10
                                    }}
                                />
                            </View>
                            {!this.isEmpty(extraData?.houseName) && (
                                <ViewText
                                    title={translate('supermarket')}
                                    text={extraData?.houseName}
                                    style={{ marginBottom: Mixins.scale(16) }}
                                />
                            )}
                            {!this.isEmpty(this.getStringLocation()) && (
                                <View>
                                    <View
                                        style={{
                                            flexDirection: 'row'
                                        }}>
                                        <Image
                                            source={{ uri: 'ic_location' }}
                                            style={styles.imgLocation}
                                        />
                                        <MyText
                                            text={translate('workplace')}
                                            addSize={2}
                                            typeFont="medium"
                                            style={{
                                                color: Colors.GRAYF10
                                            }}
                                        />
                                    </View>
                                    <ViewText
                                        text={this.getStringLocation()}
                                        style={{
                                            marginBottom: Mixins.scale(16)
                                        }}
                                    />
                                </View>
                            )}
                            {!this.isEmpty(extraData?.partnerName) && (
                                <ViewText
                                    title={translate('partner_name')}
                                    text={extraData?.partnerName}
                                    style={{ marginBottom: Mixins.scale(16) }}
                                />
                            )}
                            {!this.isEmpty(extraData?.partnerPhone) && (
                                <ViewText
                                    title={translate('partner_phone')}
                                    text={extraData?.partnerPhone}
                                    style={{ marginBottom: Mixins.scale(16) }}
                                />
                            )}
                            {!this.isEmpty(extraData?.workingTarget) && (
                                <ViewText
                                    title={translate('target_work')}
                                    text={extraData?.workingTarget}
                                    multiline={true}
                                    style={{ marginBottom: Mixins.scale(16) }}
                                />
                            )}
                            {!this.isEmpty(extraData?.workingResult) && (
                                <ViewText
                                    title={translate('result_achieve')}
                                    text={extraData?.workingResult}
                                    multiline={true}
                                    style={{ marginBottom: Mixins.scale(16) }}
                                />
                            )}
                            {this.props.selectedTask?.files?.length === 0 &&
                            !detailTicket?.data?.ticket
                                ?.isGroundTaskCreate ? null : (
                                <RenderFile
                                    handleUpFile={this.handleUpFile}
                                    listFileTicket={
                                        this.props.selectedTask.files
                                    }
                                    streamToken={streamToken}
                                    handleRemoveFile={this.handleRemoveFile}
                                    setViewMedia={this.setViewMedia}
                                    isShowRemove={false}
                                    hideAddFile={
                                        detailTicket.data?.ticket
                                            ?.supportServiceType ===
                                        'APPROVED_MATERIAL_COST'
                                    }
                                    translate={translate}
                                    currentLang={currentLang}
                                    isTask
                                    disable={
                                        !data?.ticket?.isGroundTaskEdit ||
                                        !checkReceiverUsername
                                    }
                                />
                            )}
                            {!this.isEmpty(extraData?.note) && (
                                <ViewText
                                    title={translate('note')}
                                    text={extraData?.note}
                                    multiline={true}
                                    style={{
                                        marginBottom: Mixins.scale(16),
                                        marginTop: Mixins.scale(32)
                                    }}
                                />
                            )}
                        </View>
                    </ScrollView>
                    <Toast position="bottom" config={toastConfig} />
                    {viewMedia.visible && (
                        <common.ViewFile
                            visible={viewMedia.visible}
                            imageUrls={this.props.selectedTask.files}
                            index={viewMedia.index}
                            onPress={this.onRequestClose}
                            onSwipeDown={() => this.onRequestClose()}
                            onRequestClose={this.onRequestClose}
                            streamToken={streamToken?.data?.token}
                            toastConfig={toastConfig}
                            isClickVideo={this.checkClickVideo(
                                this.props.selectedTask.files[viewMedia.index]
                            )}
                            isTask
                        />
                    )}
                </WrapperContainerTicket>
            </View>
        );
    }
}
const mapDispatchToProps = (dispatch) => {
    return {
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch)
    };
};
const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken,
        selectedTask: state.groundTaskReducer.selectedTask
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(DetailGroundTask);
