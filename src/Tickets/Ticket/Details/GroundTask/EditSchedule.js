import React, { PureComponent } from 'react';
import {
    Image,
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    TouchableOpacity,
    View
} from 'react-native';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import { connect } from 'react-redux';
import { InputText } from '../Component/ComponentGroundTask';
import { RenderFileCheckIn } from '../Component/ComponentTicket';
import { toastConfig } from '../../../GroupTicket/MemberGroupTicket';
import * as _actionHome from '../../../action';
import * as _actionTicket from '../../action';
import { requestPermission, openSetting } from '@mwg-kits/core';
import ImagePicker from 'react-native-image-crop-picker';
import ModalUser from '../../../../modal/ModalUser';
import moment from 'moment';
import Toast from 'react-native-toast-message';
const { translate } = global.props.getTranslateConfig();
import { editSchedule as style } from './style';
import { CONST_API } from '../../../../constant';
class EditSchedule extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            arrayWatcher: [],
            timeStart: '',
            image: '',
            showModalUser: false,
            titleModal: '',
            lisUser: null,
            isShowModalUser: false,
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            },
            isCheckIn: true
        };
    }
    setCheckIn = (isCheckin) => {
        this.setState({ isCheckIn: isCheckin });
    };
    componentDidMount = () => {
        this.changeTitle(this.props.detailTicket?.data?.ticket?.subject);
        this.changeFollowers();
        this.changeTimeCreate();
    };
    changeTitle = (text) => {
        this.setState({
            title: text
        });
    };
    checkQuickTicket = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        let checkQuickTicket = false;
        if (
            (helper.hasProperty(data?.ticket, 'asSocial') &&
                data.ticket.asSocial) ||
            (helper.hasProperty(data, 'ticketType') &&
                data?.ticketType === 'social')
        ) {
            checkQuickTicket = true;
            return checkQuickTicket;
        }
    };
    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };
    checkClickVideo = (item) => {
        return item?.filemime.includes('video');
    };
    changeFollowers = (array) => {
        this.setState({
            followers: array
        });
    };
    changeTimeCreate = (time) => {
        this.setState({
            timeCreate: time
        });
    };
    pickerGallery = async (item) => {
        try {
            await requestPermission('photo');
            if (item.id === 0) {
                this.onShowPicker();
            } else {
                this.takePicture();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('access_library'),
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };
    initListFile = () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10 }
        };

        this.props.actionTicket.getListFile(params);
    };
    updateDetailTicket = async () => {
        const { detailTicket } = this.props;

        await this.props.actionTicket.getDetailTicket(
            detailTicket?.data?.ticket?.id
        );
        this.handleGlobalState();
    };
    retryListTicket = () => {
        const { detailTicket } = this.props;
        const data = {
            supportServiceId: detailTicket?.data?.ticket?.supportServiceId
        };
        this.props.actionTicket.getListTicket(data);
    };
    onShowPicker = async () => {
        const { detailTicket, xworkData } = this.props;

        const { common } = xworkData;
        await ImagePicker.openPicker({
            mediaType: 'photo',
            maxFiles: 5,
            waitAnimationEnd: false,
            forceJpg: true,
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            compressVideoPreset: 'HighestQuality',
            durationLimit: 60
        }).then(async (image) => {
            try {
                let dataFile;
                if (Platform.OS === 'android') {
                    let res = await common.resize({
                        path: image.path
                    });
                    if (res !== '') {
                        dataFile = {
                            uri: res.path,
                            path: res.uri,
                            width: res.width,
                            height: res.height,
                            mime: image.mime
                        };
                    }
                } else {
                    dataFile = {
                        uri: image.sourceURL,
                        path: image.path,
                        width: image.width,
                        height: image.height,
                        mime: image.mime
                    };
                }
                const body = {
                    files: dataFile,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };
                const sendImage =
                    await this.props.actionTicket.uploadImageGroundTask(
                        body,
                        this.state.isCheckIn
                    );
                if (sendImage) {
                    setTimeout(() => {
                        this.updateDetailTicket();
                        this.retryListTicket();
                    }, 1000);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_img_suc'),
                        position: 'bottom'
                    });
                }
            } catch (e) {
                console.log(e);
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('upload_only_img_fail'),
                    position: 'bottom'
                });
            }
        });
    };
    removeFile = async (item) => {
        const { detailTicket } = this.props;
        let data = {
            ticketId: detailTicket.data?.ticket.id,
            fileId: item.id
        };
        try {
            global.props.showLoader();
            const response = await this.props.actionTicket.removeFile(data);
            this.props.initListFile();
            global.props.hideLoader();

            if (response) {
                global.props.hideLoader();

                Toast.show({
                    type: 'success',
                    text1: translate('delete_file_success'),
                    position: 'bottom'
                });
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('delete_file_failed'),
                    position: 'bottom'
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: error,
                position: 'bottom'
            });
        }
    };
    takePicture = async () => {
        const { detailTicket, xworkData } = this.props;
        const { common } = xworkData;
        let response = await common.pickSingleWithCamera({
            mediaType: 'photo',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            useFrontCamera: false
        });
        if (response && response.length > 0) {
            const body = {
                files: response[0],
                ticketId: detailTicket?.data?.ticket?.id,
                comment: false
            };
            // global.props.showLoader();
            const sendImage =
                await this.props.actionTicket.uploadImageGroundTask(
                    body,
                    this.state.isCheckIn
                );
            global.props.hideLoader();
            try {
                if (sendImage) {
                    setTimeout(() => {
                        this.updateDetailTicket();
                        this.retryListTicket();
                    }, 1000);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_img_suc'),
                        position: 'bottom'
                    });
                }
            } catch (error) {
                console.log(error);
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('upload_only_img_fail'),
                    position: 'bottom'
                });
            }
        }
    };
    handleUpFile = async (item, isCheckIn) => {
        this.setCheckIn(isCheckIn);
        setTimeout(() => {
            this.pickerGallery(item);
        }, 250);
    };
    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };

    renderFile = () => {
        const { streamToken, xworkData, detailTicket } = this.props;
        const { fullProfile } = xworkData;
        const { isGroundTaskEdit } = detailTicket.data.ticket;
        let checkUserEdit =
            fullProfile?.username ===
            detailTicket?.data?.ticket?.creatorUserName;
        return (
            <View>
                {!detailTicket?.data?.ticket?.extraDataJsonObject
                    ?.checkinImageFileID && !isGroundTaskEdit ? null : (
                    <View style={style.viewFile}>
                        <RenderFileCheckIn
                            name="Check-in"
                            translate={translate}
                            listFileTicket={
                                detailTicket?.data?.ticket?.extraDataJsonObject
                                    ?.checkinImageFileID
                            }
                            streamToken={streamToken}
                            handleUpFile={this.handleUpFile}
                            setViewMedia={this.setViewMedia}
                            index={0}
                            isCheckIn
                        />
                        {detailTicket?.data?.ticket?.extraDataJsonObject
                            ?.checkinImageFileID && (
                            <RenderFileCheckIn
                                name="Check-out"
                                translate={translate}
                                listFileTicket={
                                    detailTicket?.data?.ticket
                                        ?.extraDataJsonObject
                                        ?.checkoutImageFileID
                                }
                                streamToken={streamToken}
                                handleUpFile={this.handleUpFile}
                                setViewMedia={this.setViewMedia}
                                index={1}
                                disable={
                                    !isGroundTaskEdit ||
                                    !checkUserEdit ||
                                    !detailTicket?.data?.ticket
                                        ?.extraDataJsonObject
                                        ?.checkinImageFileID
                                }
                            />
                        )}
                    </View>
                )}
            </View>
        );
    };
    handleShowModalUser = (id) => {
        const { detailTicket } = this.props;
        const { ticket } = detailTicket.data;
        let user = [];
        let title = '';
        if (id === 0) {
            user = [
                {
                    userName: ticket?.creatorUserName,
                    userFirstName: ticket?.creatorFirstName,
                    userLastName: ticket?.creatorLastName,
                    userImage: ticket?.creatorImage
                }
            ];
            title = translate('ticket_creator');
        } else if (id === 1) {
            user = [
                {
                    userName: ticket?.assigneeUsername,
                    userFirstName: ticket?.assigneeFirstName,
                    userLastName: ticket?.assigneeLastName,
                    userImage: ticket?.assigneeImage
                }
            ];
            title = translate('receiver');
        } else {
            user = ticket.watcherUsers;
            title = translate('watcher');
        }
        this.setState(
            {
                titleModal: title,
                listUser: user
            },
            () => {
                this.setState({
                    isShowModalUser: true
                });
            }
        );
    };
    handleButtonSave = async () => {
        try {
            const { xworkData } = this.props;
            const { ticket } = this.props.detailTicket.data;
            const { watcherUsers } = ticket;
            let arrayWatcher = [];
            watcherUsers?.forEach((e) => {
                arrayWatcher.push(e.userId);
            });
            const body = {
                id: ticket?.id,
                supportServiceId: ticket?.supportServiceId,
                subject: this.state.title,
                content: 'Service Ground Task',
                startTime: ticket?.startDateLong,
                source: 'phone',
                createdUserId: ticket?.creatorId,
                statusId: ticket?.statusId,
                watcherUserId: arrayWatcher,
                assigneeUserId: xworkData?.fullProfile.id || -1
            };
            let msgError = '';
            if (this.state.title?.length === 0) {
                msgError = translate('alert_enter_name');
            }
            if (msgError?.length > 0) {
                return global.props.alert({
                    title: translate('notify'),
                    show: true,
                    message: msgError,
                    type: 'info',
                    confirmText: 'Đóng',
                    onConfirmPressed: () => {
                        global.props.alert({ show: false });
                    }
                });
            }
            global.props.showLoader();
            const reponse = await this.props.actionTicket.createTicket(body);
            this.props.actionTicket.getDetailTicket(ticket.id);
            if (reponse) {
                if (reponse?.errorReason) {
                    if (
                        reponse?.errorReason ===
                            'Lỗi kết nối mạng. Vui lòng kiểm tra lại' ||
                        reponse?.errorReason === 'Network request timed out'
                    ) {
                        Toast.show({
                            type: 'error',
                            text1: 'Lỗi kết nối mạng. Vui lòng kiểm tra lại',
                            position: 'bottom'
                        });
                    } else {
                        Toast.show({
                            type: 'error',
                            text1: 'Cập nhật thất bại',
                            position: 'bottom'
                        });
                    }
                } else {
                    this.retryListTicket();
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('edit_ticket_success'),
                        position: 'bottom'
                    });
                    setTimeout(() => {
                        Toast.hide();
                        this.props.navigation.goBack();
                    }, 1000);
                }
            }
        } catch (error) {
            global.props.hideLoader();
            console.error(error);
            if (
                error?.errorReason ===
                    'Lỗi kết nối mạng. Vui lòng kiểm tra lại' ||
                error?.errorReason === 'Network request timed out'
            ) {
                Toast.show({
                    type: 'error',
                    text1: 'Lỗi kết nối mạng. Vui lòng kiểm tra lại',
                    position: 'bottom'
                });
            } else {
                Toast.show({
                    type: 'error',
                    text1: translate('edit_ticket_fail'),
                    position: 'bottom'
                });
            }
        }
    };
    handleData = () => {
        const extraDataJsonObject =
            this.props.detailTicket?.data?.ticket?.extraDataJsonObject;
        let listFile = [];
        if (extraDataJsonObject.checkinImageFileID) {
            let bodyCheckIn = {
                id: extraDataJsonObject.checkinImageFileID,
                filename: extraDataJsonObject.checkinImageFileName,
                filemime: 'image/jpeg'
            };
            listFile.push(bodyCheckIn);
        }
        if (extraDataJsonObject.checkoutImageFileID) {
            let bodyCheckOut = {
                id: extraDataJsonObject.checkoutImageFileID,
                filename: extraDataJsonObject.checkoutImageFileName,
                filemime: 'image/jpeg'
            };
            listFile.push(bodyCheckOut);
        }
        return listFile;
    };
    render() {
        const { viewMedia } = this.state;
        const { xworkData, listFileTicket, streamToken, detailTicket } =
            this.props;
        const { ticket } = this.props.detailTicket.data;
        const { component, common } = xworkData;
        const { data } = detailTicket;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('edit_ticket')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    this.props.navigation.goBack();
                }}
                actionRetry={() => {}}
                messageLoading={translate('getting_job_ticket')}
                messageError={translate('something_wrong_server')}
                colorTitle>
                <KeyboardAvoidingView
                    style={{ flex: 1 }}
                    behavior={Platform.OS == 'ios' ? 'padding' : 'height'}
                    extraHeight={Mixins.scale(125)}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled">
                    <View style={style.container}>
                        <View>
                            <InputText
                                title={translate('ticket_name')}
                                placeholder={translate('enter_ticket_name')}
                                value={this.state.title}
                                onChangeText={this.changeTitle}
                            />
                            <TouchableOpacity
                                onPress={() => {
                                    Keyboard.dismiss();
                                    this.handleShowModalUser(0);
                                }}
                                style={{
                                    flexDirection: 'column'
                                }}>
                                <View style={{ flexDirection: 'row' }}>
                                    <Image
                                        style={style.imgLeft}
                                        source={{ uri: 'ic_profile' }}
                                    />
                                    <MyText
                                        text={translate('ticket_creator')}
                                        addSize={1}
                                        style={{
                                            color: Colors.BLACK_HEADER_TITLE,
                                            marginHorizontal: Mixins.scale(8)
                                        }}
                                        typeFont="medium"
                                    />
                                </View>

                                <View style={style.creator}>
                                    <Image
                                        style={style.imgWatched}
                                        source={{
                                            uri: `${CONST_API.baseAvatarURI}${data?.ticket?.creatorImage}`
                                        }}
                                    />
                                    <MyText
                                        addSize={1}
                                        text={`${data?.ticket?.creatorUserName}-${data?.ticket?.creatorLastName} ${data?.ticket?.creatorFirstName}`}
                                        style={{
                                            color: Colors.BLACK_HEADER_TITLE,
                                            marginLeft: 8
                                        }}
                                        numberOfLines={1}
                                    />
                                </View>
                            </TouchableOpacity>
                            {ticket?.watcherUsers?.length > 0 && (
                                <TouchableOpacity
                                    activeOpacity={1}
                                    onPress={() => {
                                        Keyboard.dismiss();
                                        this.handleShowModalUser(2);
                                    }}>
                                    <View
                                        style={{
                                            flexDirection: 'row'
                                        }}>
                                        <Image
                                            style={style.imgLeft}
                                            source={{ uri: 'ic_add_group' }}
                                        />
                                        <MyText
                                            text={translate('watcher')}
                                            addSize={1}
                                            style={{
                                                color: Colors.BLACK_HEADER_TITLE,
                                                marginHorizontal:
                                                    Mixins.scale(8)
                                            }}
                                            typeFont="medium"
                                        />
                                    </View>

                                    <View>
                                        {ticket?.manager?.userName && (
                                            <MyText
                                                style={{
                                                    color: Colors.BLACK_HEADER_TITLE,
                                                    marginTop: Mixins.scale(16)
                                                }}
                                                text={`${translate(
                                                    'manager'
                                                )}: ${
                                                    ticket?.manager?.userName
                                                } - ${
                                                    ticket?.manager
                                                        ?.userLastName
                                                } ${
                                                    ticket?.manager
                                                        ?.userFirstName
                                                }`}
                                            />
                                        )}
                                        {ticket?.leader?.userName && (
                                            <MyText
                                                style={{
                                                    color: Colors.BLACK_HEADER_TITLE,
                                                    marginTop: Mixins.scale(16)
                                                }}
                                                text={`${translate(
                                                    'leader'
                                                )}: ${
                                                    ticket?.leader?.userName
                                                } - ${
                                                    ticket?.leader?.userLastName
                                                } ${
                                                    ticket?.leader
                                                        ?.userFirstName
                                                }`}
                                            />
                                        )}
                                    </View>
                                </TouchableOpacity>
                            )}
                            {this.state.isShowModalUser && (
                                <ModalUser
                                    title={this.state.titleModal}
                                    isVisible={this.state.isShowModalUser}
                                    onPressDimiss={() => {
                                        this.setState({
                                            isShowModalUser: false
                                        });
                                    }}
                                    detailTicket={this.props.detailTicket.data}
                                    listUser={this.state.listUser}
                                />
                            )}
                            <View
                                style={[
                                    style.styleRow,
                                    {
                                        justifyContent: 'space-between',
                                        marginTop: 16
                                    }
                                ]}>
                                <View
                                    style={[style.viewRowBottom, { flex: 6 }]}>
                                    <Image
                                        style={style.imgCalendar}
                                        source={{ uri: 'ic_calendar' }}
                                    />
                                    <MyText
                                        text={translate('creation_time')}
                                        addSize={1}
                                        style={{
                                            color: Colors.BLACK_HEADER_TITLE,
                                            marginLeft: Mixins.scale(8)
                                        }}
                                        typeFont="medium"
                                    />
                                </View>
                                <View style={style.viewDate}>
                                    <MyText
                                        text={`${moment(
                                            ticket?.createTimeLong
                                        ).format('DD/MM/YYYY, HH:mm')}`}
                                        addSize={1}
                                        style={style.txtDefault}
                                    />
                                </View>
                            </View>
                            {this.renderFile()}
                        </View>
                        <TouchableOpacity
                            onPress={this.handleButtonSave}
                            style={style.btnSave}>
                            <MyText
                                text={translate('update')}
                                addSize={1}
                                style={{ color: Colors.WHITE }}
                            />
                        </TouchableOpacity>

                        <Toast position="bottom" config={toastConfig} />
                        {viewMedia.visible && (
                            <common.ViewFile
                                visible={viewMedia.visible}
                                imageUrls={this.handleData()}
                                index={viewMedia.index}
                                onPress={this.onRequestClose}
                                checkQuickTicket={this.checkQuickTicket()}
                                onSwipeDown={() => this.onRequestClose()}
                                onRequestClose={this.onRequestClose}
                                streamToken={streamToken?.data?.token}
                                toastConfig={toastConfig}
                                isClickVideo={this.checkClickVideo(
                                    listFileTicket?.data[viewMedia.index]
                                )}
                            />
                        )}
                    </View>
                    {/* </TouchableWithoutFeedback> */}
                </KeyboardAvoidingView>
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken
    };
};
//
const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(EditSchedule);
