import React, { PureComponent } from 'react';
import {
    Image,
    Keyboard,
    Platform,
    TouchableOpacity,
    View
} from 'react-native';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { connect } from 'react-redux';
import {
    ChooseForm,
    DropdownInput,
    DropdownType,
    InputText,
    RenderFile,
    RenderStatus
} from '../Component/ComponentGroundTask';
import { bindActionCreators } from 'redux';
import { checkErrorTask, checkCategory, checkPartner } from './handle';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { requestPermission, openSetting } from '@mwg-kits/core';
import { stylesTicket } from '../../stylesTicket';
import { toastConfig } from '../../../GroupTicket/MemberGroupTicket';
import * as _actionGroundTask from './action';
import * as _actionTicket from '../../action';
import AsyncStorage from '@react-native-async-storage/async-storage';
import DocumentPicker, { types } from 'react-native-document-picker';
import ImagePicker from 'react-native-image-crop-picker';
import moment from 'moment';
import Toast from 'react-native-toast-message';
const { translate } = global.props.getTranslateConfig();
import { createGroundTask as styles } from './style';

class CreateGroupTasks extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            taskId: null,
            title: '',
            timeFrom: new Date(),
            timeTo: new Date(new Date().getTime() + 60 * 60 * 1000),
            type: null,
            form: 0,
            location: '',
            address: '',
            partner: '',
            phone: '',
            target: '',
            result: '',
            file: [],
            note: '',
            isDone: null,
            isEdit: false,
            openType: false,
            isShowModalLocationGeo: false,
            dataLengthLocation: null,
            isDatePickerVisible: false,
            typeDate: 0,
            arrayLocation: null,
            canScroll: false,
            isLoading: false,
            showStarMarket: false
        };
        this.noteRef = React.createRef();
        this.flatListRef = React.createRef();
    }
    componentDidMount = () => {
        const { actionGroundTask } = this.props;
        actionGroundTask.setLocation({});
        if (this.props.route?.params) {
            const { item } = this.props.route.params;
            const { extraData } = item;
            const bodyLocation = {
                id: extraData?.houseCode,
                name: extraData?.houseName
            };
            const bodySetLocation = {
                provinceId: extraData?.provinceID,
                provinceName: extraData?.province,
                districtId: extraData?.districtID,
                districtName: extraData?.district,
                wardId: extraData?.wardsID,
                wardName: extraData?.wards
            };
            const bodyType = {
                id: extraData?.categoryID,
                name: extraData?.categoryName
            };
            this.changeTaskId(item?.id);
            this.changeTitle(extraData?.workingTitle);
            this.changeLocation(bodyLocation);
            this.changeTimeFrom(new Date(this.dateTimeToDate(item?.startTime)));
            this.changeTimeTo(new Date(this.dateTimeToDate(item?.dueTime)));
            this.changeType(bodyType);
            this.changeForm(extraData?.isStore ? 0 : 1);
            actionGroundTask.setLocation(bodySetLocation);
            this.changeAddress(extraData?.workingAddress);
            this.changePartner(extraData?.partnerName);
            this.changePhone(extraData?.partnerPhone);
            this.changeTarget(extraData?.workingTarget);
            this.changeResult(extraData?.workingResult);
            this.changeNote(extraData?.note);
            this.changeStatus(this.returnStatus(item?.done));
        }
    };
    changeLoading = (state) => {
        this.setState({ isLoading: state });
    };
    returnStatus = (isDone) => {
        if (isDone === true) return { id: 1, name: translate('finish') };
        return { id: 0, name: translate('unfinish') };
    };
    changeTaskId = (id) => {
        this.setState({ taskId: id });
    };
    changeFile = (array) => {
        this.setState({ file: array });
    };
    changeTitle = (text) => {
        this.setState({ title: text });
    };
    changeTimeFrom = (date) => {
        this.setState({ timeFrom: date });
    };
    changeTimeTo = (date) => {
        this.setState({ timeTo: date });
    };
    changeType = (ob) => {
        this.setState({ type: ob });
    };
    changeForm = (text) => {
        this.setState({ form: text });
    };
    changeLocation = (ob) => {
        this.setState({ location: ob });
    };
    changeAddress = (text) => {
        this.setState({ address: text });
    };
    changePartner = (text) => {
        this.setState({ partner: text });
    };
    changePhone = (text) => {
        this.setState({ phone: text });
    };
    changeTarget = (text) => {
        this.setState({ target: text });
    };
    changeResult = (text) => {
        this.setState({ result: text });
    };
    changeNote = (text) => {
        this.setState({ note: text });
    };
    changeStatus = (ob) => {
        this.setState({ isDone: ob });
    };
    initListTask = () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 20 }
        };
        this.props.actionTicket.getListTask(params);
    };
    onPressBack = () => {
        const { detailTicket } = this.props;
        this.props.navigation.navigate('DetailTicket', {
            id: detailTicket?.data?.ticket?.id,
            indexHeader: 2
        });
    };
    handleButtonCreate = async () => {
        const {
            taskId,
            title,
            timeFrom,
            timeTo,
            type,
            form,
            location,
            address,
            partner,
            phone,
            target,
            result,
            note,
            isDone
        } = this.state;

        const { detailTicket, xworkData, dataLocation } = this.props;
        const { fullProfile } = xworkData;
        try {
            global.props.showLoader();
            this.changeLoading(true);
            const body = {
                taskId: taskId,
                ticketId: detailTicket?.data?.ticket?.id,
                arrayReceivedUserId: [fullProfile.id],
                ticketTaskContent: title,
                ticketTaskStartTime: timeFrom.getTime(),
                ticketTaskDueTime: timeTo.getTime(),
                ticketTaskSubject: type?.name,
                ticketTaskDone: isDone?.id === 1 ? true : false,
                groundWroking: {
                    workingTitle: title,
                    categoryID: type?.id,
                    categoryName: type?.name,
                    isStore: form === 0 ? true : false,
                    houseCode: location?.id,
                    houseName: location?.name,
                    provinceID: dataLocation?.provinceId,
                    province: dataLocation?.provinceName,
                    districtID: dataLocation?.districtId,
                    district: dataLocation?.districtName,
                    wardsID: dataLocation?.wardId,
                    wards: dataLocation?.wardName,
                    workingAddress: address,
                    partnerName: partner,
                    partnerPhone: phone,
                    workingTarget: target,
                    workingResult: result,
                    note: note
                }
            };
            const isError = await checkErrorTask(body);
            if (!isError) {
                const reponse =
                    await this.props.actionGroundTask.createGroundTask1(
                        body,
                        this.state.file
                    );

                if (reponse) {
                    if (reponse.error === true) {
                        global.props.hideLoader();
                        this.changeLoading(false);
                        Toast.show({
                            type: 'error',
                            text1: this.props.route.params
                                ? translate('update_fail')
                                : translate('create_ticket_fail'),
                            position: 'bottom'
                        });
                        return;
                    }
                    if (xworkData.screenName === 'CreateGroundTask') {
                        AsyncStorage.setItem(
                            'EDIT_TICKET',
                            JSON.stringify(reponse)
                        );
                    }
                    setTimeout(() => {
                        this.updateDetailTicket();
                        this.retryListTicket();
                    }, 5000);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: this.props.route.params
                            ? translate('update_ticket_success')
                            : translate('create_ticket_success'),
                        position: 'bottom'
                    });
                    setTimeout(() => {
                        Toast.hide();
                        this.initListTask();
                        this.onPressBack();
                        this.changeLoading(false);
                    }, 1000);
                }
            } else {
                this.changeLoading(false);
                global.props.hideLoader();
            }
        } catch (error) {
            console.log(error.message);
            global.props.hideLoader();
            this.changeLoading(false);
            Toast.show({
                type: 'error',
                text1: this.props.route.params
                    ? translate('update_fail')
                    : translate('create_ticket_fail'),
                position: 'bottom'
            });
        }
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    updateDetailTicket = async () => {
        const { detailTicket } = this.props;
        // this.setState({
        //     refesshDetail: true
        // });
        await this.props.actionTicket.getDetailTicket(
            detailTicket?.data?.ticket?.id
        );
        this.handleGlobalState();
        // this.setState({
        //     refesshDetail: false
        // });
    };
    retryListTicket = () => {
        const { detailTicket } = this.props;
        const data = {
            supportServiceId: detailTicket?.data?.ticket?.supportServiceId
        };
        this.props.actionTicket.getListTicket(data);
    };
    pickerGallery = async (item) => {
        try {
            await requestPermission('photo');
            if (item.id === 0) {
                this.onShowPicker();
            } else {
                this.upLoadFile();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message: translate('access_library'),
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };
    fileEdit = async (image) => {
        try {
            const { detailTicket } = this.props;
            const { item } = this.props.route.params;
            // global.props.showLoader();
            global.props.hideLoader();
            const body = {
                taskId: item.id,
                ticketId: detailTicket?.data?.ticket?.id,
                arrayReceivedUserId: [item.creatorId],
                ticketTaskDone: item?.done,
                startTime: item?.startTime,
                dueTime: item?.dueTime,
                ticketTaskContent: item?.content,
                ticketTaskSubject: item?.subject
            };
            const sendImage =
                await this.props.actionGroundTask.createGroundTask1(
                    body,
                    image
                );
            try {
                if (sendImage) {
                    if (sendImage.error) {
                        global.props.hideLoader();
                        Toast.show({
                            type: 'error',
                            text1: translate('upload_only_img_fail'),
                            position: 'bottom'
                        });
                        return;
                    }
                    this.initListTask();
                    this.props.actionGroundTask.filesTask(sendImage[0].files);
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_img_suc'),
                        position: 'bottom'
                    });
                }
            } catch (error) {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('upload_only_img_fail'),
                    position: 'bottom'
                });
            }
        } catch (e) {
            console.log(e, '12312312321');
        }
    };

    onShowPicker = async () => {
        try {
            await ImagePicker.openPicker({
                mediaType: 'photo',
                maxFiles: 5,
                multiple: true,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                maximumVideoDuration: 60000,
                compressVideoPreset: 'HighestQuality'
            }).then(async (image) => {
                if (this.props.route.params) {
                    this.fileEdit(image);
                } else {
                    image?.map((item) => {
                        this.setState({ file: [...this.state.file, item] });
                    });
                }
            });
        } catch (e) {
            console.log(e, '12312312321');
        }
    };
    handleShowVideo = async () => {
        const { detailTicket, initListFile } = this.props;
        const respone = await ImagePicker.openPicker({
            mediaType: 'video',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            durationLimit: 60,
            maximumVideoDuration: 60000,
            compressVideoPreset: 'HighestQuality'
        });
        try {
            if (respone) {
                const body = {
                    files: respone,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };

                const sendVideo =
                    await this.props.actionTicket.uploadImageComment(body);
                global.props.hideLoader();
                initListFile();

                if (sendVideo) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_video_success'),
                        position: 'bottom'
                    });
                }
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('upload_video_failed'),
                position: 'bottom'
            });
        }
    };
    upLoadFile = async () => {
        try {
            const response = await DocumentPicker.pick({
                allowMultiSelection: true,
                presentationStyle: 'fullScreen',
                type: [
                    types.pdf,
                    types.doc,
                    types.xls,
                    types.pptx,
                    types.ppt,
                    types.docx,
                    types.xlsx
                ]
            });
            const { detailTicket } = this.props;

            if (response) {
                if (this.props.route.params) {
                    const { item } = this.props.route.params;
                    const body = {
                        taskId: item.id,
                        ticketId: detailTicket?.data?.ticket?.id,
                        arrayReceivedUserId: [item.creatorId],
                        ticketTaskDone: item?.done,
                        startTime: item?.startTime,
                        dueTime: item?.dueTime,
                        ticketTaskContent: item?.content,
                        ticketTaskSubject: item?.subject
                    };

                    const sendFile =
                        await this.props.actionGroundTask.uploadFileGround(
                            body,
                            response
                        );
                    try {
                        if (sendFile) {
                            if (sendFile.error) {
                                global.props.hideLoader();
                                Toast.show({
                                    type: 'error',
                                    text1: translate('upload_file_fail'),
                                    position: 'bottom'
                                });
                                return;
                            }
                            this.initListTask();
                            this.props.actionGroundTask.filesTask(
                                sendFile[0].files
                            );
                            global.props.hideLoader();
                            Toast.show({
                                type: 'success',
                                text1: translate('upload_file_success'),
                                position: 'bottom'
                            });
                        }
                    } catch (error) {
                        Toast.show({
                            type: 'error',
                            text1: translate('upload_file_fail'),
                            position: 'bottom'
                        });
                    }
                } else {
                    response?.map((e) => {
                        this.setState({ file: [...this.state.file, e] });
                    });
                }
            }
        } catch (e) {
            console.log(e);
        }
    };
    handleRemoveFile = (item) => {
        global.props.alert({
            show: true,
            title: this.checkMime(item, 'image')
                ? translate('delete_pic')
                : this.checkMime(item, 'video')
                ? 'Xoá video'
                : translate('delete_file'),
            titleColor: {
                color: Colors.DARK_RED_30
            },
            message: this.checkMime(item, 'image')
                ? translate('confirm_delete_file')
                : this.checkMime(item, 'video')
                ? 'Bạn có muốn xoá video này'
                : translate('ask_delete_file'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
                this.removeFile(item);
            },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
            }
        });
    };
    removeFile = async (item) => {
        if (this.props.route.params) {
            try {
                //global.props.showLoader();
                const response =
                    await this.props.actionGroundTask.removeFileTask(item?.id);
                global.props.hideLoader();

                if (response) {
                    if (response.error) {
                        Toast.show({
                            type: 'error',
                            text1: translate('delete_file_failed'),
                            position: 'bottom'
                        });
                    }
                    this.initListTask();
                    const reloadFile = this.props.selectedTask.files.filter(
                        (e) => {
                            return e.id !== item?.id;
                        }
                    );
                    global.props.hideLoader();
                    this.props.actionGroundTask.filesTask(reloadFile);
                    Toast.show({
                        type: 'success',
                        text1: translate('delete_file_success'),
                        position: 'bottom'
                    });
                } else {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'error',
                        text1: translate('delete_file_failed'),
                        position: 'bottom'
                    });
                }
            } catch (error) {
                global.props.hideLoader();
                console.log(error);
                Toast.show({
                    type: 'error',
                    text1: translate('delete_file_failed'),
                    position: 'bottom'
                });
            }
        } else {
            const { file } = this.state;
            const index = file.indexOf(item);
            file.map((image) => {
                if (image.path === item.path) {
                    file.splice(index, 1);
                    this.setState({ file: [...file] });
                }
            });
        }
    };

    handleUpFile = async (item) => {
        setTimeout(() => {
            this.pickerGallery(item);
        }, 1000);
    };

    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };
    checkMime = (item, type) => {
        if (
            item?.filemime?.includes(type) ||
            item?.type?.includes(type) ||
            item?.mime?.includes(type)
        )
            return true;
        return false;
    };
    renderFile = () => {
        const { xworkData, detailTicket } = this.props;
        const { currentLang } = xworkData;

        return (
            <RenderFile
                handleUpFile={this.handleUpFile}
                listFileTicket={
                    this.props.route.params
                        ? this.props.selectedTask.files
                        : this.state.file
                }
                handleRemoveFile={this.handleRemoveFile}
                setViewMedia={this.setViewMedia}
                isShowRemove={true}
                hideAddFile={
                    detailTicket.data?.ticket?.supportServiceType ===
                    'APPROVED_MATERIAL_COST'
                }
                translate={translate}
                currentLang={currentLang}
                createTask={!this.props.route.params}
                isTask
            />
        );
    };
    loadMoreLocation = (text) => {
        const { dataLengthGeoLocation } = this.state;
        const { detailTicket } = this.props;
        const body = {
            supportServiceId:
                detailTicket?.data?.ticket?.supportServiceId || -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: dataLengthGeoLocation + 10,
                search: text || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(body);
        this.setState({
            dataLengthGeoLocation: dataLengthGeoLocation + 10
        });
    };
    actionSearch = (txt) => {
        const { detailTicket } = this.props;
        const data = {
            supportServiceId:
                detailTicket?.data?.ticket?.supportServiceId || -1,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 20, search: txt },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
    };
    AllModal = (props) => {
        const { isShowModalLocationGeo } = this.state;
        const { listLocationGeo } = this.props;
        const { ModalListServiceV2 } = props.component;
        return (
            <View>
                <ModalListServiceV2
                    isVisible={isShowModalLocationGeo}
                    props={this.props}
                    isShowCodeService={true}
                    titleModal={translate('choose_supermarket')}
                    listData={listLocationGeo?.data}
                    serviceSelected={this.state.location}
                    hideModalSynch={() => {
                        this.setState({
                            isShowModalLocationGeo: false
                        });
                    }}
                    onPressSave={(item) => {
                        this.setState({
                            isShowModalLocationGeo: false,
                            location: item
                        });
                    }}
                    onEndReached={(text) => {
                        this.loadMoreLocation(text);
                    }}
                    actionSearch={this.actionSearch}
                />
            </View>
        );
    };
    dateTimeToDate = (string) => {
        let date = string.split(' ')[0];
        let time = string.split(' ')[1];
        let datePart = date.split('-');
        let timePart = time.split(':');
        let res = new Date(
            datePart[2],
            parseInt(datePart[1]) - 1,
            datePart[0],
            timePart[0],
            timePart[1],
            timePart[2]
        );
        return res;
    };
    actionLocation = (type) => {
        //this.setState({ isShowModalLocation: true });
        const { dataLocation, navigation } = this.props;
        if (
            type === 1 &&
            (dataLocation.provinceId == '' ||
                dataLocation.provinceId === undefined ||
                dataLocation.provinceId === null)
        ) {
            Toast.show({
                type: 'error',
                text1: `${translate('warning_select')} ${translate(
                    'province'
                )}`,
                position: 'bottom'
            });
        } else if (
            type === 2 &&
            (dataLocation.districtId === '' ||
                dataLocation.districtId === undefined ||
                dataLocation.districtId === null)
        ) {
            Toast.show({
                type: 'error',
                text1: `${translate('warning_select')} ${translate(
                    'district'
                )}`,
                position: 'bottom'
            });
        } else {
            navigation.navigate('ChooseLocation', {
                type: type
            });
        }
    };
    //
    renderHours = (type, time) => {
        return (
            <TouchableOpacity
                style={styles.viewHour}
                onPress={() => {
                    this.setState({
                        typeDate: type,
                        isDatePickerVisible: !this.state.isDatePickerVisible
                    });
                }}>
                <MyText
                    text={`${moment(time).format('HH:mm')}`}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}
                />
            </TouchableOpacity>
        );
    };
    canScroll = () => {
        this.setState({ canScroll: !this.state.canScroll });
    };
    render() {
        const { xworkData, dataLocation, detailTicket } = this.props;
        const { component, DateTimePickerModal } = xworkData;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket } = component;
        const {
            title,
            type,
            form,
            location,
            address,
            partner,
            phone,
            target,
            result,
            note,
            isDone
        } = this.state;
        return (
            <View style={{ flex: 1 }}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={
                        this.props.route.params
                            ? translate('edit_work_order')
                            : translate('create_work_order')
                    }
                    actionRetry={() => {}}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    onPressBack={() => {
                        this.props.navigation.goBack();
                    }}
                    // isSuccess={!detailTicket?.isError}
                    colorTitle>
                    <View style={styles.container}>
                        <KeyboardAwareScrollView
                            ref={this.flatListRef}
                            behavior={
                                Platform.OS == 'ios' ? 'padding' : 'height'
                            }
                            extraHeight={230}
                            enableResetScrollToCoords={this.state.canScroll}
                            showsVerticalScrollIndicator={false}
                            keyboardShouldPersistTaps="never">
                            <InputText
                                title={translate('work_name')}
                                value={title}
                                placeholder={translate('enter_ticket_name')}
                                showStar
                                onChangeText={this.changeTitle}
                            />
                            <View style={styles.viewTabClock}>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}>
                                    <Image
                                        source={{ uri: 'ic_clock' }}
                                        style={styles.styleImgClock}
                                    />
                                    <MyText
                                        text={translate('time')}
                                        addSize={2}
                                        typeFont="medium"
                                        style={{
                                            color: Colors.GRAYF10
                                        }}>
                                        <MyText
                                            text="* "
                                            addSize={2}
                                            typeFont="medium"
                                            numberOfLines={1}
                                            style={stylesTicket.txtStart}
                                        />
                                    </MyText>
                                </View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}>
                                    {this.renderHours(0, this.state.timeFrom)}
                                    <MyText
                                        text={' - '}
                                        style={{
                                            paddingLeft: Mixins.scale(4)
                                        }}
                                    />
                                    {this.renderHours(1, this.state.timeTo)}
                                </View>
                            </View>
                            <DropdownType
                                showStar
                                title={translate('job_type')}
                                onChangeItem={this.changeType}
                                selectedTemplate={type}
                                listTemplateTask={
                                    this.props.listTemplateTask?.data
                                }
                            />
                            <ChooseForm
                                onTick={this.changeForm}
                                choose={form}
                            />
                            <DropdownInput
                                title={translate('supermarket')}
                                placce
                                text={location.name}
                                onPress={() => {
                                    Keyboard.dismiss();
                                    this.setState({
                                        isShowModalLocationGeo: true
                                    });
                                }}
                                showStar={checkCategory(type?.name)}
                            />
                            <View
                                style={{
                                    flexDirection: 'row',
                                    marginBottom: Mixins.scale(16)
                                }}>
                                <Image
                                    source={{ uri: 'ic_location' }}
                                    style={styles.imgLocation}
                                />
                                <MyText
                                    text={translate('workplace')}
                                    addSize={2}
                                    typeFont="medium"
                                    style={{
                                        color: Colors.GRAYF10
                                    }}
                                />
                            </View>
                            <DropdownInput
                                title={translate('province')}
                                text={dataLocation.provinceName}
                                style={{ marginBottom: Mixins.scale(16) }}
                                onPress={() => {
                                    this.actionLocation(0);
                                }}
                            />
                            <DropdownInput
                                title={translate('district')}
                                text={dataLocation.districtName}
                                style={{ marginBottom: Mixins.scale(16) }}
                                onPress={() => {
                                    this.actionLocation(1);
                                }}
                            />
                            <DropdownInput
                                title={translate('ward')}
                                text={dataLocation.wardName}
                                style={{ marginBottom: Mixins.scale(16) }}
                                onPress={() => {
                                    this.actionLocation(2);
                                }}
                            />
                            <InputText
                                title={translate('specific_house')}
                                value={address}
                                placeholder={translate(
                                    'street_and_house_number_placeholder'
                                )}
                                onChangeText={this.changeAddress}
                            />
                            <InputText
                                title={translate('partner_name')}
                                value={partner}
                                placeholder={translate('enter_partner_name')}
                                onChangeText={this.changePartner}
                                showStar={checkPartner(type?.name)}
                            />
                            <InputText
                                number
                                title={translate('partner_phone')}
                                value={phone}
                                placeholder={translate('enter_partner_phone')}
                                onChangeText={this.changePhone}
                                showStar={checkPartner(type?.name)}
                            />
                            <InputText
                                title={translate('target_work')}
                                value={target}
                                multiline={true}
                                onChangeText={this.changeTarget}
                                showStar
                            />
                            <InputText
                                title={translate('result_achieve')}
                                value={result}
                                multiline={true}
                                onChangeText={this.changeResult}
                            />
                            {this.renderFile()}
                            <InputText
                                ref={this.noteRef}
                                title={translate('note')}
                                value={note}
                                multiline={true}
                                onChangeText={this.changeNote}
                                onFocus={this.canScroll}
                                onBlur={this.canScroll}
                            />
                            {this.props.route.params &&
                                isDone !== null &&
                                detailTicket?.data?.ticket
                                    ?.isGroundTaskCreate && (
                                    <RenderStatus
                                        isDone={isDone}
                                        onChangeItem={this.changeStatus}
                                    />
                                )}
                        </KeyboardAwareScrollView>
                        <TouchableOpacity
                            disabled={this.props.isLoading}
                            onPress={this.handleButtonCreate}
                            style={styles.btnCreate}>
                            <MyText
                                text={
                                    this.props.route.params
                                        ? translate('edit_ticket')
                                        : translate('create')
                                }
                                style={{ color: Colors.WHITE }}
                                addSize={2}
                            />
                        </TouchableOpacity>

                        <this.AllModal component={component} />
                    </View>
                    <DateTimePickerModal
                        isVisible={this.state.isDatePickerVisible}
                        mode="time"
                        locale="en_GB"
                        onConfirm={(date) => {
                            this.setState({
                                isDatePickerVisible:
                                    !this.state.isDatePickerVisible
                            });
                            if (this.state.typeDate === 0) {
                                this.changeTimeFrom(date);
                            } else this.changeTimeTo(date);
                        }}
                        onCancel={() => {
                            this.setState({
                                isDatePickerVisible:
                                    !this.state.isDatePickerVisible
                            });
                        }}
                        confirmTextIOS={translate('confirm_approve')}
                        cancelTextIOS={translate('cancel')}
                    />
                    <Toast position="bottom" config={toastConfig} />
                </WrapperContainerTicket>
            </View>
        );
    }
}
//
const mapStateToProps = function (state) {
    return {
        detailTicket: state.ticketReducer.detailTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        dataLocation: state.groundTaskReducer.listLocation.dataLocation,
        listTemplateTask: state.ticketReducer.listTemplateTask,
        xworkData: state.groupTicketReducer.xworkData,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken,
        listTask: state.ticketReducer.listTask,
        selectedTask: state.groundTaskReducer.selectedTask
    };
};
const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(CreateGroupTasks);
