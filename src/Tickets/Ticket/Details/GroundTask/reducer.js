import * as _action from './action';
import * as _state from '../../../state';

const groundTaskReducer = function (state = _state.ticketState, action) {
    switch (action.type) {
        case _action.groundTaskAction.START_GET_LOCATION:
            return {
                ...state,
                listLocation: {
                    ...state.listLocation,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.groundTaskAction.STOP_GET_LOCATION:
            console.log('bang', state.listLocation.data);
            return {
                ...state,
                listLocation: {
                    ...state.listLocation,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.groundTaskAction.SET_LOCATION:
            return {
                ...state,
                listLocation: {
                    ...state.listLocation,
                    dataLocation: action.data
                }
            };
        case _action.groundTaskAction.SELECTED_TASK:
            return {
                ...state,
                selectedTask: {
                    ...state.selectedTask,
                    files: action.data.files,
                    data: action.data.task
                }
            };
        case _action.groundTaskAction.FILES_TASK:
            return {
                ...state,
                selectedTask: {
                    ...state.selectedTask,
                    files: action.data
                }
            };
        default:
            return state;
    }
};
export { groundTaskReducer };
