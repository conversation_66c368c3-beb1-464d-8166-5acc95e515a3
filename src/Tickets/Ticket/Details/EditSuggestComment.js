import React, { Component } from 'react';
import {
    View,
    Platform,
    KeyboardAvoidingView,
    TouchableOpacity,
    TextInput
} from 'react-native';
import { connect } from 'react-redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import Toast from 'react-native-toast-message';
const { translate } = global.props.getTranslateConfig();

export class EditSuggestComment extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isEdit: false,
            itemSuggest: { name: '', id: -1, priority: -1 }
        };
    }
    deleteSuggestComment = () => {
        global.props.alert({
            show: true,
            title: translate('delete_quick_reply'),
            titleColor: { color: Colors.DARK_RED_30 },
            message: translate('confirm_delete_quick_rep'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },
            onConfirmPressed: async () => {
                global.props.alert({ show: false });
                if (helper.hasProperty(this.state.itemSuggest, 'id')) {
                    global.props.showLoader(`${translate('processing')}...`);
                    let response =
                        await this.props.actionTicket.deleteCommentSuggest(
                            this.state.itemSuggest.id
                        );
                    if (
                        helper.IsValidateObject(response) &&
                        helper.hasProperty(response, 'error') &&
                        !response.error
                    ) {
                        await this.props.actionTicket.getCommentSuggest(true);
                        Toast.show({
                            type: 'success',
                            text1: translate('delete_quick_rep_success'),
                            position: 'bottom'
                        });
                        this.props.navigation.goBack();
                    } else {
                        Toast.show({
                            type: 'error',
                            text1: translate('delete_quick_rep_fail'),
                            position: 'bottom'
                        });
                    }

                    global.props.hideLoader();
                }
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };
    componentDidMount() {
        if (
            helper.IsValidateObject(this.props.route.params) &&
            helper.hasProperty(this.props.route.params, 'id')
        ) {
            this.setState({
                itemSuggest: {
                    id: this.props.route.params.id,
                    name: this.props.route.params.name,
                    priority: this.props.route.params.priority
                }
            });
        }
    }

    updateSuggestComment = async () => {
        if (this.state.itemSuggest.name.length > 0) {
            global.props.showLoader(`${translate('processing')}...`);

            let response =
                await this.props.actionTicket.createUpdateCommentSuggest({
                    type: 'TICKET',
                    name: this.state.itemSuggest.name,
                    id: this.state.itemSuggest.id,
                    priority: this.state.itemSuggest.priority
                });

            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.hasProperty(response.object, 'name') &&
                helper.hasProperty(response, 'error') &&
                !response.error
            ) {
                await this.props.actionTicket.getCommentSuggest(true);
                Toast.show({
                    type: 'success',
                    text1: translate('update_quick_rep_success'),
                    position: 'bottom'
                });
                this.props.navigation.goBack();
            } else {
                Toast.show({
                    type: 'error',
                    text1: translate('update_quick_rep_fail'),
                    position: 'bottom'
                });
            }
            global.props.hideLoader();
        } else {
            Toast.show({
                type: 'error',
                text1: translate('quick_rep_not_empty'),
                position: 'bottom'
            });
        }
    };
    renderContent = () => {
        return (
            <View style={{ marginHorizontal: 16, flex: 1 }}>
                <MyText
                    text={translate('content')}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: 24
                    }}
                />

                <TextInput
                    value={this.state.itemSuggest.name}
                    multiline
                    onChangeText={(text) => {
                        this.setState((prevState) => ({
                            itemSuggest: {
                                ...prevState.itemSuggest,
                                name: text
                            }
                        }));
                    }}
                    textAlignVertical="top"
                    style={{
                        borderColor: Colors.DARK_ORANGE_15,
                        borderRadius: Mixins.scale(16),
                        borderWidth: 1,
                        color: Colors.BLACK,
                        fontSize: 16,
                        fontWeight: '400',
                        maxHeight: 200,
                        minHeight: 80,
                        marginTop: Mixins.scale(8),
                        paddingHorizontal: Mixins.scale(8),
                        paddingVertical: 5
                    }}
                />

                <TouchableOpacity
                    onPress={this.deleteSuggestComment}
                    style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 1,
                        borderRadius: 12,
                        borderColor: Colors.DARK_RED_30,
                        paddingVertical: 7,
                        marginTop: 24
                    }}>
                    <MyText
                        text={translate('delete_quick_reply')}
                        addSize={2}
                        style={{ color: Colors.DARK_RED_30 }}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    render() {
        const { xworkData } = this.props;
        const { component } = xworkData;
        if (component === undefined) return null;
        const { WrapperContainerTicket } = component;
        return (
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1, backgroundColor: Colors.WHITE }}>
                <View style={{ flex: 1 }}>
                    <WrapperContainerTicket
                        navigation={this.props.navigation}
                        nameTitle={translate('edit_rep_template')}
                        centerAlign={false}
                        actionRetry={() => {}}
                        colorBackButton={Colors.DARK_BLUE_50}
                        onPressBack={() => this.props.navigation.goBack()}
                        isSuccess={true}
                        messageEmpty={translate('no_quick_reply_template')}
                        messageLoading={translate('getting_quick_reply_list')}
                        messageError={translate('something_wrong_server')}
                        isError={false}
                        isLoading={false}
                        titleButtonEditTicket={translate('save')}
                        onPressEditTicket={
                            this.props.route?.params?.name !==
                            this.state.itemSuggest.name
                                ? () => this.updateSuggestComment()
                                : null
                        }>
                        {this.renderContent()}
                    </WrapperContainerTicket>
                </View>
            </KeyboardAvoidingView>
        );
    }
}

const mapStateToProps = (state) => ({
    xworkData: state.groupTicketReducer.xworkData,
    suggestComment: state.ticketReducer.suggestComment,
    detailTicket: state.ticketReducer.detailTicket
});

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(EditSuggestComment);
