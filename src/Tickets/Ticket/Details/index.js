import React, { PureComponent } from 'react';
import {
    Image,
    KeyboardAvoidingView,
    Platform,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View,
    Animated,
    Keyboard,
    ActivityIndicator
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ImagePicker from 'react-native-image-crop-picker';
import { Mixins, XworkColor as Colors, ThemeXwork } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, TouchableDebounce } from '@mwg-kits/components';
import CommentTicket from './CommentTicket';
import InformationTicket from './InformationTicket';
import InfoSchedule from './GroundTask/InfoSchedule';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import * as _actionDetailTicket from './action';
import Toast from 'react-native-toast-message';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ListTask from './ListTask';
import { requestPermission, openSetting } from '@mwg-kits/core';
import { TooltipShow } from '../../GroupTicket/CompoentTooltip';
import {
    RenderHeader,
    RenderListSuggestComment,
    RenderMention,
    RenderMentionSelected,
    RenderTopHeader
} from './Component/ComponentTicket';
import * as _actionGroundTask from './GroundTask/action';
import { generateID } from '../../../socketWS/WSservice';
const { translate } = global.props.getTranslateConfig();
import { GlobalStore } from 'redux-micro-frontend';
import { RenderButtonWithoutAssignee } from './Component/ComponentDetailTicket';
import { converToDate } from '../../../utility';

class DetailTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            indexHeader: this.getIndexHeader(),
            content: '',
            isOpenMention: false,
            memberInMention: [],
            mentionedMember: [],
            initMentionCount: 0,
            refesshDetail: false,
            fetchingData: true,
            isSent: false,
            isExpand: false,
            isShowMoreTitle: false,
            showLoadSent: false,
            messageId: '',
            isShowModalResson: false,
            itemStatus: null,
            typing: false,
            isTyping: false
        };
        this.isNotification = false;
        this.animationValue = new Animated.Value(0);
        this.handleWS = this.handleWS.bind(this);
        this.client = global.props.client;
        this.globalStore = GlobalStore.Get(false);
        this.globalStateChanged = this.globalStateChanged.bind(this);
        this.checkAndConnectWS = this.checkAndConnectWS.bind(this);
        this.globalStore.SubscribeToGlobalState(
            'XworkStore',
            this.globalStateChanged
        );
        this.timeOutId = -1;
    }

    getIndexHeader = () => {
        if (this.props.route?.params && this.props.route.params?.indexHeader) {
            return this.props.route.params.indexHeader;
        }
        return 0;
    };
    actionRatingGetInfo = async () => {
        const { detailTicket } = this.props;
        if (
            detailTicket.data?.ticket?.completeTime !== undefined ||
            detailTicket.data?.ticket?.completeTimeLong !== undefined
        ) {
            await this.props.actionTicket.actionRatingGetInfo({
                ticketID: detailTicket?.data?.ticket?.id
            });
        }
    };
    callApiMBBank = () => {
        const { detailTicket } = this.props;
        if (
            detailTicket?.data?.approveType === 'PENDING' &&
            detailTicket.data?.ticket?.supportServiceType === 'APPROVE_MBBANK'
        ) {
            this.props.actionDetailTicket.getCancelReason();
            this.props.actionDetailTicket.getListBank({ storeId: 907 });
        }
    };

    componentDidMount() {
        this.props.navigation.addListener('blur', () => {
            this.props.actionTicket.stop_get_list_file({
                data: []
            });
            this.getListMember();
        });
        this.keyboardDidHideListener = Keyboard.addListener(
            'keyboardDidHide',
            this.keyboardDidHide
        );
        this.initData();
    }
    getListMember = () => {
        const { detailTicket } = this.props;
        const dataMember = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: ''
            },
            requestId: '',
            ticketId: detailTicket?.data?.ticket?.id
        };
        this.props.actionHome.getListMemberGroup(dataMember);
    };
    keyboardDidHide = () => {
        this.sendTyping(false);
        this.setState({
            typing: false
        });
    };

    componentWillUnmount() {
        const { detailTicket, typeListData } = this.props;

        if (
            this.client.state === 'connected' &&
            !helper.IsValidateObject(detailTicket?.data.approveType) &&
            !typeListData.data.includes(
                detailTicket?.data?.ticket?.supportServiceType
            )
        ) {
            this.unsubcribeWS(detailTicket?.data?.ticket?.id);
        }
        this.getListMember();
        this.keyboardDidHideListener?.remove();
    }
    globalStateChanged = (stateChanged) => {
        // / The global state has a separate attribute for all the apps registered in the store
        const { detailTicket } = this.props;
        if (
            stateChanged.XworkStore.wsState &&
            !helper.IsValidateObject(detailTicket?.data.approveType)
        ) {
            this.subcribeWsTicket();
        }
    };
    onPressSendReason = async (text) => {
        const { itemStatus } = this.state;
        const { detailTicket } = this.props;
        let body = {
            id: detailTicket?.data?.ticket?.id,
            statusId: itemStatus.id,
            reasonStatus: text
        };
        this.updateStatusTicket(body);
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };

    handleWS = (msg) => {
        const { xworkData } = this.props;
        const { type, payload } = msg;

        if (!type || type === undefined || type === null) {
            return;
        }
        if (type === 'default') {
            let newComment = JSON.parse(payload);
            let notSendUser =
                xworkData?.fullProfile?.id !== newComment?.senderId;
            let index = this.props.listComment.data?.findIndex(
                (element) => element.id === newComment?.data?.id
            );

            if (newComment.type === 'message' && notSendUser && index === -1) {
                this.addRedux(newComment.data);
                this.setState({
                    isTyping: false
                });
            }
            if (newComment.type === 'delete-message' && notSendUser) {
                let indexComment = this.props.listComment.data?.findIndex(
                    (element) => element.id === newComment?.data?.commentId
                );
                let newData = [...this.props.listComment.data];
                newData[indexComment] = {
                    ...newData[indexComment],
                    ...newComment.data,
                    updateTime: converToDate(newComment.data.updateTime),
                    status: 'DEACTIVE'
                };

                this.props.actionTicket.stop_get_list_comment({
                    data: newData
                });
            }
            if (newComment.type === 'typing' && notSendUser) {
                this.setState({
                    isTyping: true
                });
            }
            if (newComment.data.isTyping === false) {
                this.setState({
                    isTyping: false
                });
            }
        }
        console.log('WEBSOCKET SEND MESSS IN TICKET', type, msg);
    };

    checkAndConnectWS = (id) => {
        if (
            this.client &&
            this.client.state &&
            this.client.state === 'connected'
        ) {
            this.client._internalEvent.removeListener('xticket');
            this.client._internalEvent.addListener('xticket', this.handleWS);
            //send subcribeWsTicket
            const idMessage = generateID(
                this.client.clientID,
                new Date().getTime()
            );
            const subscribe = {
                id: idMessage,
                type: 'subscribe',
                destination: '',
                payload: ['TICKET_' + id],
                action: 'app'
            };
            this.client.sendWS(subscribe);
        }
    };
    addRedux = (data) => {
        let newData = [data, ...this.props.listComment.data];
        this.props.actionTicket.stop_get_list_comment({
            data: newData
        });
    };
    subcribeWsTicket = () => {
        const { detailTicket } = this.props;
        const idTicket = detailTicket?.data?.ticket?.id;
        if (idTicket) {
            this.checkAndConnectWS(idTicket);
        }
    };
    unsubcribeWS = (id) => {
        const idMessage = generateID(
            this.client.clientID,
            new Date().getTime()
        );
        const subscribe = {
            id: idMessage,
            type: 'unsubscribe',
            destination: '',
            payload: ['TICKET_' + id]
        };

        this.client.sendWS(subscribe);
    };

    componentDidUpdate(prevProps, preState) {
        const { detailTicket } = this.props;
        if (
            prevProps?.detailTicket?.data &&
            detailTicket?.data?.ticket?.id !==
                prevProps.detailTicket?.data?.ticket?.id
        ) {
            this.unsubcribeWS(prevProps.detailTicket?.data?.ticket?.id);
        }
        if (this.state.content !== preState.content) {
            if (
                this.timeOutId > -1 &&
                this.timeOutId !== -1 &&
                this.timeOutId !== undefined
            ) {
                clearTimeout(this.timeOutId);
            }
            this.timeOutId = setTimeout(() => {
                if (this.state.typing) {
                    this.sendTyping(false);
                    this.setState({
                        typing: false
                    });
                }
            }, 1500);
        }
        if (prevProps.route?.params !== this.props.route?.params) {
            if (helper.hasProperty(this.props.route.params, 'suggestComment')) {
                this.setState((prevState) => ({
                    content:
                        prevState.content +
                        ' ' +
                        this.props.route.params.suggestComment
                }));
            }
        }
        if (this.props.listComment.data !== prevProps.listComment.data) {
            this.setState({ showLoadSent: false });
        }
    }

    initToken = () => {
        const { detailTicket } = this.props;
        this.props.actionTicket.getStreamToken(detailTicket?.data?.ticket?.id); ///Lấy token cho ảnh
    };
    initData = async () => {
        try {
            this.setState({
                fetchingData: true
            });
            await this.getDataTicket();
            this.actionRatingGetInfo();
            this.checkSubscribe();
            this.initListFile();
            this.callApiMBBank();
            this.offFetching();
        } catch (error) {
            this.offFetching();
        }
    };
    offFetching = () => {
        this.setState({
            fetchingData: false
        });
    };
    checkSubscribe = () => {
        const { detailTicket, typeListData } = this.props;
        if (
            !helper.IsValidateObject(detailTicket?.data.approveType) &&
            !typeListData.data.includes(
                detailTicket?.data?.ticket?.supportServiceType
            )
        ) {
            this.subcribeWsTicket();
        }
    };
    initDetail = () => {
        try {
            this.getListMember();
            this.initToken();
            this.initComment();
            this.props.actionTicket.getListHashtag();
        } catch (error) {
            this.offFetching();
        }
    };
    getDataTicket = async () => {
        try {
            const paramsXticket = await AsyncStorage.getItem('TICKET_ID');
            this.props.actionTicket.getApproveTypeList();
            if (helper.IsValidateObject(paramsXticket)) {
                this.isNotification = true;
                let stogrageTicket = JSON.parse(paramsXticket);
                await this.props.actionTicket.getDetailTicket(
                    parseInt(stogrageTicket.id)
                );
                this.initDetail();
                this.setState({
                    indexHeader: parseInt(stogrageTicket?.indexHeader)
                });
                if (this.props.detailTicket?.isSuccess) {
                    AsyncStorage.removeItem('TICKET_ID');
                }
            } else {
                await this.props.actionTicket.getDetailTicket(
                    this.props.route.params.id
                );
                this.initDetail();
            }
        } catch (error) {
            this.offFetching();
        }
    };
    updateDetailTicket = async () => {
        const { detailTicket } = this.props;
        this.setState({
            refesshDetail: true
        });
        await this.props.actionTicket.getDetailTicket(
            detailTicket?.data?.ticket?.id
        );
        this.handleGlobalState();
        this.setState({
            refesshDetail: false
        });
    };
    retryData = async () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10 }
        };
        await this.props.actionTicket.getDetailTicket(
            detailTicket?.data?.ticket?.id
        );

        this.props.actionTicket.getListCommentTicket(params);
        this.props.actionTicket.getListTask(params);
        this.props.actionTicket.getListFile(params);
    };

    initListFile = () => {
        const { detailTicket } = this.props;
        const params = {
            ticketId: detailTicket?.data?.ticket?.id,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 10 }
        };

        this.props.actionTicket.getListFile(params);
    };

    initComment = async (lengthList = 10, isMedia = true, checkSeen = true) => {
        try {
            const { detailTicket } = this.props;
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: lengthList
                },
                ticketId: detailTicket?.data?.ticket?.id
            };
            if (!isMedia) {
                this.setState({
                    content: '',
                    mentionedMember: []
                });
            }
            await this.props.actionTicket.getListCommentTicket(body, checkSeen);
        } catch (error) {
            console.log(error, 'error');
        }
    };
    handleNewFeed = (item) => {
        const { listNewFeed } = this.props;
        let newData = [...listNewFeed.data];

        let index = this.props.listNewFeed.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            this.props.actionHome.stop_get_new_feed({
                data: newData
            });
        }
    };
    handleChangeTicket = (item) => {
        const { listTicket } = this.props;
        let newData = [...listTicket.data];

        let index = this.props.listTicket.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            this.props.actionTicket.stop_get_list_ticket({
                data: newData
            });
        }
    };

    updateStatusTicket = async (body) => {
        const response = await this.props.actionTicket.updateStatusTicket(body);
        try {
            if (
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object)
            ) {
                Toast.show({
                    type: 'success',
                    text1: translate('change_status_success')
                });
                this.updateDetailTicket();
                this.handleChangeTicket(response.object);
                this.handleNewFeed(response.object, false);
                this.handleGlobalState();
            } else {
                Toast.show({
                    type: 'error',
                    text1: response ?? translate('change_status_fail')
                });
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('change_status_fail')
            });
        }
    };

    getDataMention = async (text) => {
        const { detailTicket } = this.props;

        const dataMember = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: text || ''
            },
            requestId: '',
            ticketId: detailTicket?.data?.ticket?.id
        };
        const result = await this.props.actionHome.getListMemberGroup(
            dataMember
        );
        this.setState({
            initMentionCount: Math.round(result.data.length)
        });

        const { userName: profileUserName } = this.props.xworkData.profile;

        const filterProfile = result.data.filter((item) => {
            return item.user.username !== profileUserName;
        });
        const toRemove = this.state.mentionedMember;
        for (let i = filterProfile?.length - 1; i >= 0; i--) {
            for (let j = 0; j < toRemove?.length; j++) {
                if (
                    filterProfile[i] &&
                    filterProfile[i].user.username === toRemove[j].user.username
                ) {
                    filterProfile.splice(i, 1);
                }
            }
        }
        this.setState({
            memberInMention: filterProfile
        });
    };
    retryListTicket = () => {
        const { detailTicket } = this.props;

        const data = {
            supportServiceId: detailTicket?.data?.ticket?.supportServiceId
        };
        this.props.actionTicket.getListTicket(data);
    };
    onChangeStatus = (item) => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        let body = {
            id: data?.ticket.id,
            statusId: item.id
        };
        if (item?.isReason) {
            return this.setState({
                itemStatus: item,
                isShowModalResson: true
            });
        }

        this.props.actionTicket.actionRatingGetInfo({
            ticketID: body?.id
        });
        this.updateStatusTicket(body);
    };

    renderHearder = () => {
        return (
            <RenderHeader
                onChangeStatus={this.onChangeStatus}
                navigation={this.props.navigation}
                handleGlobalState={this.handleGlobalState}
            />
        );
    };
    handleTouchHeader = (index) => {
        if (this.state.isExpand) {
            this.onPressShowHideList(50);
        }
        setTimeout(
            () => {
                this.setState({
                    indexHeader: index
                });
            },
            this.state.isExpand ? 50 : 0
        );
    };

    renderTopHeader = () => {
        return (
            <RenderTopHeader
                indexHeader={this.state.indexHeader}
                handleTouchHeader={this.handleTouchHeader}
            />
        );
    };

    sendCommentSocket = (response) => {
        const { detailTicket, xworkData } = this.props;
        const message = {
            ticketId: detailTicket?.data?.ticket?.id,
            page: 'XWORK-APP',
            data: response,
            senderId: xworkData.fullProfile.id,
            type: 'message'
        };
        if (
            this.client &&
            this.client.state &&
            this.client.state === 'connected'
        ) {
            const idMessage = generateID(
                this.client.clientID,
                new Date().getTime()
            );
            const defaultData = {
                id: idMessage,
                type: 'default',
                destination: 'TICKET_' + detailTicket?.data?.ticket?.id,
                payload: JSON.stringify(message)
            };
            this.client.sendWS(defaultData);
        }
    };
    sendComment = async () => {
        //! HÀM COMMENT
        const { detailTicket } = this.props;
        const body = {
            ticketId: detailTicket?.data?.ticket?.id,
            content: this.state.content,
            privateSendUser: -1,
            isComment: false
        };
        this.setState({ showLoadSent: true });
        const response = await this.props.actionTicket.sendComment(
            body,
            this.state.mentionedMember
        );
        if (response) {
            this.sendCommentSocket(response);
            this.initComment(
                this.props.listComment.data?.length + 1,
                false,
                false
            );
            setTimeout(() => {
                this.sendTyping(false);
            }, 1500);

            this.setState({ isSent: true, typing: false }, () => {
                this.setState({ isSent: false });
            });
        }
    };

    onOpenCamera = async () => {
        const { detailTicket, xworkData } = this.props;

        const { common } = xworkData;

        let response = await common.pickSingleWithCamera({
            mediaType: 'photo',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            useFrontCamera: false
        });
        if (response && response.length > 0) {
            try {
                const body = {
                    files: response[0],
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: true
                };

                const sendImage =
                    await this.props.actionTicket.uploadImageComment(body);

                if (sendImage) {
                    this.sendCommentSocket(sendImage.comment);
                    this.initComment(
                        this.props.listComment.data?.length + 1,
                        true,
                        false
                    );
                }
            } catch (e) {
                Toast.show({
                    type: 'error',
                    text1: translate('upload_image_failed')
                });
            }
        }
    };

    onShowPicker = async () => {
        const { detailTicket } = this.props;

        try {
            await ImagePicker.openPicker({
                maxFiles: 1,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageQuality: 1,
                compressVideoPreset: 'HighestQuality',
                durationLimit: 60,
                maximumVideoDuration: 60000
            }).then(async (image) => {
                try {
                    if (image.mime.includes('video')) {
                        let dataFile = {
                            uri: image.sourceURL,
                            path: image.path,
                            width: image.width,
                            height: image.height,
                            mime: image.mime
                        };
                        const body = {
                            files: dataFile,
                            ticketId: detailTicket?.data?.ticket?.id,
                            comment: true
                        };
                        const sendImage =
                            await this.props.actionTicket.uploadImageComment(
                                body
                            );
                        if (sendImage) {
                            this.sendCommentSocket(sendImage.comment);
                            this.initComment(
                                this.props.listComment.data?.length + 1,
                                true,
                                false
                            );
                        } else {
                            Toast.show({
                                type: 'error',
                                text1: translate('upload_video_failed')
                            });
                        }
                    } else {
                        let dataFile;

                        dataFile = {
                            uri: image.sourceURL,
                            path: image.path,
                            width: image.width,
                            height: image.height,
                            mime: image.mime
                        };
                        // }
                        const body = {
                            files: dataFile,
                            ticketId: detailTicket?.data?.ticket?.id,
                            comment: true
                        };
                        const sendImage =
                            await this.props.actionTicket.uploadImageComment(
                                body
                            );

                        this.sendCommentSocket(sendImage.comment);

                        if (sendImage) {
                            this.initComment(
                                this.props.listComment.data?.length + 1,
                                true,
                                false
                            );
                        }
                    }
                } catch (error) {
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_image_failed')
                    });
                }
            });
        } catch (e) {
            console.log(e, 'error');
        }
    };

    pickerGallery = async (item) => {
        let msg =
            item === 2
                ? translate('contribute_no_gallery_right')
                : translate('camera_permission');
        try {
            if (item === 2) {
                await requestPermission('photo');
                this.onShowPicker();
            } else if (item === 1) {
                await requestPermission('camera');
                msg = translate('allow_library');
                await requestPermission('storage');
                this.onOpenCamera();
            }
        } catch (e) {
            return global.props.alert({
                show: true,
                title: translate('notify'),
                message: msg,
                confirmText: translate('close'),
                cancelText: translate('setting'),

                onConfirmPressed: () => {
                    global.props.alert({
                        show: false
                    });
                },
                onCancelPressed: () => {
                    global.props.alert({
                        show: false
                    });
                    openSetting();
                }
            });
        }
    };

    renderSuggestComment = () => {
        return (
            <TouchableOpacity
                style={{
                    backgroundColor: this.state.isExpand
                        ? Colors.GRAYF8
                        : ThemeXwork.primary.$600,
                    borderRadius: 12,
                    height: Mixins.scale(24),
                    width: Mixins.scale(24)
                }}
                onPress={() => {
                    this.onPressShowHideList();
                    Keyboard.dismiss();
                }}>
                <Image
                    source={{ uri: 'ic_plus' }}
                    style={{
                        height: Mixins.scale(24),
                        width: Mixins.scale(24),
                        tintColor: Colors.WHITE
                    }}
                />
            </TouchableOpacity>
        );
    };
    renderItemSuggestComment = (item, index) => {
        return (
            <View
                style={{
                    justifyContent: 'center',
                    flexDirection: 'row'
                }}>
                <TouchableOpacity
                    style={{
                        marginRight: index === 5 ? 16 : 8,
                        backgroundColor: ThemeXwork.neutrals.$950,
                        borderRadius: 16,
                        justifyContent: 'center'
                    }}
                    onPress={() => this.setCommentTicket(item.name)}>
                    <MyText
                        text={item.name}
                        addSize={-1}
                        style={{
                            marginHorizontal: 8,
                            color: ThemeXwork.neutrals.$200
                        }}
                    />
                </TouchableOpacity>
            </View>
        );
    };
    setCommentTicket = (content) => {
        this.setState((prevState) => ({
            content: prevState.content + ' ' + content
        }));
    };

    handleAnimationButton = (rotateValue, durationTime = 100) => {
        Animated.timing(this.animationValue, {
            toValue: rotateValue,
            duration: durationTime,
            useNativeDriver: true
        }).start();
    };
    onPressShowHideList = (durationTime = 100) => {
        if (this.state.isExpand) {
            this.handleAnimationButton(1, durationTime);
        } else {
            this.handleAnimationButton(0.5, durationTime);
        }
        setTimeout(() => {
            this.setState((preState) => ({ isExpand: !preState.isExpand }));
        }, durationTime);
    };

    checkShowTooltipSuggestComment = async () => {
        try {
            let ress = await AsyncStorage.getItem('SHOW_SUGGEST');
            if (ress && ress.length > 0) {
                return false;
            } else {
                AsyncStorage.setItem('SHOW_SUGGEST', 'true');
                return true;
            }
        } catch (e) {
            return false;
        }
    };

    handleRemoveMention = async (item) => {
        const tempArr = this.state.mentionedMember;
        const result = await tempArr.filter(
            (filterItem) => filterItem.user.username !== item.user.username
        );
        this.setState({
            mentionedMember: result
        });
    };
    sendTyping = (typing) => {
        const { detailTicket } = this.props;
        const { fullProfile } = this.props.xworkData;
        let payload = {
            ticketId: detailTicket?.data?.ticket?.id,
            page: 'workspace',
            data: {
                user: fullProfile.userName,
                isTyping: typing,
                avatarURI: fullProfile.image,
                noContent: false,
                ticketid: detailTicket?.data?.ticket?.id
            },
            senderId: fullProfile.id,
            type: 'typing'
        };
        const idMessage = generateID(
            this.client.clientID,
            new Date().getTime()
        );
        let dataTyPing = {
            id: idMessage,
            type: 'default',
            destination: 'TICKET_' + detailTicket?.data?.ticket?.id,
            payload: JSON.stringify(payload)
        };
        this.client.sendWS(dataTyPing);
    };
    onFocusInput = () => {
        const userNameRegEx = new RegExp(/@([\w\d.\-_]+)?/g);
        if (this.state.content.match(userNameRegEx)) {
            this.getDataMention();
            this.setState({
                isOpenMention: true
            });
        } else {
            this.setState({
                isOpenMention: false
            });
        }
        if (this.state.isExpand) {
            this.onPressShowHideList();
        }
    };
    onChangeText = (text) => {
        const userNameRegEx = new RegExp(/@([\w\d.\-_]+)?/g);
        // wordArray.includes('@')
        if (!this.state.typing && text !== this.state.content) {
            this.sendTyping(true);
            this.setState({ typing: true });
        }
        if (text.match(userNameRegEx)) {
            this.getDataMention();
            this.setState({
                isOpenMention: true
            });
        } else {
            this.setState({
                isOpenMention: false
            });
        }

        this.setState({ content: text });
    };

    handleConfirmBotComment = async (value) => {
        const { detailTicket, listComment } = this.props;
        const commentID = listComment?.data[0]?.id;
        const ticketId = detailTicket?.data?.ticket?.id;

        const dataConfirm = {
            ticketID: ticketId,
            commentID: commentID,
            confirm: value
        };

        await this.props.actionTicket.actionConfirmBotComment(
            dataConfirm,
            () => {
                this.props.actionTicket.getDetailTicket(ticketId);
            }
        );
    };

    renderInputComment = () => {
        return (
            <View style={style.inputComment}>
                <RenderMentionSelected
                    mentionedMember={this.state.mentionedMember}
                    listMemberGroup={this.props.listMemberGroup}
                    initMentionCount={this.state.initMentionCount}
                    handleRemoveMention={this.handleRemoveMention}
                />

                <TextInput
                    style={style.inputStyle}
                    value={this.state.content}
                    onFocus={this.onFocusInput}
                    onChangeText={this.onChangeText}
                    onBlur={() => {
                        setTimeout(() => {
                            this.sendTyping(false);
                        }, 1500);
                    }}
                    textAlignVertical="top"
                    keyboardType="default"
                    placeholder={translate('enter_comment')}
                    multiline
                />
                {this.renderIconBottom()}
            </View>
        );
    };

    renderBotComment = () => {
        return (
            <View
                style={{
                    backgroundColor: Colors.GRAY_NEUTRALS,
                    borderRadius: Mixins.scale(12),
                    marginBottom: global.props.insets.bottom + Mixins.scale(8),
                    marginTop: Mixins.scale(10),
                    maxHeight: Mixins.scale(180),
                    zIndex: 100,
                    gap: Mixins.scale(16),
                    paddingHorizontal: Mixins.scale(16),
                    paddingVertical: Mixins.scale(8),
                    alignSelf: 'flex-end'
                }}>
                <View style={{ gap: Mixins.scale(4) }}>
                    <MyText
                        text={'Thông tin này có giúp bạn xử lý được sự cố?'}
                        typeFont={'semiBold'}
                        style={{ color: Colors.BLACK_HEADER_TITLE }}
                    />
                    <MyText
                        text={
                            'Hãy cho chúng tôi biết để được hỗ trợ bạn tốt hơn.'
                        }
                        addSize={-2}
                        style={{ color: Colors.GRAYF9 }}
                    />
                </View>
                <View style={{ flexDirection: 'row', gap: Mixins.scale(12) }}>
                    <TouchableOpacity
                        onPress={() => this.handleConfirmBotComment(true)}
                        style={{
                            paddingHorizontal: Mixins.scale(8),
                            paddingVertical: Mixins.scale(4),
                            backgroundColor: `${Colors.GRAYF6}60`,
                            borderRadius: Mixins.scale(8),
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: Mixins.scale(6)
                        }}>
                        <Image
                            source={require('../../../assets/images/ic_like_bot.webp')}
                            style={{
                                width: Mixins.scale(20),
                                height: Mixins.scale(20)
                            }}
                        />
                        <MyText text={'Có'} typeFont={'medium'} />
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={() => this.handleConfirmBotComment(false)}
                        style={{
                            paddingHorizontal: Mixins.scale(8),
                            paddingVertical: Mixins.scale(4),
                            backgroundColor: `${Colors.GRAYF6}60`,
                            borderRadius: Mixins.scale(8),
                            flexDirection: 'row',
                            alignItems: 'center',
                            gap: Mixins.scale(6)
                        }}>
                        <Image
                            source={require('../../../assets/images/ic_dislike_bot.webp')}
                            style={{
                                width: Mixins.scale(20),
                                height: Mixins.scale(20)
                            }}
                        />
                        <MyText text={'Không'} typeFont={'medium'} />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    renderIconBottom = () => {
        return (
            <View style={style.viewIconBottom}>
                <View style={style.rowIcon}>
                    <Animated.View
                        style={{
                            marginRight: 16,
                            transform: [
                                {
                                    rotate: this.animationValue.interpolate({
                                        inputRange: [0, 0.5, 1],
                                        outputRange: ['0deg', '45deg', '0deg']
                                    })
                                }
                            ]
                        }}>
                        <TooltipShow
                            textShow={translate('quick_reply')}
                            onCheckVisible={() =>
                                this.checkShowTooltipSuggestComment()
                            }>
                            {this.renderSuggestComment()}
                        </TooltipShow>
                    </Animated.View>
                    <TouchableOpacity
                        onPress={() => {
                            this.getDataMention();
                            this.setState((prevState) => ({
                                isOpenMention: !prevState.isOpenMention
                            }));
                        }}
                        style={style.btnIcon}>
                        <Image
                            style={style.ic_input}
                            source={{ uri: 'ic_at_sign' }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        hitSlop={{
                            top: 10,
                            bottom: 10,
                            left: 10,
                            right: 10
                        }}
                        onPress={() => {
                            this.pickerGallery(1);
                        }}
                        style={style.btnIcon}>
                        <Image
                            style={style.ic_input}
                            resizeMode="contain"
                            source={{ uri: 'ic_camera_comment' }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        hitSlop={{
                            top: 10,
                            bottom: 10,
                            left: 10,
                            right: 10
                        }}
                        onPress={() => {
                            this.pickerGallery(2);
                        }}
                        style={style.btnIcon}>
                        <Image
                            style={style.ic_input}
                            resizeMode="contain"
                            source={{ uri: 'ic_image_cmt' }}
                        />
                    </TouchableOpacity>
                </View>
                {this.isShowButtonSend() && (
                    <TouchableDebounce
                        onPress={() => {
                            this.sendComment();
                        }}
                        debounceTime={200}
                        style={[style.btnIcon, { marginRight: 8 }]}>
                        <Image
                            resizeMode="contain"
                            style={{
                                height: Mixins.scale(22),
                                width: Mixins.scale(22)
                            }}
                            source={{ uri: 'ic_send' }}
                        />
                    </TouchableDebounce>
                )}
                {!this.isShowButtonSend() &&
                    this.state.showLoadSent &&
                    this.props.listComment.isFetching && (
                        <View style={{ marginRight: 8 }}>
                            <ActivityIndicator
                                size="small"
                                color={Colors.DARK_BLUE_60}
                            />
                        </View>
                    )}
            </View>
        );
    };

    isShowButtonSend = () => {
        return (
            this.state.content ||
            (this.state.mentionedMember &&
                this.state.mentionedMember.length > 0)
        );
    };
    onTouchMention = (item) => {
        const arrMention = [...this.state.mentionedMember];
        arrMention.push(item);

        const mentionCompleted = this.state.content.replace('@', '');
        this.setState((prevState) => ({
            content: mentionCompleted,
            isOpenMention: false,
            mentionedMember: [...prevState.mentionedMember, item]
        }));
    };

    notShowEdit = () => {
        const { indexHeader } = this.state;
        const { detailTicket, xworkData, typeListData } = this.props;
        const { data } = detailTicket;
        const checkTypelist = typeListData?.data.includes(
            data?.ticket?.supportServiceType
        );
        if (
            indexHeader === 1 ||
            checkTypelist ||
            !data?.ticket?.isEdit ||
            (helper.hasProperty(data?.ticket, 'isGroundTaskEdit') &&
                !data?.ticket?.isGroundTaskEdit)
        ) {
            return true;
        } else {
            return false;
        }
    };
    actionPressBack = async () => {
        const ticketID = await AsyncStorage.getItem('TICKET_ID');
        if (helper.IsValidateObject(ticketID)) {
            const items = 'TICKET_ID';
            await AsyncStorage.removeItem(items);
            this.props.navigation.navigate('Home');
        } else if (this.isNotification) {
            this.props.navigation.navigate('Home');
        } else {
            this.props.navigation.goBack();
        }
    };
    handleButtonRightHeader = () => {
        const { detailTicket } = this.props;
        const { indexHeader } = this.state;

        if (detailTicket.isFetching) {
            return null;
        }
        if (indexHeader === 0) {
            if (
                detailTicket.data?.ticket?.supportServiceType === 'GROUND_TASK'
            ) {
                this.props.navigation.navigate('EditSchedule');
            } else {
                this.props.navigation.navigate('EditTicket');
            }
        }
        if (indexHeader === 2) {
            if (
                detailTicket.data?.ticket?.supportServiceType === 'GROUND_TASK'
            ) {
                this.props.actionGroundTask.filesTask([]);
                this.props.navigation.navigate('CreateGroundTask');
            } else {
                this.setState({
                    isShowModalCreateWork: true
                });
            }
        }
    };
    render() {
        const { indexHeader, isShowModalCreateWork, fetchingData } = this.state;
        const {
            xworkData,
            detailTicket,
            typeListData,
            ratingInfo,
            listComment
        } = this.props;
        const { component } = xworkData;
        if (component === undefined) return null;
        const { WrapperContainerTicket, ModalReason } = component;
        const notEdit = this.notShowEdit();

        const isBot =
            listComment?.data.length > 0 && listComment?.data[0]?.isBot;

        const checkIsBot =
            (detailTicket.data?.ticket?.completeTimeLong === undefined ||
                detailTicket.data?.ticket?.completeTimeLong === 0) &&
            isBot;

        return (
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={style.container}>
                <View style={style.container}>
                    <WrapperContainerTicket
                        navigation={this.props.navigation}
                        nameTitle={
                            indexHeader !== 0 ? '' : translate('detail_ticket')
                        }
                        centerAlign={false}
                        colorBackButton={Colors.DARK_BLUE_50}
                        onPressBack={this.actionPressBack}
                        isSuccess={!detailTicket?.isError}
                        messageLoading={translate('getting_job_ticket')}
                        messageError={
                            detailTicket?.msgError ||
                            translate('something_wrong_server')
                        }
                        isError={detailTicket.isError}
                        isLoading={fetchingData && !this.state.refesshDetail}
                        buttonRightOutsize={
                            notEdit ? null : this.handleButtonRightHeader
                        }
                        actionRetry={this.initData}
                        imgButtonOutSize={
                            indexHeader === 0 ? 'ic_edit' : 'ic_plus'
                        }>
                        <View style={style.ctnView}>
                            {indexHeader === 0 &&
                                (detailTicket.data?.ticket
                                    ?.supportServiceType === 'GROUND_TASK' ? (
                                    <InfoSchedule
                                        props={this.props}
                                        renderHearder={this.renderHearder()}
                                        renderTopHeader={this.renderTopHeader()}
                                        initListFile={this.initListFile}
                                        retryData={this.retryData}
                                        fetchingData={fetchingData}
                                        updateDetailTicket={
                                            this.updateDetailTicket
                                        }
                                        retryListTicket={this.retryListTicket}
                                    />
                                ) : (
                                    <InformationTicket
                                        props={this.props}
                                        showRating={ratingInfo}
                                        actionRatingGetInfo={
                                            this.actionRatingGetInfo
                                        }
                                        renderHearder={this.renderHearder()}
                                        renderTopHeader={this.renderTopHeader()}
                                        initListFile={this.initListFile}
                                        retryData={this.retryData}
                                        fetchingData={this.state.fetchingData}
                                    />
                                ))}
                            {indexHeader === 1 && (
                                <CommentTicket
                                    isOpenMention={this.state.isOpenMention}
                                    props={this.props}
                                    content={this.state.content}
                                    setComment={this.setCommentTicket}
                                    navigation={this.props.navigation}
                                    renderHearder={this.renderHearder()}
                                    renderTopHeader={this.renderTopHeader()}
                                    initComment={this.initComment}
                                    offMention={() => {
                                        if (this.state.isExpand) {
                                            this.onPressShowHideList();
                                        }
                                        this.setState({
                                            isOpenMention: false
                                        });
                                    }}
                                    isSent={this.state.isSent}
                                    client={this.client}
                                    messageId={this.state.messageId}
                                    isTyping={this.state.isTyping}
                                    componentBot={
                                        checkIsBot
                                            ? this.renderBotComment()
                                            : null
                                    }
                                />
                            )}
                            {indexHeader === 2 && (
                                <ListTask
                                    props={this.props}
                                    content={this.state.content}
                                    renderHearder={this.renderHearder()}
                                    renderTopHeader={this.renderTopHeader()}
                                    isShowModalCreateWork={
                                        isShowModalCreateWork
                                    }
                                    dismissModalCreateWork={() =>
                                        this.setState({
                                            isShowModalCreateWork: false
                                        })
                                    }
                                    updateDetailTicket={this.updateDetailTicket}
                                    retryListTicket={this.retryListTicket}
                                />
                            )}
                        </View>
                    </WrapperContainerTicket>
                </View>

                {!fetchingData &&
                    indexHeader === 0 &&
                    !detailTicket?.isError && (
                        <RenderButtonWithoutAssignee
                            detailTicket={detailTicket}
                            typeListData={typeListData}
                            xworkData={xworkData}
                            handleNewFeed={this.handleNewFeed}
                            handleChangeTicket={this.handleChangeTicket}
                            handleGlobalState={this.handleGlobalState}
                            updateDetailTicket={this.updateDetailTicket}
                        />
                    )}
                {this.state.isExpand && (
                    <RenderListSuggestComment
                        navigation={this.props.navigation}
                        suggestComment={this.props.suggestComment}
                        setCommentTicket={this.setCommentTicket}
                    />
                )}
                {this.state.isShowModalResson && (
                    <ModalReason
                        isVisible={this.state.isShowModalResson}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalResson: false
                            });
                        }}
                        onPressSendReason={this.onPressSendReason}
                    />
                )}

                {indexHeader === 1 && !fetchingData && (
                    <View
                        style={{
                            backgroundColor: Colors.WHITE,
                            zIndex: 1000
                        }}>
                        {this.state.isOpenMention && (
                            <RenderMention
                                listMemberGroup={this.props.listMemberGroup}
                                memberInMention={this.state.memberInMention}
                                onTouchMention={this.onTouchMention}
                            />
                        )}
                        {isBot ? null : this.renderInputComment()}
                    </View>
                )}
            </KeyboardAvoidingView>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        detailTicket: state.ticketReducer.detailTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listComment: state.ticketReducer.listComment,
        typeListData: state.ticketReducer.typeListData,
        suggestComment: state.ticketReducer.suggestComment,
        listNewFeed: state.groupTicketReducer.listNewFeed,
        listTicket: state.ticketReducer.listTicket,
        ratingInfo: state.ticketReducer.ratingInfo
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch),
        actionDetailTicket: bindActionCreators(_actionDetailTicket, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(DetailTicket);
const style = StyleSheet.create({
    btnIcon: {
        alignItems: 'center',
        height: Mixins.scale(24),
        justifyContent: 'center',
        marginRight: Mixins.scale(16),
        width: Mixins.scale(24)
    },
    container: {
        backgroundColor: Colors.WHITE,
        flex: 1
    },
    ctnView: {
        flex: 1,
        marginTop: Mixins.scale(12),
        paddingHorizontal: 16
    },
    ic_input: {
        height: Mixins.scale(22),
        tintColor: Colors.GRAYF8,
        width: Mixins.scale(22)
    },
    inputComment: {
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        marginBottom: global.props.insets.bottom + Mixins.scale(8),
        marginHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(10),
        maxHeight: Mixins.scale(180),
        zIndex: 100
    },
    inputStyle: {
        maxHeight: Mixins.scale(60),
        minHeight: Mixins.scale(40),
        paddingHorizontal: Mixins.scale(12),
        paddingTop: Mixins.scale(12),
        width: '100%',
        zIndex: 1000
    },
    rowIcon: {
        alignItems: 'center',
        flexDirection: 'row',
        paddingHorizontal: Mixins.scale(12),
        paddingTop: Mixins.scale(12)
    },
    viewIconBottom: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(18),
        justifyContent: 'space-between',
        marginBottom: Mixins.scale(12),
        marginTop: Mixins.scale(8)
    }
});
