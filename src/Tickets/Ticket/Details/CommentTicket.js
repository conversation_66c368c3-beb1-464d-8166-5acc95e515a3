import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    Alert,
    Animated,
    Dimensions,
    FlatList,
    Image,
    Keyboard,
    Platform,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import { ThemeXwork } from '@mwg-sdk/styles';
import { requestPermission, openSetting } from '@mwg-kits/core';
import { toastConfig } from '../../GroupTicket/MemberGroupTicket';
import { CameraRoll } from '@react-native-camera-roll/camera-roll';
import RNFetchBlob from 'react-native-blob-util';
import { TooltipComment } from '../../GroupTicket/CompoentTooltip';
import AntDesign from 'react-native-vector-icons/AntDesign';
import ModalUser from '../../../modal/ModalUser';
const { translate } = global.props.getTranslateConfig();
import ShowMoreText from '../Components/ShowMoreText';
import { FastImage } from '@mwg-kits/components';
import TypingIndicator from '../Components/Typing';
import { CONST_API } from '../../../constant';
import { textRemoveComment } from '../../../utility';
class CommentTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            },
            iDisplayLength: 0,
            isRefresh: false,
            isLoadMore: false,
            data: [],
            fadeAnim: new Animated.Value(0),
            listViewImage: [],
            isShowModalUser: false,
            indexComment: -1
        };
        this.firstTime = true;
    }
    componentDidMount() {
        this.props.actionTicket.getCommentSuggest();
    }
    fadeIn = () => {
        Animated.timing(this.state.fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true
        }).start();
    };

    fadeOut = () => {
        // Will change fadeAnim value to 0 in 3 seconds
        Animated.timing(this.state.fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true
        }).start();
    };

    checkClickVideo = (item) => {
        return item.contentType.includes('video');
    };

    showModalPic = (item) => {
        const { listComment } = this.props;
        let dataPicture = [];
        listComment?.data?.filter((element) => {
            if (element?.contentType === 'image') {
                dataPicture.push({
                    ...element,
                    ...JSON.parse(element?.content),
                    filemime: element.contentType
                });
            }
            if (element?.contentType === 'video') {
                dataPicture.push({
                    ...element,
                    ...JSON.parse(element?.content),
                    filemime: element.contentType
                });
            }
        });
        const exits = dataPicture.findIndex(
            (ele) => ele.id === JSON.parse(item?.content).id
        );

        this.setState(
            {
                listViewImage: dataPicture
            },
            () => {
                this.setState({
                    viewMedia: {
                        index: exits,
                        visible: true,
                        uris: dataPicture
                    }
                });
            }
        );
    };

    componentDidUpdate(prevProps) {
        if (
            this.props.listComment.data !== prevProps.listComment.data &&
            this.props.listComment.data &&
            this.props.listComment.data?.length > 0 &&
            this.state.iDisplayLength === 0 &&
            this.firstTime
        ) {
            this.firstTime = false;
        }
    }

    confirmRemoveComments = (item) => {
        if (item.status === 'ACTIVE') {
            if (
                this.props.xworkData.profile?.userName === item?.creatorUsername
            ) {
                global.props.alert({
                    show: true,
                    title: translate('delete_cmt'),
                    message: translate('ask_delete_comment'),
                    confirmText: translate('delete'),
                    titleColor: {
                        color: Colors.DARK_RED_30
                    },
                    confirmButtonTextStyle: {
                        color: Colors.DARK_RED_30,
                        fontWeight: '500'
                    },
                    cancelText: translate('cancel'),
                    cancelButtonTextStyle: {
                        color: Colors.GRAYF7
                    },
                    onConfirmPressed: () => {
                        global.props.alert({ show: false });
                        this.removeComments(item);
                    },
                    onCancelPressed: () => {
                        global.props.alert({ show: false });
                    }
                });
            } else {
                return;
            }
        }
    };
    _checkPerOpenGallery = async () => {
        try {
            await requestPermission('photo');
            await requestPermission('storage');
        } catch (error) {
            return Alert.alert(
                translate('notify'),
                translate('contribute_no_gallery_right'),
                [
                    {
                        text: translate('setting'),
                        onPress: () => openSetting()
                    },
                    {
                        text: translate('close'),
                        onPress: () => console.log('Cancel Pressed')
                    }
                ]
            );
        }
    };
    saveToCameraRoll = (url, contentType) => {
        let options =
            contentType === 'image' ? { type: 'photo' } : { type: 'video' };
        return new Promise((resolve) => {
            CameraRoll.save(url, options)
                .then((ress) => {
                    if (ress) {
                        resolve(true);
                    }
                    return;
                })
                .catch(() => {
                    resolve(false);
                    return;
                });
        });
    };
    dowloadImage = (index) => {
        return new Promise(async (resolve) => {
            try {
                this._checkPerOpenGallery();
                const images = this._getUrisImage();
                const { url, contentType } = images[index];
                if (Platform.OS === 'android') {
                    RNFetchBlob.config({
                        fileCache: true,
                        appendExt: contentType === 'image' ? 'png' : 'mp4'
                    })
                        .fetch('GET', url)
                        .then(async (res) => {
                            if (res) {
                                let result = await this.saveToCameraRoll(
                                    res.data,
                                    contentType
                                );
                                resolve(result);
                            }
                        })
                        .catch(() => {
                            resolve(false);
                            return;
                        });
                } else {
                    let result = await this.saveToCameraRoll(url, contentType);
                    resolve(result);
                }
            } catch (error) {
                console.log(error);
            }
        });
    };

    removeComments = async (item) => {
        const { initComment } = this.props;
        const { fullProfile } = this.props.xworkData;
        const body = {
            arrayCommentIds: [item.id],
            ticketId: this.props.detailTicket?.data?.ticket?.id
        };

        global.props.showLoader();

        const result = await this.props.actionTicket.removeComments(body);

        if (result && !result.error) {
            if (
                this.props.client &&
                this.props.client.state &&
                this.props.client.state === 'connected'
            ) {
                const deleteMessage = {
                    ticketId: this.props.detailTicket?.data?.ticket?.id,
                    page: 'XWORK-APP',
                    data: {
                        commentId: item.id,
                        creatorId: item.creatorId,
                        updateTime: new Date().getTime()
                    },
                    senderId: fullProfile.id,
                    type: 'delete-message'
                };
                const defaultData = {
                    id: this.props.messageId,
                    type: 'default',
                    destination:
                        'TICKET_' + this.props.detailTicket?.data?.ticket?.id,
                    payload: JSON.stringify(deleteMessage)
                };
                console.log('removeCommentsremoveComments', deleteMessage);
                this.props.client.sendWS(defaultData);
            }
            await initComment(
                this.props.listComment.data?.length,
                false,
                false
            );
        } else {
            global.props.alert({
                show: true,
                title: translate('error_delete_comment'),
                message: translate('error_comment_try_again'),
                confirmText: translate('confrim'),
                confirmButtonTextStyle: {
                    color: Colors.DARK_RED_30
                },
                cancelText: translate('cancel'),
                cancelButtonTextStyle: {
                    color: Colors.GRAYF7
                },
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                    this.removeComments(item);
                },
                onCancelPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        }
        global.props.hideLoader();
    };

    getUserMentioned = (item) => {
        let str = item?.content;
        const arr = [];
        let fullStr = '';
        while (str?.indexOf('{') !== -1 && str?.includes('@mention:')) {
            const left = str?.indexOf('{');
            const right = str?.indexOf('}') + 1;
            const data = str?.slice(left, right);
            str = str?.slice(0, left) + str?.slice(right, str?.length);
            arr.push(JSON.parse(data));
        }

        if (arr && arr.length > 0) {
            arr.forEach((element) => {
                if (helper.IsValidateObject(element.userName)) {
                    fullStr =
                        fullStr +
                        `${element?.userName}-${element?.userLastName} ${element?.userFirstName} `;
                }
            });
        }
        str = str?.replace(/@mention:/g, ' ');
        str = str.trim();
        fullStr = fullStr + str;
        return { content: fullStr, arrUser: arr, str: str };
    };

    _renderItem = (item, indexItem) => {
        const { streamToken, xworkData } = this.props;
        const { common } = xworkData;
        const newDate = common.helper.getDisplayTimeWithFormat(
            item?.createTime,
            'DD-MM-YYYY hh:ii:ss'
        );

        const dataContent = this.getUserMentioned(item);
        const isImage = item?.contentType === 'image';
        const image = isImage && JSON.parse(item?.content);
        const isVideo = item?.contentType === 'video';
        const isText = item?.contentType === 'text';
        const itemActive = item?.status === 'ACTIVE';
        const commentImage =
            isImage &&
            `${CONST_API.TICKET_URL}file/${streamToken?.data?.token}/${image?.id}?original=true&download=false`;
        return (
            <TouchableOpacity
                key={item.id}
                onPress={() => {
                    this.props.offMention();
                    Keyboard.dismiss();
                }}
                style={style.commentContainer}
                activeOpacity={1}
                onLongPress={
                    isText ? () => {} : () => this.confirmRemoveComments(item)
                }>
                <Image
                    style={{
                        height: Mixins.scale(44),
                        width: Mixins.scale(44),
                        borderRadius: Mixins.scale(22),
                        marginRight: Mixins.scale(16)
                    }}
                    source={{
                        uri: `${CONST_API.baseAvatarURI}${item?.creatorImage}`
                    }}
                />
                <View style={{ flex: 1 }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between'
                        }}>
                        <MyText
                            typeFont="medium"
                            text={`${item?.creatorLastName} ${item?.creatorFirstName} - ${item?.creatorUsername}`}
                            style={{
                                color: ThemeXwork.neutrals.$300,
                                fontSize: 13
                            }}
                        />
                        <MyText
                            text={newDate}
                            style={{
                                color: ThemeXwork.neutrals.$600,
                                fontSize: 12
                            }}
                        />
                    </View>
                    {itemActive ? (
                        <View style={{ marginTop: Mixins.scale(4) }}>
                            {isText && (
                                <View style={{ flexDirection: 'row' }}>
                                    <TooltipComment
                                        dataCopy={dataContent.content}
                                        deleteComment={() =>
                                            this.confirmRemoveComments(item)
                                        }
                                        allowDelete={
                                            xworkData.profile?.userName ===
                                            item?.creatorUsername
                                        }
                                        children={() => {
                                            return this.renderUserMention(
                                                dataContent
                                            );
                                        }}
                                    />
                                </View>
                            )}

                            {isImage && (
                                <TouchableOpacity
                                    onPress={() => {
                                        this.showModalPic(item);
                                    }}
                                    onLongPress={
                                        isText
                                            ? () => {}
                                            : () =>
                                                  this.confirmRemoveComments(
                                                      item
                                                  )
                                    }
                                    style={{
                                        alignItems: 'center',
                                        borderColor: Colors.GRAY_BODERRADIUS,
                                        borderRadius: 20,
                                        borderWidth: 0.5,
                                        height: Mixins.scale(
                                            image?.thumbHeight / 2
                                        ),
                                        width: Mixins.scale(
                                            image?.thumbWidth / 2
                                        ),
                                        marginTop: Mixins.scale(10)
                                    }}>
                                    <FastImage
                                        source={{
                                            uri: commentImage
                                        }}
                                        style={{
                                            borderRadius: 20,
                                            width: Mixins.scale(
                                                image?.thumbWidth / 2
                                            ),
                                            height: Mixins.scale(
                                                image?.thumbHeight / 2
                                            )
                                        }}
                                        resizeMode="stretch"
                                    />
                                </TouchableOpacity>
                            )}
                            {isVideo && (
                                <TouchableOpacity
                                    onPress={() => {
                                        this.checkClickVideo(item);
                                        this.showModalPic(item);
                                    }}
                                    onLongPress={
                                        isText
                                            ? () => {}
                                            : () =>
                                                  this.confirmRemoveComments(
                                                      item
                                                  )
                                    }
                                    style={{
                                        borderColor: Colors.GRAY_BODERRADIUS,
                                        borderRadius: 20,
                                        borderWidth: 0.5,
                                        marginTop: Mixins.scale(10),
                                        height: 150,
                                        width: 150,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>
                                    <View onPress={() => {}}>
                                        <AntDesign
                                            name="caretright"
                                            size={24}
                                            color={Colors.DARK_BLUE_60}
                                        />
                                    </View>
                                </TouchableOpacity>
                            )}
                        </View>
                    ) : (
                        <View style={{ flex: 1 }}>
                            <MyText
                                text={textRemoveComment(item)}
                                addSize={1}
                            />
                        </View>
                    )}
                    {item?.seenUsers?.length > 0 &&
                        this.renderUserSeen(item, indexItem)}
                </View>
            </TouchableOpacity>
        );
    };
    renderUserMention = (dataContent) => {
        return (
            <View>
                {dataContent.arrUser &&
                    dataContent.arrUser.length > 0 &&
                    dataContent.arrUser.map((element, index) => {
                        if (helper.IsValidateObject(element.userName))
                            return (
                                <MyText
                                    key={`${index}_${element.userName}`}
                                    typeFont="semiBold"
                                    text={`${element?.userName}-${element?.userLastName} ${element?.userFirstName}`}
                                    style={{
                                        color: ThemeXwork.neutrals.$100,
                                        fontSize: 13
                                    }}
                                />
                            );
                        return null;
                    })}
                <ShowMoreText
                    content={dataContent.str}
                    maxLine={5}
                    lineHeight={18}
                />
            </View>
        );
    };
    renderUserSeen = (item, indexItem) => {
        return (
            <TouchableOpacity
                activeOpacity={0.85}
                hitSlop={{
                    left: 10,
                    right: 10,
                    bottom: 5,
                    top: 5
                }}
                onPress={() => {
                    Keyboard.dismiss();
                    this.props.offMention();
                    this.setState(
                        {
                            indexComment: indexItem
                        },
                        () => {
                            this.setState({
                                isShowModalUser: true
                            });
                        }
                    );
                }}
                style={{
                    maxWidth: 147
                }}>
                <View style={style.rowWatchUser}>
                    <View style={style.rowUser}>
                        {item?.seenUsers?.map((ele, eleIndex) => {
                            if (eleIndex < 3) {
                                return (
                                    <Image
                                        key={eleIndex}
                                        style={{
                                            zIndex: eleIndex,
                                            position: 'absolute',
                                            left:
                                                eleIndex === 0
                                                    ? 0
                                                    : Mixins.scale(
                                                          16 * eleIndex
                                                      ),
                                            height: Mixins.scale(20),
                                            width: Mixins.scale(20),
                                            borderRadius: Mixins.scale(10)
                                        }}
                                        source={{
                                            uri: `${CONST_API.baseAvatarURI}${ele?.userImage}`
                                        }}
                                    />
                                );
                            }
                        })}
                        {item?.seenUsers?.length > 3 && (
                            <View style={style.userSeen}>
                                <MyText
                                    text={`+${
                                        parseInt(item?.seenUsers?.length) - 3
                                    }`}
                                    addSize={-2}
                                    style={{
                                        color: Colors.WHITE
                                    }}
                                />
                            </View>
                        )}
                    </View>
                    <MyText
                        text={translate('seen')}
                        style={{
                            color: ThemeXwork.neutrals.$600,
                            textDecorationLine: 'underline',
                            fontSize: 11,
                            left:
                                item?.seenUsers.length > 3
                                    ? Mixins.scale(18 * 5)
                                    : Mixins.scale(
                                          18 * (item?.seenUsers.length + 1)
                                      )
                        }}
                    />
                </View>
            </TouchableOpacity>
        );
    };

    renderEmptyComment = () => {
        return (
            <TouchableWithoutFeedback
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
                onPress={() => {
                    Keyboard.dismiss();
                    this.props.offMention();
                }}>
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: -5
                    }}>
                    <Animated.View
                        style={{
                            justifyContent: 'center',
                            alignItems: 'center',
                            opacity: this.state.fadeAnim
                        }}>
                        {/* isOpenMention */}
                        <Image
                            style={{
                                height: Mixins.scale(160),
                                width: Mixins.scale(160)
                            }}
                            resizeMode="contain"
                            source={{ uri: 'ic_empty_comment' }}
                        />
                        <MyText
                            addSize={1}
                            text={translate('write_first_comment')}
                        />
                    </Animated.View>
                </View>
            </TouchableWithoutFeedback>
        );
    };

    renderComment = () => {
        if (!this.props.isOpenMention) {
            this.fadeIn();
        } else {
            this.fadeOut();
        }
        const { listComment, componentBot } = this.props;
        if (
            listComment?.isFetching &&
            this.firstTime &&
            !this.state.isRefresh
        ) {
            return (
                <TouchableWithoutFeedback onPress={this.props.offMention}>
                    <View
                        style={{
                            flex: 1,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                        <MyText text={translate('loading_comment')} />
                    </View>
                </TouchableWithoutFeedback>
            );
        }

        return (
            <TouchableOpacity
                activeOpacity={1}
                style={{
                    flex: 1,
                    justifyContent: 'center'
                }}
                accessible={false}
                onPress={() => {
                    this.props.offMention();
                }}>
                <FlatList
                    ref={(ref) => (this.forwardRef = ref)}
                    keyboardShouldPersistTaps="handled"
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={[
                        {
                            paddingBottom: 30,
                            justifyContent: 'center'
                        },
                        this.props.listComment.data &&
                            this.props.listComment.data.length === 0 && {
                                flex: 1
                            }
                    ]}
                    style={{
                        flex: 1
                    }}
                    data={this.props.listComment?.data}
                    renderItem={({ item, index }) => {
                        return this._renderItem(item, index);
                    }}
                    extraData={this.props.isSent}
                    initialNumToRender={10}
                    windowSize={11}
                    onEndReachedThreshold={0.1}
                    bounces
                    scrollEventThrottle={10}
                    onEndReached={this.loadMoreData}
                    ListFooterComponent={() => {
                        if (this.state.isLoadMore) {
                            return (
                                <View
                                    style={{
                                        alignSelf: 'center'
                                    }}>
                                    <MyText
                                        text={translate('loading_comment')}
                                        addSize={-3}
                                        style={{ padding: 5 }}
                                    />
                                </View>
                            );
                        } else {
                            return componentBot;
                        }
                    }}
                    ListEmptyComponent={this.renderEmptyComment}
                    refreshControl={
                        <RefreshControl
                            tintColor={'#9FC6FF'}
                            colors={['#9FC6FF']}
                            onRefresh={this.refreshData}
                            refreshing={this.state.isRefresh}
                        />
                    }
                />
            </TouchableOpacity>
        );
    };

    loadMoreData = () => {
        const { detailTicket, listComment } = this.props;
        const body = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength:
                    this.state.iDisplayLength === 0
                        ? listComment?.data?.length + 10
                        : this.state.iDisplayLength + 10
            },
            ticketId: detailTicket?.data?.ticket?.id
        };
        if (
            listComment?.data?.length < 10 ||
            body.pageRequest.iDisplayLength - listComment?.data?.length > 10
        ) {
            return;
        }
        this.setState(
            {
                isLoadMore: true,
                iDisplayLength: body.pageRequest.iDisplayLength
            },
            async () => {
                await this.props.actionTicket.getListCommentTicket(body);
                this.setState({ isLoadMore: false });
            }
        );
    };
    refreshData = () => {
        const { detailTicket } = this.props;
        const { fullProfile } = this.props.xworkData;
        this.setState(
            {
                isRefresh: true
            },

            () =>
                setTimeout(async () => {
                    const body = {
                        pageRequest: {
                            iDisplayStart: 0,
                            iDisplayLength: 10
                        },
                        ticketId: detailTicket?.data?.ticket?.id
                    };
                    await this.props.actionTicket.getListCommentTicket(
                        body,
                        true,
                        fullProfile?.username
                    );
                    this.setState({
                        isRefresh: false
                    });
                }, 1000)
        );
    };

    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };

    _getUrisImage = () => {
        const deviceWidth = Dimensions.get('window').width;
        const deviceHeight = Dimensions.get('window').height;
        const uris = [];
        this.state.viewMedia.uris.forEach((item) => {
            const { streamToken } = this.props;
            const isImage = item?.contentType === 'image';
            const image = isImage && JSON.parse(item?.content);
            const commentImage = `${CONST_API.TICKET_URL}file/${streamToken?.data?.token}/${image?.id}?original=true&download=false`;
            uris.push({
                ...item,
                url: commentImage,
                width: deviceWidth,
                height: deviceHeight
            });
        });
        return uris;
    };
    checkQuickTicket = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        let checkQuickTicket = false;
        if (
            (helper.hasProperty(data?.ticket, 'asSocial') &&
                data.ticket.asSocial) ||
            (helper.hasProperty(data, 'ticketType') &&
                data?.ticketType === 'social')
        ) {
            checkQuickTicket = true;
            return checkQuickTicket;
        }
    };
    render() {
        const {
            renderHearder,
            renderTopHeader,
            xworkData,
            streamToken,
            isTyping
        } = this.props;
        const { common } = xworkData;
        const { viewMedia } = this.state;

        return (
            <>
                <View style={{ flex: 1, justifyContent: 'center' }}>
                    <TouchableWithoutFeedback onPress={this.props.offMention}>
                        <View>
                            {renderHearder}
                            {renderTopHeader}
                        </View>
                    </TouchableWithoutFeedback>
                    {isTyping && <TypingIndicator />}
                    {this.renderComment()}
                    {viewMedia.visible && (
                        <common.ViewFile
                            visible={viewMedia.visible}
                            imageUrls={this.state.listViewImage}
                            index={viewMedia.index}
                            onPress={this.onRequestClose}
                            checkQuickTicket={this.checkQuickTicket()}
                            onSwipeDown={() => this.onRequestClose()}
                            onRequestClose={this.onRequestClose}
                            streamToken={streamToken?.data?.token}
                            toastConfig={toastConfig}
                            isClickVideo={this.checkClickVideo(
                                this.state.listViewImage[viewMedia.index]
                            )}
                        />
                    )}
                    {this.state.isShowModalUser && (
                        <ModalUser
                            title={translate('seen')}
                            isVisible={this.state.isShowModalUser}
                            onPressDimiss={() => {
                                this.setState({
                                    isShowModalUser: false,
                                    indexComment: -1
                                });
                            }}
                            listUser={
                                this.props.listComment.data[
                                    this.state.indexComment
                                ]?.seenUsers
                            }
                        />
                    )}
                </View>
            </>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.ticketReducer.listGroupTicket,
        detailTicket: state.ticketReducer.detailTicket,
        listComment: state.ticketReducer.listComment,
        streamToken: state.ticketReducer.streamToken,
        suggestComment: state.ticketReducer.suggestComment
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(CommentTicket);

const style = StyleSheet.create({
    commentContainer: {
        flexDirection: 'row',
        paddingTop: Mixins.scale(32),
        width: '100%'
    },
    rowUser: {
        alignItems: 'center',
        flexDirection: 'row',
        maxWidth: 107
    },
    rowWatchUser: {
        alignItems: 'center',
        flexDirection: 'row',
        height: Mixins.scale(22),
        marginTop: Mixins.scale(12)
    },
    userSeen: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(10),
        height: Mixins.scale(20),
        justifyContent: 'center',
        left: Mixins.scale(16 * 3),
        position: 'absolute',
        width: Mixins.scale(20),
        zIndex: 100
    }
});
