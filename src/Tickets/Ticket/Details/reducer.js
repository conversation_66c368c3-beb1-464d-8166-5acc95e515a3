import * as _action from './action';
import * as _state from './state';
const detailTicketReducer = function (
    state = _state.detailTicketState,
    action
) {
    switch (action.type) {
        case _action.detailTicketAction.START_GET_TROUBLE_STATLISTICAL:
            return {
                ...state,
                listTrouble: {
                    ...state.listTrouble,
                    isFetching: true
                }
            };
        case _action.detailTicketAction.STOP_GET_TROUBLE_STATLISTICAL:
            return {
                ...state,
                listTrouble: {
                    ...state.listTrouble,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.detailTicketAction.START_GET_CANCEL_REASON:
            return {
                ...state,
                listCancelReason: {
                    ...state.listCancelReason,
                    isFetching: true
                }
            };
        case _action.detailTicketAction.STOP_GET_CANCEL_REASON:
            return {
                ...state,
                listCancelReason: {
                    ...state.listCancelReason,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.detailTicketAction.START_GET_LIST_BANK:
            return {
                ...state,
                listBank: {
                    ...state.listBank,
                    isFetching: true
                }
            };
        case _action.detailTicketAction.STOP_GET_LIST_BANK:
            return {
                ...state,
                listBank: {
                    ...state.listBank,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };

        default:
            return state;
    }
};

export { detailTicketReducer };
