import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiBase, METHOD } from '@mwg-kits/core';
import { helper } from '@mwg-kits/common';
import { CONST_API } from './../../../constant';
const START_GET_TROUBLE_STATLISTICAL = 'START_GET_TROUBLE_STATLISTICAL';
const STOP_GET_TROUBLE_STATLISTICAL = 'STOP_GET_TROUBLE_STATLISTICAL';
const START_GET_CANCEL_REASON = 'START_GET_CANCEL_REASON';
const STOP_GET_CANCEL_REASON = 'STOP_GET_CANCEL_REASON';
const START_GET_LIST_BANK = 'START_GET_LIST_BANK';
const STOP_GET_LIST_BANK = 'STOP_GET_LIST_BANK';

export const detailTicketAction = {
    START_GET_TROUBLE_STATLISTICAL,
    STOP_GET_TROUBLE_STATLISTICAL,
    START_GET_CANCEL_REASON,
    STOP_GET_CANCEL_REASON,
    START_GET_LIST_BANK,
    STOP_GET_LIST_BANK
};
const start_get_trouble = () => {
    return {
        type: START_GET_TROUBLE_STATLISTICAL,
        isFetching: true
    };
};
export const stop_get_trouble = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_TROUBLE_STATLISTICAL,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getToken = (key) => AsyncStorage.getItem(key);
export const getListTrouble = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_trouble());

            let body = data;
            let token = await getToken('TOKEN_ACCESS');
            console.log(token, '***************');

            const response = await apiBase(
                CONST_API.API_TROUBLE_STATLISTICAL,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            console.log(response, '***************');

            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.IsValidateObject(response?.object) &&
                !response?.error
            ) {
                dispatch(
                    stop_get_trouble({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_trouble({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            console.log(error, '12313131312312');
            dispatch(
                stop_get_trouble({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_get_cancel_reason = () => {
    return {
        type: START_GET_CANCEL_REASON,
        isFetching: true
    };
};
export const stop_get_cancel_reason = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_CANCEL_REASON,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getCancelReason = () => {
    return async (dispatch) => {
        try {
            dispatch(start_get_cancel_reason());
            const response = await apiBase(
                CONST_API.API_GET_CANCEL_REASON,
                METHOD.POST,
                {},
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.IsValidateObject(response?.object) &&
                !response?.error
            ) {
                dispatch(
                    stop_get_cancel_reason({
                        isSuccess: true,
                        data: response?.object?.lstCancelReason
                    })
                );
            } else {
                dispatch(
                    stop_get_cancel_reason({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            console.log(error, '12313131231313123132');
            dispatch(
                stop_get_cancel_reason({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError:
                        error?.errorReason ||
                        'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_get_list_bank = () => {
    return {
        type: START_GET_LIST_BANK,
        isFetching: true
    };
};
export const stop_get_list_bank = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_BANK,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getListBank = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_bank());
            const response = await apiBase(
                CONST_API.API_GET_BANK,
                METHOD.POST,
                data,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.IsValidateObject(response?.object) &&
                !response?.error
            ) {
                dispatch(
                    stop_get_list_bank({
                        isSuccess: true,
                        data: response?.object?.lstBank
                    })
                );
            } else {
                dispatch(
                    stop_get_list_bank({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            console.log(error, '12313131231313123132');
            dispatch(
                stop_get_list_bank({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError:
                        error?.errorReason ||
                        'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
export const actionCancelRequestApprove = (body) => {
    return async () => {
        let data = body;
        try {
            const response = await apiBase(
                CONST_API.API_CANCEL_REQUEST,
                METHOD.POST,
                data,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            return response;
        } catch (e) {
            return e;
        }
    };
};
