import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    FlatList,
    Image,
    StyleSheet,
    View,
    RefreshControl,
    TouchableOpacity
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import Toast from 'react-native-toast-message';
import ProgressBar from 'react-native-progress/Bar';
import { COLOR_LOADING } from '../..';
import { TooltipFinishDay } from '../../GroupTicket/CompoentTooltip';
import { stylesTicket } from '../stylesTicket';
const { translate } = global.props.getTranslateConfig();
import * as _actionGroundTask from './GroundTask/action';
import { LoadingListTask } from '../Components/loading';
import { CONST_API } from '../../../constant';

class ListTask extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            isRefreshing: false,
            isShowModalDetailTask: false,
            itemSelectedTask: null
        };
    }
    componentDidMount() {
        this.initListTask(true);
    }
    initListTask = (isFetching = false) => {
        try {
            const { detailTicket, xworkData } = this.props;
            const { currentLang } = xworkData;
            const params = {
                ticketId: detailTicket?.data?.ticket?.id,
                pageRequest: { iDisplayStart: 0, iDisplayLength: 20 }
            };
            this.props.actionTicket.getListTask(params, isFetching);
            let body = {
                supportServiceId: detailTicket?.data?.ticket?.supportServiceId,
                lang: currentLang || 'vi'
            };
            this.props.actionTicket.getListTemplateTask(body);
        } catch (error) {
            console.log(error);
        }
    };

    renderTaskList = () => {
        const { listTask } = this.props;

        if (listTask?.isFetching) {
            return <LoadingListTask />;
        }
        if (listTask?.data?.length === 0 && listTask?.isSuccess) {
            return (
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginBottom: Mixins.scale(20)
                    }}>
                    <Image
                        style={{
                            height: Mixins.scale(196),
                            width: Mixins.scale(196)
                        }}
                        source={{ uri: 'ic_empty_task' }}
                    />
                    <MyText text={translate('no_work_todo')} />
                </View>
            );
        }

        return (
            <View style={{ marginTop: Mixins.scale(24), flex: 1 }}>
                {listTask?.isSuccess && (
                    <View
                        style={{
                            alignItems: 'center',
                            flexDirection: 'row'
                        }}>
                        <MyText
                            text={`${parseFloat(
                                listTask?.percentDone * 100
                            ).toFixed(0)}%`}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(4)
                            }}
                            typeFont="medium"
                        />
                        <ProgressBar
                            width={null}
                            progress={listTask?.percentDone || 0}
                            color={Colors.LIGHT_GREEN_50}
                            style={{ marginLeft: 12, flex: 1 }}
                        />
                    </View>
                )}
                <FlatList
                    showsVerticalScrollIndicator={false}
                    style={{ flex: 1 }}
                    data={listTask.data}
                    renderItem={this.renderItem}
                    extraData={this.state || this.props}
                    refreshControl={
                        <RefreshControl
                            tintColor={COLOR_LOADING}
                            colors={[COLOR_LOADING]}
                            onRefresh={this.props.initListTask}
                            refreshing={this.state.isRefreshing}
                        />
                    }
                    scrollEventThrottle={10}
                    initialNumToRender={10}
                    windowSize={11}
                    onEndReachedThreshold={0.1}
                />
            </View>
        );
    };
    formatDate = (date) => {
        return date?.slice(0, date.lastIndexOf(' '));
    };
    handleTask = async (item) => {
        try {
            const { detailTicket } = this.props;
            const body = {
                ticketId: detailTicket?.data?.ticket?.id,
                taskId: item.id,
                ticketTaskDone: !item.done
            };
            const respone = await this.props.actionTicket.actionCheckTask(body);
            if (respone) {
                if (respone?.errorReason) {
                    Toast.show({
                        type: 'error',
                        text1: respone?.errorReason || translate('update_fail'),
                        position: 'bottom'
                    });
                } else {
                    this.initListTask();
                    setTimeout(() => {
                        this.props.updateDetailTicket();
                        this.props.retryListTicket();
                    }, 5000);
                    Toast.show({
                        type: 'success',
                        text1: translate('update_success'),
                        position: 'bottom'
                    });
                }
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('update_fail'),
                position: 'bottom'
            });
        }
    };
    handlCheckReiver = (item) => {
        const { xworkData, detailTicket } = this.props;
        const { isEdit, statusName, supportServiceType } =
            detailTicket.data.ticket;
        const { fullProfile } = xworkData;
        let checkReceiverUsername =
            fullProfile.username === item.receiverUsername;
        let isGroundTask = supportServiceType === 'GROUND_TASK';
        if (!isEdit && isGroundTask) {
            Toast.show({
                type: 'error',
                text1: translate(
                    statusName === 'Công việc mới'
                        ? 'outtime_update'
                        : 'ticket_completed'
                ),
                position: 'bottom'
            });
            return;
        }
        if (!checkReceiverUsername && isGroundTask) {
            Toast.show({
                type: 'warning',
                text1: translate('only_creator'),
                position: 'bottom'
            });
            return;
        }
        if (!checkReceiverUsername && !isGroundTask) {
            Toast.show({
                type: 'warning',
                text1: translate('only_reciever'),
                position: 'bottom'
            });
            return;
        }

        global.props.alert({
            show: true,
            title: translate('notify'),
            message: item.done
                ? translate('do_this_again')
                : translate('get_work_done'),
            cancelText: translate('confrim'),
            confirmText: translate('cancel'),
            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
            },
            confirmButtonStyle: { borderRightWidth: 0 },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
                this.handleTask(item);
            }
        });
    };

    renderItem = ({ item }) => {
        const { detailTicket, props } = this.props;
        return (
            <View key={item.id}>
                <View style={style.btnTitleTask}>
                    <TouchableOpacity
                        activeOpacity={1}
                        hitSlop={{ top: 10, right: 10, left: 10, bottom: 10 }}
                        onPress={() => {
                            this.handlCheckReiver(item);
                        }}>
                        <Image
                            style={style.imgCheck}
                            resizeMode="contain"
                            source={{
                                uri: item.done ? 'ic_check_user' : 'ic_check'
                            }}
                        />
                    </TouchableOpacity>
                    <TouchableOpacity
                        hitSlop={{ top: 10, right: 10, left: 10, bottom: 10 }}
                        activeOpacity={1}
                        onPress={() => {
                            if (
                                detailTicket.data?.ticket
                                    ?.supportServiceType === 'GROUND_TASK'
                            ) {
                                this.props.actionGroundTask.selectedTask(item);
                                props.navigation.navigate('DetailGroundTask', {
                                    item
                                });
                            } else {
                                this.setState({
                                    isShowModalDetailTask: true,
                                    itemSelectedTask: item
                                });
                            }
                        }}
                        style={{
                            flex: 1,
                            marginLeft: Mixins.scale(12)
                        }}>
                        <View style={style.viewText}>
                            <MyText
                                numberOfLines={1}
                                addSize={1}
                                text={
                                    detailTicket.data?.ticket
                                        ?.supportServiceType === 'GROUND_TASK'
                                        ? item?.extraData?.workingTitle
                                        : item?.subject
                                }
                                typeFont="semiBold"
                                style={style.txtDefault}
                            />
                        </View>
                        <View style={style.viewText}>
                            <MyText
                                numberOfLines={2}
                                addSize={-1}
                                text={
                                    detailTicket.data?.ticket
                                        ?.supportServiceType === 'GROUND_TASK'
                                        ? `Loại công việc: ${item?.subject}`
                                        : item?.content
                                }
                                style={style.txtDefault}
                            />
                        </View>
                    </TouchableOpacity>
                </View>
                {detailTicket.data?.ticket?.supportServiceType !==
                    'GROUND_TASK' && (
                    <View style={style.bottomItemTask}>
                        <View
                            style={{
                                paddingHorizontal: Mixins.scale(12),
                                paddingVertical: Mixins.scale(4),
                                borderRadius: Mixins.scale(8),
                                backgroundColor: item.done
                                    ? Colors.LIGHT_GREEN_10
                                    : Colors.DARK_YELLOW_10
                            }}>
                            <MyText
                                addSize={-1}
                                style={{
                                    color: item.done
                                        ? Colors.LIGHT_GREEN_50
                                        : Colors.DARK_YELLOW_40
                                }}
                                text={
                                    item.done
                                        ? translate('finish')
                                        : translate('unfinish')
                                }
                            />
                        </View>
                        <View style={stylesTicket.viewRowAlignItems}>
                            <Image
                                style={style.iconAvatarTask}
                                source={{
                                    uri: `${CONST_API.baseAvatarURI}${item?.receiverImage}`
                                }}
                            />
                            <TooltipFinishDay>
                                <Image
                                    style={{
                                        height: Mixins.scale(24),
                                        marginLeft: Mixins.scale(16),
                                        marginRight: Mixins.scale(16),
                                        width: Mixins.scale(24)
                                    }}
                                    source={{
                                        uri: 'ic_clock_ticket'
                                    }}
                                />
                            </TooltipFinishDay>

                            <MyText text={this.formatDate(item.dueTime)} />
                        </View>
                    </View>
                )}
            </View>
        );
    };
    handleUpdateTask = async (item) => {
        const { data } = this.props.detailTicket;
        const body = {
            ticketId: data?.ticket.id,
            taskId: this.state.itemSelectedTask.id,
            ticketTaskSubject: item?.seletedTemplate.name,
            ticketTaskContent: item?.ticketTaskContent,
            receivedUserId: item?.arrayReceivedUserId[0],
            ticketTaskStartTime: item?.ticketTaskStartTime,
            ticketTaskDueTime: item?.ticketTaskDueTime,
            ticketTaskDone: this.state.itemSelectedTask.done
        };
        global.props.showLoader();
        const respone = await this.props.actionTicket.actionCheckTask(body);
        this.initListTask();
        if (!respone?.error && respone) {
            global.props.hideLoader();
            Toast.show({
                type: 'success',
                text1: translate('edit_job_success'),
                position: 'bottom'
            });
        } else {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('edit_job_fail'),
                position: 'bottom'
            });
        }
    };
    handleSearchUser = (txt) => {
        const { detailTicket } = this.props;
        const data = {
            ticketId: detailTicket?.data.ticket.id,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: txt || ''
            },
            requestId: ''
        };
        this.props.actionHome.getListMemberGroup(data);
    };
    handleCreateWork = async (item) => {
        const { data } = this.props.detailTicket;
        this.props.dismissModalCreateWork();
        const body = {
            ticketId: data?.ticket.id,
            ticketTaskSubject: item?.seletedTemplate.name,
            ticketTaskContent: item?.ticketTaskContent,
            arrayReceivedUserId: item?.arrayReceivedUserId,
            ticketTaskStartTime: item?.ticketTaskStartTime,
            ticketTaskDueTime: item?.ticketTaskDueTime,
            ticketTaskDone: false
        };
        global.props.showLoader();
        const respone = await this.props.actionTicket.createWork(body);
        if (!respone?.error) {
            this.initListTask();

            global.props.hideLoader();

            setTimeout(() => {
                Toast.show({
                    type: 'success',
                    text1: translate('create_task_cuccess'),
                    position: 'bottom'
                });
            }, 1000);
        } else {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('create_task_fail'),
                position: 'bottom'
            });
        }
    };
    render() {
        const {
            renderHearder,
            renderTopHeader,
            xworkData,
            isShowModalCreateWork,
            listTemplateTask,
            dismissModalCreateWork
        } = this.props;
        const { component, fullProfile } = xworkData;
        const { isShowModalDetailTask, itemSelectedTask } = this.state;

        let checkUserCreateTask =
            itemSelectedTask?.creatorUsername === fullProfile.username;

        return (
            <View style={{ flex: 1 }}>
                {renderHearder}
                {renderTopHeader}
                {this.renderTaskList()}
                {isShowModalDetailTask && (
                    <component.ModalDetailTask
                        isVisible={isShowModalDetailTask}
                        itemSelectedTask={itemSelectedTask}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalDetailTask: false
                            });
                        }}
                        userCreateTask={checkUserCreateTask}
                        component={component}
                        listMember={this.props.listMemberGroup.data}
                        handleSearchUser={this.handleSearchUser}
                        listTemplateTask={this.props.listTemplateTask?.data}
                        API_CONST={CONST_API.baseAvatarURI}
                        handleUpdateTask={this.handleUpdateTask}
                    />
                )}
                {isShowModalCreateWork && (
                    <component.ModalCreateWork
                        onPressDimiss={dismissModalCreateWork}
                        listTemplateTask={listTemplateTask?.data}
                        handleSearchUser={this.handleSearchUser}
                        isVisible={isShowModalCreateWork}
                        listMember={this.props.listMemberGroup.data}
                        handleCreateWork={this.handleCreateWork}
                    />
                )}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.ticketReducer.listGroupTicket,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        slaTicketDetail: state.ticketReducer.slaTicketDetail,
        listTask: state.ticketReducer.listTask,
        listTemplateTask: state.ticketReducer.listTemplateTask,
        listMemberGroup: state.groupTicketReducer.listMemberGroup
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ListTask);

const style = StyleSheet.create({
    bottomItemTask: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginLeft: Mixins.scale(16),
        marginTop: Mixins.scale(8)
    },
    btnTitleTask: {
        alignItems: 'center',
        borderColor: Colors.GRAYF6,
        borderRadius: Mixins.scale(12),
        borderWidth: 1,
        flexDirection: 'row',
        height: Mixins.scale(56),
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(8),
        width: '100%'
    },

    iconAvatarTask: {
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(24),
        width: Mixins.scale(24)
    },
    imgCheck: {
        height: Mixins.scale(24),
        width: Mixins.scale(24)
    },
    txtDefault: {
        color: Colors.BLACK
    },

    viewText: {
        flexDirection: 'row',
        flex: 1
    }
});
