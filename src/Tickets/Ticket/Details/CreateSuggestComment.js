import React, { Component } from 'react';
import {
    View,
    Platform,
    KeyboardAvoidingView,
    TouchableOpacity,
    StyleSheet,
    TextInput,
    Image,
    Dimensions
} from 'react-native';
import { connect } from 'react-redux';

import { ThemeXwork } from '@mwg-sdk/styles';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, TouchableDebounce } from '@mwg-kits/components';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import Toast from 'react-native-toast-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

const { width } = Dimensions.get('window');
const { translate } = global.props.getTranslateConfig();
export class CreateSuggestComment extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isEdit: false,
            content: '',
            isRefresh: false,
            dataSuggestComment: [],
            enabled: true
        };
    }

    deleteSuggestComment = (item) => {
        global.props.alert({
            show: true,
            title: translate('delete_quick_reply'),
            titleColor: { color: Colors.DARK_RED_30 },
            message: translate('confirm_delete_quick_rep'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },
            onConfirmPressed: async () => {
                global.props.alert({ show: false });
                if (helper.hasProperty(item, 'id')) {
                    global.props.showLoader(`${translate('processing')}...`);
                    let response =
                        await this.props.actionTicket.deleteCommentSuggest(
                            item.id
                        );
                    if (
                        helper.IsValidateObject(response) &&
                        helper.hasProperty(response, 'error') &&
                        !response.error
                    ) {
                        await this.props.actionTicket.getCommentSuggest(true);
                        this.setState({
                            dataSuggestComment: this.props.suggestComment.data
                        });
                        Toast.show({
                            type: 'success',
                            text1: translate('delete_quick_rep_success'),
                            position: 'bottom'
                        });
                    } else {
                        Toast.show({
                            type: 'error',
                            text1: translate('delete_quick_rep_fail'),
                            position: 'bottom'
                        });
                    }

                    global.props.hideLoader();
                }
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };
    renderItemSuggest = ({ item, drag, isActive, getIndex }) => {
        const { xworkData } = this.props;
        const { ScaleDecorator } = xworkData;
        return (
            <View>
                <GestureHandlerRootView>
                    <TouchableOpacity
                        activeOpacity={1}
                        onPress={() => {
                            if (this.state.isEdit) {
                                this.props.navigation.navigate(
                                    'EditSuggestComment',
                                    {
                                        id: item?.id,
                                        name: item.name,
                                        priority: getIndex() + 1
                                    }
                                );
                            } else {
                                this.props.navigation.navigate('DetailTicket', {
                                    id: this.props.detailTicket.data?.ticket
                                        ?.id,
                                    indexHeader: 1,
                                    suggestComment: item?.name
                                });
                            }
                        }}
                        style={{
                            flex: 1,
                            marginHorizontal: 16,
                            justifyContent: this.state.isEdit
                                ? 'space-between'
                                : 'flex-start',
                            borderTopWidth: getIndex() === 0 ? 0 : 1,
                            borderBottomColor: ThemeXwork.neutrals.$950,
                            borderColor: ThemeXwork.neutrals.$950,
                            flexDirection: 'row',
                            alignItems: 'center',
                            paddingVertical: 12,
                            backgroundColor: isActive
                                ? ThemeXwork.neutrals.$950
                                : Colors.WHITE
                        }}>
                        <View
                            style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                                marginRight: 12
                            }}>
                            {this.state.isEdit && (
                                <TouchableOpacity
                                    onLongPress={drag}
                                    hitSlop={{ left: 5, right: 15 }}
                                    style={{ marginRight: 16 }}>
                                    <Image
                                        source={{ uri: 'ic_draggable' }}
                                        style={{ height: 24, width: 24 }}
                                    />
                                </TouchableOpacity>
                            )}
                            <MyText
                                text={item?.name}
                                style={{
                                    color: ThemeXwork.neutrals.$200,
                                    fontSize: 15
                                }}
                            />
                        </View>

                        {this.state.isEdit && (
                            <TouchableOpacity
                                onPress={() => this.deleteSuggestComment(item)}
                                hitSlop={{
                                    bottom: 5,
                                    top: 5,
                                    right: 5,
                                    left: 5
                                }}
                                style={{
                                    marginRight: 12,
                                    width: width * 0.15,
                                    alignItems: 'flex-end'
                                }}>
                                <Image
                                    source={{ uri: 'ic_remove' }}
                                    style={{ height: 20, width: 20 }}
                                />
                            </TouchableOpacity>
                        )}
                    </TouchableOpacity>
                </GestureHandlerRootView>
            </View>
        );
    };
    renderEmptySuggest = () => {
        if (this.state.isEdit) {
            return (
                <TouchableOpacity
                    // onPress={() => this.deleteSuggestComment(item)}
                    style={{ marginRight: 12 }}>
                    <Image
                        source={{ uri: 'ic_remove' }}
                        style={{ height: 20, width: 20 }}
                    />
                </TouchableOpacity>
            );
        }
        return null;
    };
    handleResfresh = () => {
        this.setState({ isRefresh: true }, async () => {
            await this.props.actionTicket.getCommentSuggest(true);
            this.setState({
                dataSuggestComment: this.props.suggestComment.data
            });
        });

        this.setState({ isRefresh: false });
    };

    renderListSuggest = () => {
        const { xworkData } = this.props;
        const { DraggableFlatList, GestureHandler } = xworkData;

        return (
            <DraggableFlatList
                data={this.state.dataSuggestComment}
                showsVerticalScrollIndicator={false}
                renderItem={this.renderItemSuggest}
                onDragEnd={({ data, from, to }) =>
                    this.createSuggestComment(data, from, to)
                }
                onDragBegin={() => {
                    this.setState({ enabled: false });
                }}
                containerStyle={{ flex: 1 }}
                extraData={this.state.isEdit}
                style={{ marginTop: 24 }}
                contentContainerStyle={{ flexGrow: 1 }}
                keyExtractor={(item, index) => `${index}`}
                refreshControl={
                    <GestureHandler.RefreshControl
                        progressViewOffset={0}
                        refreshing={this.state.isRefresh}
                        tintColor={'#9FC6FF'}
                        colors={['#9FC6FF']}
                        onRefresh={this.handleResfresh}
                        enabled={this.state.enabled}
                    />
                }
            />
        );
    };
    isDragItem = (from, to) => {
        if (helper.IsValidateObject(from) || helper.IsValidateObject(to)) {
            return true;
        }
        return false;
    };
    createSuggestComment = async (data, from, to) => {
        if (this.isDragItem(from, to)) {
            this.setState({ dataSuggestComment: data, enabled: true });
        }

        if (this.state.content.length > 0 || this.isDragItem(from, to)) {
            if (!this.isDragItem(from, to)) {
                global.props.showLoader(`${translate('processing')}...`);
            }

            let body = this.isDragItem(from, to)
                ? {
                      name: this.props.suggestComment.data[from]?.name,
                      id: this.props.suggestComment.data[from]?.id,
                      priority: to + 1
                  }
                : { name: this.state.content };
            let response =
                await this.props.actionTicket.createUpdateCommentSuggest({
                    ...body,
                    type: 'TICKET'
                });
            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.hasProperty(response.object, 'name') &&
                helper.hasProperty(response, 'error') &&
                !response.error
            ) {
                await this.props.actionTicket.getCommentSuggest(true);
                if (!this.isDragItem(from, to)) {
                    this.setState({
                        content: '',
                        dataSuggestComment: this.props.suggestComment.data
                    });

                    Toast.show({
                        type: 'success',
                        text1: translate('create_quick_rep_success'),
                        position: 'bottom'
                    });
                }
            } else {
                if (this.isDragItem(from, to)) {
                    this.setState({
                        dataSuggestComment: this.props.suggestComment.data
                    });
                    Toast.show({
                        type: 'success',
                        text1: translate('change_location_fail'),
                        position: 'bottom'
                    });
                } else {
                    Toast.show({
                        type: 'error',
                        text1: translate('create_quick_rep_fail'),
                        position: 'bottom'
                    });
                }
            }
            if (!this.isDragItem(from, to)) {
                global.props.hideLoader();
            }
        } else {
            if (!this.isDragItem(from, to)) {
                Toast.show({
                    type: 'error',
                    text1: translate('not_enter_quick_rep'),
                    position: 'bottom'
                });
            }
        }
    };
    renderTextInput = () => {
        return (
            <View style={style.inputComment}>
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        paddingVertical: 5
                    }}>
                    <TextInput
                        style={{
                            minHeight: Mixins.scale(40),
                            maxHeight: Mixins.scale(60),
                            width: '100%',
                            paddingLeft: 16,
                            paddingRight: 40,
                            paddingTop: Mixins.scale(12)
                        }}
                        ref={(ref) => (this.refTextInput = ref)}
                        value={this.state.content}
                        onChangeText={(text) => {
                            this.setState({ content: text });
                        }}
                        textAlignVertical="top"
                        keyboardType="default"
                        placeholder={`${translate('enter_quick_rep')}...`}
                        multiline
                        placeholderTextColor={ThemeXwork.neutrals.$500}
                    />
                    <View style={{ position: 'absolute', right: 0 }}>
                        <TouchableDebounce
                            onPress={this.createSuggestComment}
                            debounceTime={200}
                            hitSlop={{ top: 5, bottom: 5 }}
                            style={{
                                marginRight: 8,
                                alignItems: 'center',
                                height: Mixins.scale(24),
                                justifyContent: 'center',
                                width: Mixins.scale(24)
                            }}>
                            <Image
                                resizeMode="contain"
                                style={{
                                    height: Mixins.scale(24),
                                    width: Mixins.scale(24)
                                }}
                                source={{ uri: 'ic_send' }}
                            />
                        </TouchableDebounce>
                    </View>
                </View>
            </View>
        );
    };
    componentDidMount() {
        this.props.navigation.addListener('focus', () => {
            this.componentWillFocus();
        });
    }

    componentWillFocus() {
        this.setState({ dataSuggestComment: this.props.suggestComment.data });
    }

    render() {
        const { xworkData } = this.props;
        const { component } = xworkData;
        if (component === undefined) return null;
        const { WrapperContainerTicket } = component;
        return (
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={{ flex: 1, backgroundColor: Colors.WHITE }}>
                <View style={{ flex: 1 }}>
                    <WrapperContainerTicket
                        navigation={this.props.navigation}
                        nameTitle={translate('quick_reply')}
                        centerAlign={false}
                        colorBackButton={Colors.DARK_BLUE_50}
                        onPressBack={() => this.props.navigation.goBack()}
                        isSuccess={this.props.suggestComment.isSuccess}
                        messageEmpty={translate('no_quick_reply_template')}
                        messageLoading={translate('getting_quick_reply_list')}
                        messageError={translate('something_wrong_server')}
                        isError={this.props.suggestComment.isError}
                        isLoading={this.props.suggestComment.isFetching}
                        actionRetry={this.props.actionTicket.getCommentSuggest}
                        isEmpty={this.props.suggestComment.data.length === 0}
                        titleButtonEditTicket={
                            this.state.isEdit
                                ? translate('done')
                                : translate('edit')
                        }
                        onPressEditTicket={
                            this.props.suggestComment.data.length !== 0
                                ? () =>
                                      this.setState((prevState) => ({
                                          isEdit: !prevState.isEdit
                                      }))
                                : null
                        }>
                        {this.renderListSuggest()}
                    </WrapperContainerTicket>
                </View>
                {this.renderTextInput()}
            </KeyboardAvoidingView>
        );
    }
}

const mapStateToProps = (state) => ({
    xworkData: state.groupTicketReducer.xworkData,
    suggestComment: state.ticketReducer.suggestComment,
    detailTicket: state.ticketReducer.detailTicket
});

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch)
    };
};

export default connect(
    mapStateToProps,
    mapDispatchToProps
)(CreateSuggestComment);
const style = StyleSheet.create({
    inputComment: {
        backgroundColor: Colors.WHITE,
        borderColor: ThemeXwork.neutrals.$900,
        borderRadius: Mixins.scale(12),
        borderWidth: 0.5,
        marginBottom: Mixins.scale(32),
        marginHorizontal: Mixins.scale(16),
        marginTop: Mixins.scale(10),
        maxHeight: Mixins.scale(180)
    }
});
