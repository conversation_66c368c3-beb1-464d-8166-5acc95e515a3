import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    Image,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../../action';
import * as _actionTicket from '../action';
import ImagePicker from 'react-native-image-crop-picker';
import { toastConfig } from '../../GroupTicket/MemberGroupTicket';
import Toast from 'react-native-toast-message';
import ShowMoreText from '../Components/ShowMoreText';
import {
    TooltipCopyPaste,
    TooltipCall
} from '../../GroupTicket/CompoentTooltip';
import { stylesTicket } from '../stylesTicket';
import { RenderFile, RenderUserApprove } from './Component/ComponentTicket';
import ModalUser from '../../../modal/ModalUser';
const { translate } = global.props.getTranslateConfig();
import { CONST_API } from '../../../constant';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { UploadFileOrPhoto } from '../Components/ComponentItemTicket';
import RenderHtml from 'react-native-render-html';
import { GlobalStore } from 'redux-micro-frontend';
import {
    InformationPaymentApprove,
    RenderRating
} from './Component/ComponentDetailTicket';
class InformationTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            showReadMore: false,
            showwReadMoreSolution: false,
            isVisible: false,
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            },
            isShowModalUser: false,
            listUser: [],
            titleModal: '',
            endCallTime: 0
        };
        this.globalStore = GlobalStore.Get();
        this.globalStateChanged = this.globalStateChanged.bind(this);
        this.globalStore.SubscribeToGlobalState(
            'XworkStore',
            this.globalStateChanged
        );
    }

    globalStateChanged = (state) => {
        this.setState({
            endCallTime: state.XworkStore.endCallTime
        });
    };

    componentDidMount() {}
    getUserMentioned = (item) => {
        let str = item;
        const arr = [];
        let fullStr = '';
        while (str?.indexOf('{') !== -1) {
            const left = str?.indexOf('{');
            const right = str?.indexOf('}') + 1;
            const data = str?.slice(left, right);
            str = str?.slice(0, left) + str?.slice(right, str.length);
            arr.push(data);
        }
        if (arr && arr.length > 0) {
            arr.forEach((element) => {
                if (helper.IsValidateObject(element.userName)) {
                    fullStr =
                        fullStr + `${element?.userName}-${element?.fullName} `;
                }
                if (helper.IsValidateObject(element.name)) {
                    fullStr = fullStr + `#${element.name} `;
                }
            });
        }
        str = str?.replace(/@mentionuser:/g, ' ');
        str = str?.replace(/#hashtag:/g, ' ');
        str = str?.replace(/->-/g, '>');
        str = str?.replace(/-<-/g, '<');
        str = str.trim();
        fullStr = fullStr + str;
        return { content: fullStr, arrUser: arr, str: str };
    };

    renderTextDescription = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;

        let contentItem = '';
        contentItem = this.getUserMentioned(data?.ticket?.content);
        const source = {
            html: `<a>${contentItem?.content}</a>`
        };

        if (data?.ticket?.content?.length > 0) {
            return (
                <View
                    style={{
                        minHeight: Mixins.scale(35),
                        marginVertical: Mixins.scale(16)
                    }}>
                    {data?.ticket?.content?.includes('-<-') ||
                    data?.ticket?.content?.includes('<p') ||
                    data?.ticket?.content?.includes('</span>') ||
                    data?.ticket?.content?.includes('<br') ? (
                        <RenderHtml contentWidth={'100%'} source={source} />
                    ) : (
                        <ScrollView nestedScrollEnabled={true}>
                            <TooltipCopyPaste
                                dataCopy={contentItem.str}
                                children={() => {
                                    return (
                                        <View style={{}}>
                                            {contentItem?.arrUser?.length > 0 &&
                                                contentItem?.arrUser?.map(
                                                    (element, index) => {
                                                        if (
                                                            helper.IsValidateObject(
                                                                element.name
                                                            )
                                                        )
                                                            return (
                                                                <MyText
                                                                    key={`${index}_${element.name}`}
                                                                    typeFont="semiBold"
                                                                    text={`#${element?.name}`}
                                                                    style={{
                                                                        color: Colors.DARK_BLUE_60,
                                                                        fontSize: 13
                                                                    }}
                                                                />
                                                            );
                                                        return null;
                                                    }
                                                )}
                                            <ShowMoreText
                                                content={contentItem.str}
                                                maxLine={7}
                                                lineHeight={18}
                                                onPress={() => {
                                                    this.setState(
                                                        (prevState) => ({
                                                            showReadMore:
                                                                !prevState.showReadMore
                                                        })
                                                    );
                                                }}
                                            />
                                        </View>
                                    );
                                }}
                            />
                        </ScrollView>
                    )}
                </View>
            );
        }
        return (
            <View
                style={{
                    flex: 1,
                    flexDirection: 'row',
                    marginVertical: Mixins.scale(16)
                }}>
                <TooltipCopyPaste
                    dataCopy={contentItem}
                    children={() => {
                        return (
                            <MyText
                                numberOfLines={2}
                                style={{
                                    color: Colors.BLACK
                                }}
                                text={
                                    helper.IsEmptyString(data?.ticket?.content)
                                        ? translate('no_detail_descip')
                                        : contentItem.content
                                }
                            />
                        );
                    }}
                />
            </View>
        );
    };

    renderHtmlText = (text = '') => {
        let htmlContent = text?.replace(/\n/g, '<br>') || '';
        return (
            <RenderHtml
                source={{ html: `<span>${htmlContent}</span>` }}
                tagsStyles={{
                    span: { lineHeight: 20, color: Colors.BLACK }
                }}
            />
        );
    };

    renderTextApprove = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        if (data?.ticket?.content) {
            return (
                <View
                    style={{
                        marginVertical: Mixins.scale(16)
                    }}>
                    {/* <MyText
                        style={style.txtDefault}
                        text={`${data?.ticket?.content}`}
                    /> */}
                    {this.renderHtmlText(data?.ticket?.content)}
                </View>
            );
        }

        return (
            <MyText
                numberOfLines={2}
                style={{
                    marginVertical: Mixins.scale(16),
                    color: Colors.BLACK
                }}
                text={
                    helper.IsEmptyString(data?.ticket?.content)
                        ? translate('no_detail_descip')
                        : data?.ticket?.content
                }
            />
        );
    };

    handleShowModalUser = (id) => {
        const { detailTicket, listMemberGroup } = this.props;
        const { ticket } = detailTicket.data;

        let user = [];
        let title = '';
        let phoneNumber = '';
        if (id === 0) {
            listMemberGroup.data.filter((e) => {
                if (e.user.username === ticket?.creatorUserName) {
                    phoneNumber = e.user.phone;
                }
            });

            user = [
                {
                    userName: ticket?.creatorUserName,
                    userFirstName: ticket?.creatorFirstName,
                    userLastName: ticket?.creatorLastName,
                    userImage: ticket?.creatorImage,
                    phoneNumber: phoneNumber
                }
            ];
            title = translate('ticket_creator');
        } else if (id === 1) {
            listMemberGroup.data.filter((e) => {
                if (e.user.username === ticket?.assigneeUsername) {
                    phoneNumber = e.user.phone;
                }
            });
            user = [
                {
                    userName: ticket?.assigneeUsername,
                    userFirstName: ticket?.assigneeFirstName,
                    userLastName: ticket?.assigneeLastName,
                    userImage: ticket?.assigneeImage,
                    phoneNumber: phoneNumber
                }
            ];
            title = translate('receiver');
        } else {
            title = translate('watcher');

            let arrPhone = [];

            ticket.watcherUsers.map((item, index) => {
                listMemberGroup.data.filter((e) => {
                    if (e.user.username === item.userName) {
                        arrPhone.push(e.user.phone);
                    }
                });
                let obj = {
                    ...item,
                    phoneNumber: arrPhone[index]
                };
                user.push(obj);
            });
        }
        this.setState(
            {
                titleModal: title,
                listUser: user
            },
            () => {
                this.setState({
                    isShowModalUser: true
                });
            }
        );
    };

    checkShowTooltip = async () => {
        const isShow = await AsyncStorage.getItem('SHOW_TOOLTIP_CALL');
        if (isShow === '1') {
            return false;
        } else {
            AsyncStorage.setItem('SHOW_TOOLTIP_CALL', '1');
            return true;
        }
    };

    renderChip = (text, index) => {
        return (
            <View
                key={index}
                style={{
                    backgroundColor: Colors.GRAYF5,
                    borderRadius: Mixins.scale(8),
                    paddingHorizontal: Mixins.scale(12),
                    paddingVertical: Mixins.scale(10),
                    marginRight: Mixins.scale(8),
                    marginBottom: Mixins.scale(8),
                    borderWidth: 1,
                    borderColor: Colors.GRAYF4,
                    flexDirection: 'row',
                    alignItems: 'center',
                    minHeight: Mixins.scale(36)
                }}>
                <MyText
                    text={text}
                    style={{
                        color: Colors.BLACK
                    }}
                    numberOfLines={2}
                />
            </View>
        );
    };

    parseSolutionData = (extraData4) => {
        try {
            // If it's a string and looks like JSON array
            if (typeof extraData4 === 'string') {
                // Try to parse JSON
                if (
                    extraData4.trim().startsWith('[') &&
                    extraData4.trim().endsWith(']')
                ) {
                    const parsed = JSON.parse(extraData4);
                    if (Array.isArray(parsed)) {
                        return parsed.map(
                            (item) => item.name || item.toString()
                        );
                    }
                }
                // If it's a plain string, split by /
                return [extraData4.toString()];
            }
            // If it's already an array
            if (Array.isArray(extraData4)) {
                return extraData4.map((item) => item.name || item.toString());
            }
            // Fallback: convert to string
            return [extraData4.toString()];
        } catch (error) {
            console.log('Error parsing solution data:', error);
            // Fallback: treat as string
            if (typeof extraData4 === 'string') {
                return [extraData4.toString()];
            }
            return [extraData4.toString() || ''];
        }
    };

    renderSolution = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        if (
            !helper.hasProperty(data?.ticket, 'extraData4') ||
            helper.IsEmptyString(data?.ticket?.extraData4)
        ) {
            return null;
        }

        const extraData4 = data?.ticket?.extraData4;

        // Parse data to array
        const solutionItems = this.parseSolutionData(extraData4);

        return (
            <View style={{ marginBottom: 16 }}>
                <View style={style.viewRowBottom}>
                    <MyText
                        text={translate('solution')}
                        addSize={1}
                        style={style.txtDefault}
                        typeFont="medium"
                    />
                </View>
                <View
                    style={{
                        marginTop: 16,
                        flexDirection: 'row',
                        flexWrap: 'wrap'
                    }}>
                    {solutionItems.map((item, index) =>
                        this.renderChip(item, index)
                    )}
                </View>
            </View>
        );
    };

    renderContent = () => {
        const { detailTicket, xworkData, listFileTicket, streamToken } =
            this.props;
        const { data } = detailTicket;
        const { common } = xworkData;

        /// Check Render with Payment Approve
        let checkUserAdmin = data?.ticket?.creatorPrefix === 'internal';
        const fullUserCreate = `${data?.ticket?.creatorUserName}-${data?.ticket?.creatorLastName} ${data?.ticket?.creatorFirstName}`;
        let isPaymentApprove =
            detailTicket.data?.ticket?.supportServiceType === 'APPROVE_MBBANK';
        return (
            <View style={{ marginTop: Mixins.scale(24) }}>
                <View style={style.viewRowBottom}>
                    <MyText
                        text={translate('detail_descrip')}
                        addSize={1}
                        style={style.txtDefault}
                        typeFont="medium"
                    />
                </View>

                {helper.IsValidateObject(data.approveType)
                    ? this.renderTextApprove()
                    : this.renderTextDescription()}
                {/* Chi tiết phiếu */}
                {this.renderSolution()}
                {isPaymentApprove && (
                    <InformationPaymentApprove
                        data={detailTicket.data}
                        listFileTicket={listFileTicket.data}
                        streamToken={streamToken}
                        setViewMedia={this.setViewMedia}
                    />
                )}

                <View
                    style={[
                        style.styleRow,
                        { justifyContent: 'space-between', marginTop: 8 }
                    ]}>
                    <View style={style.viewRowBottom}>
                        <Image
                            style={style.imgCalendar}
                            source={{ uri: 'ic_calendar' }}
                        />
                        <MyText
                            text={translate('date')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                    <View style={style.viewDate}>
                        <MyText
                            text={`${common.helper.getFullDate(
                                data?.ticket?.startDateLong
                            )}   -   ${common.helper.getFullDate(
                                data?.ticket?.dueDateLong
                            )}`}
                            addSize={1}
                            style={style.txtDefault}
                        />
                    </View>
                </View>
                <View style={style.styleRow}>
                    <Image
                        style={style.imgLeft}
                        source={{ uri: 'ic_profile' }}
                    />
                    <MyText
                        text={translate('ticket_creator')}
                        addSize={1}
                        style={{
                            color: Colors.BLACK_HEADER_TITLE,
                            marginHorizontal: Mixins.scale(8)
                        }}
                        typeFont="medium"
                    />
                </View>
                <TooltipCall onCheckVisible={() => this.checkShowTooltip()}>
                    <TouchableOpacity
                        activeOpacity={1}
                        style={{ flex: 1, marginTop: Mixins.scale(12) }}
                        onPress={() => {
                            this.handleShowModalUser(0);
                        }}>
                        <View style={style.viewUserImage}>
                            <Image
                                style={style.imgWatched}
                                source={{
                                    uri: `${CONST_API.baseAvatarURI}${data?.ticket?.creatorImage}`
                                }}
                            />
                            <View style={{ flex: 1 }}>
                                <MyText
                                    text={
                                        checkUserAdmin
                                            ? 'Admin'
                                            : fullUserCreate
                                    }
                                    style={{
                                        color: Colors.BLACK,
                                        marginLeft: 8
                                    }}
                                    numberOfLines={1}
                                />
                            </View>
                        </View>
                    </TouchableOpacity>
                </TooltipCall>
                {helper.IsValidateObject(data?.ticket?.assigneeImage) && (
                    <>
                        <View
                            disabled={helper.IsValidateObject(data.approveType)}
                            style={style.styleRow}>
                            <Image
                                style={style.imgLeft}
                                source={{ uri: 'ic_profile' }}
                            />
                            <MyText
                                text={translate('receiver')}
                                addSize={1}
                                style={{
                                    color: Colors.BLACK_HEADER_TITLE,
                                    marginHorizontal: Mixins.scale(8)
                                }}
                                typeFont="medium"
                            />
                        </View>
                        <TouchableOpacity
                            style={{ flex: 1, marginTop: 12 }}
                            activeOpacity={1}
                            onPress={() => {
                                this.handleShowModalUser(1);
                            }}>
                            <View style={style.viewUserImage}>
                                <Image
                                    style={style.imgWatched}
                                    source={{
                                        uri: `${CONST_API.baseAvatarURI}${data?.ticket?.assigneeImage}`
                                    }}
                                />
                                <View style={{}}>
                                    <MyText
                                        text={`${data?.ticket?.assigneeUsername}-${data?.ticket?.assigneeLastName} ${data?.ticket?.assigneeFirstName}`}
                                        style={{
                                            color: Colors.BLACK,
                                            marginLeft: 8,
                                            marginRight: 4
                                        }}
                                        numberOfLines={1}
                                    />
                                </View>
                            </View>
                        </TouchableOpacity>
                    </>
                )}
                {data?.ticket?.watcherUsers?.length > 0 && (
                    <TouchableOpacity
                        activeOpacity={1}
                        onPress={() => {
                            this.handleShowModalUser(2);
                        }}
                        style={style.styleRow}>
                        <Image
                            style={style.imgLeft}
                            source={{ uri: 'ic_add_group' }}
                        />
                        <MyText
                            text={translate('watcher')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginHorizontal: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                        <View style={stylesTicket.viewRowAlignItems}>
                            {data?.ticket?.watcherUsers?.map((item, index) => {
                                if (index < 3) {
                                    return (
                                        <Image
                                            key={index}
                                            style={{
                                                zIndex: index,
                                                position: 'absolute',
                                                left:
                                                    index === 0
                                                        ? 0
                                                        : Mixins.scale(
                                                              20 * index
                                                          ),
                                                height: Mixins.scale(28),
                                                width: Mixins.scale(28),
                                                borderRadius: Mixins.scale(14)
                                            }}
                                            source={{
                                                uri: `${CONST_API.baseAvatarURI}${item?.userImage}`
                                            }}
                                        />
                                    );
                                }
                            })}
                            {data?.ticket?.watcherUsers?.length > 3 && (
                                <View
                                    style={{
                                        zIndex: 100,
                                        position: 'absolute',
                                        height: Mixins.scale(28),
                                        width: Mixins.scale(28),
                                        borderRadius: Mixins.scale(14),
                                        left: Mixins.scale(20 * 3),
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: Colors.GRAYF6
                                    }}>
                                    <MyText
                                        text={`+${
                                            parseInt(
                                                data?.ticket?.watcherUsers
                                                    ?.length
                                            ) - 3
                                        }`}
                                        addSize={-2}
                                        style={{
                                            color: Colors.WHITE
                                        }}
                                    />
                                </View>
                            )}
                        </View>
                    </TouchableOpacity>
                )}
                {helper.hasProperty(data?.ticket, 'locationGeoName') &&
                    !isPaymentApprove && (
                        <View style={{}}>
                            <View
                                style={[
                                    style.styleRow,
                                    {
                                        marginTop: Mixins.scale(32)
                                    }
                                ]}>
                                <Image
                                    style={style.imgLocation}
                                    resizeMode="contain"
                                    source={{ uri: 'ic_location_ticket' }}
                                />
                                <MyText
                                    text={translate('location')}
                                    addSize={1}
                                    style={{
                                        color: Colors.BLACK_HEADER_TITLE,
                                        marginHorizontal: Mixins.scale(8)
                                    }}
                                    typeFont="medium"
                                />
                            </View>
                            <View style={style.btnLocation}>
                                <MyText
                                    numberOfLines={1}
                                    text={data?.ticket?.locationGeoName}
                                    addSize={1}
                                    style={style.txtDefault}
                                />
                            </View>
                        </View>
                    )}
            </View>
        );
    };
    uploadImages = async (respone) => {
        const { detailTicket, initListFile } = this.props;
        try {
            const body = {
                files: respone,
                ticketId: detailTicket?.data?.ticket?.id,
                comment: false
            };
            const sendImage = await this.props.actionTicket.uploadImageComment(
                body
            );
            global.props.hideLoader();
            initListFile();
            try {
                if (sendImage) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_image_success')
                    });
                }
            } catch (error) {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('upload_image_failed')
                });
            }
        } catch (e) {
            console.log(e, '12312312321');
        }
    };

    pickerGallery = async (item) => {
        const respone = await UploadFileOrPhoto(item.id);
        try {
            if (item.id === 0) {
                this.uploadImages(respone);
            } else {
                this.upLoadFile(respone);
            }
        } catch (error) {
            console.log(error, '1231231');
        }
    };

    handleShowVideo = async () => {
        const { detailTicket, initListFile } = this.props;
        const respone = await ImagePicker.openPicker({
            mediaType: 'video',
            compressImageMaxWidth: 1000,
            compressImageMaxHeight: 1000,
            compressImageQuality: 1,
            durationLimit: 60,
            maximumVideoDuration: 60000,
            compressVideoPreset: 'HighestQuality'
        });
        try {
            if (respone) {
                const body = {
                    files: respone,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };

                const sendVideo =
                    await this.props.actionTicket.uploadImageComment(body);
                global.props.hideLoader();
                initListFile();

                if (sendVideo) {
                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('upload_video_success')
                    });
                }
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('upload_video_failed')
            });
        }
    };
    handleRemoveFile = (item) => {
        global.props.alert({
            show: true,
            title: translate('delete_file'),
            titleColor: {
                color: Colors.DARK_RED_30
            },
            message: translate('ask_delete_file'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
                this.removeFile(item);
            },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
            }
        });
    };
    removeFile = async (item) => {
        const { detailTicket } = this.props;
        let data = {
            ticketId: detailTicket.data?.ticket.id,
            fileId: item.id
        };
        try {
            global.props.showLoader();
            const response = await this.props.actionTicket.removeFile(data);
            this.props.initListFile();
            global.props.hideLoader();

            if (response) {
                global.props.hideLoader();

                Toast.show({
                    type: 'success',
                    text1: translate('delete_file_success')
                });
            } else {
                global.props.hideLoader();
                Toast.show({
                    type: 'error',
                    text1: translate('delete_file_failed')
                });
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: error
            });
        }
    };
    upLoadFile = async (response) => {
        try {
            const { detailTicket, initListFile } = this.props;
            if (response) {
                const body = {
                    files: response,
                    ticketId: detailTicket?.data?.ticket?.id,
                    comment: false
                };
                const sendFile = await this.props.actionTicket.uploadFile(body);
                try {
                    if (sendFile) {
                        initListFile();
                        global.props.hideLoader();
                        Toast.show({
                            type: 'success',
                            text1: translate('upload_file_success')
                        });
                    }
                } catch (error) {
                    Toast.show({
                        type: 'error',
                        text1: translate('upload_file_fail')
                    });
                }
            }
        } catch (e) {
            console.log(e);
        }
    };
    handleUpFile = async (item) => {
        setTimeout(() => {
            this.pickerGallery(item);
        }, 1000);
    };
    setViewMedia = (index) => {
        this.setState({
            viewMedia: {
                index: index,
                visible: true
            }
        });
    };

    renderFile = () => {
        const { detailTicket } = this.props;
        return (
            <RenderFile
                handleUpFile={this.handleUpFile}
                handleRemoveFile={this.handleRemoveFile}
                setViewMedia={this.setViewMedia}
                isShowRemove={false}
                hideAddFile={
                    detailTicket.data?.ticket?.supportServiceType ===
                    'APPROVED_MATERIAL_COST'
                }
            />
        );
    };

    renderActivity = () => {
        const { props } = this.props;
        return (
            <View
                style={{
                    paddingBottom: Mixins.scale(16),
                    marginTop: Mixins.scale(32)
                }}>
                <View
                    style={[
                        style.viewRow,
                        { justifyContent: 'space-between' }
                    ]}>
                    <View style={style.viewRowBottom}>
                        <Image
                            style={style.imgLeftBottom}
                            source={{ uri: 'ic_clock' }}
                        />
                        <MyText
                            text={translate('activity')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                    <TouchableOpacity
                        onPress={() => {
                            props.navigation.navigate('TicketActivityHistory');
                        }}
                        style={{}}>
                        <MyText
                            style={{
                                color: Colors.DARK_BLUE_60
                            }}
                            numberOfLines={1}
                            typeFont="semiBold"
                            text={translate('see_detail')}
                        />
                    </TouchableOpacity>
                </View>
            </View>
        );
    };

    onRequestClose = () => {
        this.setState({
            viewMedia: {
                index: 0,
                visible: false,
                uris: []
            }
        });
    };

    checkQuickTicket = () => {
        const { detailTicket } = this.props;
        const { data } = detailTicket;
        let checkQuickTicket = false;
        if (
            (helper.hasProperty(data?.ticket, 'asSocial') &&
                data.ticket.asSocial) ||
            (helper.hasProperty(data, 'ticketType') &&
                data?.ticketType === 'social')
        ) {
            checkQuickTicket = true;
            return checkQuickTicket;
        }
    };
    checkClickVideo = (item) => {
        return item?.filemime.includes('video');
    };

    render() {
        const {
            renderHearder,
            renderTopHeader,
            detailTicket,
            xworkData,
            listFileTicket,
            streamToken,
            props,
            showRating
        } = this.props;
        const { common } = xworkData;
        const { viewMedia } = this.state;
        let isPaymentApprove =
            detailTicket.data?.ticket?.supportServiceType === 'APPROVE_MBBANK';
        return (
            <View style={{ flex: 1 }}>
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ paddingBottom: 70 }}
                    style={style.container}>
                    {renderHearder}
                    <RenderRating
                        showRating={showRating}
                        xworkData={xworkData}
                        actionRatingGetInfo={this.props.actionRatingGetInfo}
                        detailTicket={detailTicket}
                    />
                    {!helper.IsValidateObject(detailTicket?.data.approveType) &&
                        !isPaymentApprove &&
                        renderTopHeader}
                    {this.renderContent()}
                    {!isPaymentApprove && this.renderFile()}
                    {helper.IsValidateObject(detailTicket?.data.approveType) ||
                    detailTicket?.data.approveType === 'PENDING' ? (
                        <RenderUserApprove />
                    ) : (
                        this.renderActivity()
                    )}
                </ScrollView>
                {this.state.isShowModalUser && (
                    <ModalUser
                        title={this.state.titleModal}
                        isVisible={this.state.isShowModalUser}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalUser: false
                            });
                        }}
                        detailTicket={this.props.detailTicket.data}
                        listUser={this.state.listUser}
                        onPressCall={(data, typeCall) => {
                            props.navigation.navigate('CallCenter', {
                                videoCallData:
                                    typeCall === 'video' ? data : null,
                                infoCallApps: typeCall === 'audio' ? data : null
                            });
                            this.setState({
                                isShowModalUser: false
                            });
                        }}
                        endCallTime={this.state.endCallTime}
                    />
                )}

                {viewMedia.visible && (
                    <common.ViewFile
                        visible={viewMedia.visible}
                        imageUrls={listFileTicket?.data}
                        index={viewMedia.index}
                        onPress={this.onRequestClose}
                        checkQuickTicket={this.checkQuickTicket()}
                        onSwipeDown={() => this.onRequestClose()}
                        onRequestClose={this.onRequestClose}
                        streamToken={streamToken?.data?.token}
                        toastConfig={toastConfig}
                        isClickVideo={this.checkClickVideo(
                            listFileTicket?.data[viewMedia.index]
                        )}
                    />
                )}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.ticketReducer.listGroupTicket,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        streamToken: state.ticketReducer.streamToken,
        listMemberGroup: state.groupTicketReducer.listMemberGroup
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(InformationTicket);

const style = StyleSheet.create({
    btnLocation: {
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(16),
        height: Mixins.scale(52),
        justifyContent: 'center',
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(12),
        width: '100%'
    },

    container: {
        // flex: 1,
        // paddingBottom: 60
    },
    imgCalendar: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },

    imgLeft: {
        height: Mixins.scale(20),
        tintColor: Colors.DARK_BLUE_60,
        width: Mixins.scale(20)
    },
    imgLeftBottom: {
        height: Mixins.scale(20),
        width: Mixins.scale(20)
    },
    imgLocation: {
        height: Mixins.scale(20),
        width: Mixins.scale(20)
    },

    imgWatched: {
        borderRadius: Mixins.scale(14),
        height: Mixins.scale(28),
        width: Mixins.scale(28)
    },
    styleApprove: {
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    styleRow: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(24)
    },

    txtDefault: {
        color: Colors.BLACK
    },
    viewDate: {
        alignItems: 'center',
        borderRadius: 12,
        flexDirection: 'row',
        marginLeft: Mixins.scale(10),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(4)
    },

    viewRow: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    viewRowBottom: {
        alignItems: 'center',
        flexDirection: 'row'
    },
    viewUserImage: {
        alignItems: 'center',

        flexDirection: 'row'
    }
});
