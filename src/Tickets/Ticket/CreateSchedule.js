import React, { PureComponent } from 'react';
import {
    TextInput,
    TouchableOpacity,
    View,
    Image,
    KeyboardAvoidingView,
    TouchableWithoutFeedback,
    Keyboard,
    BackHandler
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import * as _actionHome from '../action';
import * as _actionTicket from './action';
import * as _actionGroundTask from './Details/GroundTask/action';
import Toast from 'react-native-toast-message';
import { toastConfig } from '../GroupTicket/MemberGroupTicket';
import AsyncStorage from '@react-native-async-storage/async-storage';
const { translate } = global.props.getTranslateConfig();

class CreateSchedule extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            isLoading: false,
            title: '',
            checkShowButton: false
        };
    }
    componentDidMount() {
        BackHandler.addEventListener('hardwareBackPress', this.handleBackPress);
    }
    componentWillUnmount() {
        BackHandler.removeEventListener(
            'hardwareBackPress',
            this.handleBackPress
        );
    }
    handleBackPress = () => {
        const { params } = this.props.route;
        if (this.state.isLoading) {
            return;
        } else {
            if (params) {
                this.props.navigation.navigate('TicketList', params);
            } else {
                this.props.navigation.goBack();
            }
        }
    };
    setIsLoading = (state) => {
        this.setState({ isLoading: state });
    };
    handleButtonSave = async () => {
        const { xworkData } = this.props;
        const { fullProfile } = this.props.xworkData;
        try {
            global.props.showLoader();
            this.setIsLoading(true);
            const getManager = await this.props.actionGroundTask.getManager(
                fullProfile.id
            );
            if (getManager) {
                const body = {
                    supportServiceId: this.props.route.params,
                    subject: this.state.title,
                    content: 'Service Ground Task',
                    startTime: new Date().getTime(),
                    source: 'phone',
                    createdUserId: -1,
                    statusId: -1,
                    watcherUserId: [
                        getManager.userinfo.manager.id,
                        getManager.userinfo.leader.id
                    ],
                    assigneeUserId: xworkData?.fullProfile.id || -1
                };
                const reponse = await this.props.actionTicket.createTicket(
                    body
                );
                if (reponse) {
                    if (xworkData.screenName === 'CreateSchedule') {
                        AsyncStorage.setItem(
                            'EDIT_TICKET',
                            JSON.stringify(reponse)
                        );
                    }
                    const data = {
                        supportServiceId: this.props.route.params
                    };
                    this.props.actionTicket.getListTicket(data);

                    global.props.hideLoader();
                    Toast.show({
                        type: 'success',
                        text1: translate('create_ticket_success'),
                        position: 'bottom'
                    });
                    setTimeout(() => {
                        Toast.hide();
                        this.props.actionTicket.getDataService({
                            id: this.props.route.params
                        });
                        this.setIsLoading(false);
                        this.props.navigation.goBack();
                    }, 1000);
                }
            }
        } catch (e) {
            this.setIsLoading(false);
            global.props.hideLoader();
            console.log(e);
            Toast.show({
                type: 'error',
                text1: translate(
                    e?.errorReason?.includes('leader')
                        ? 'not_author_create'
                        : 'create_ticket_fail'
                ),
                position: 'bottom'
            });
        }
    };
    render() {
        const { xworkData } = this.props;
        const { component } = xworkData;
        if (component === undefined) {
            return null;
        }
        const {
            WrapperContainerTicket
            // ModalAddFollowers,
        } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('create_ticket')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    const { params } = this.props.route;
                    if (params) {
                        this.props.navigation.navigate('TicketList', params);
                    } else {
                        this.props.navigation.goBack();
                    }
                }}
                colorTitle>
                <TouchableWithoutFeedback
                    onPress={() => {
                        Keyboard.dismiss();
                    }}>
                    <View
                        style={{
                            flex: 1,
                            marginHorizontal: Mixins.scale(16),
                            marginVertical: Mixins.scale(24)
                        }}>
                        <MyText
                            text={`${translate('ticket_name')}: `}
                            addSize={2}
                            typeFont="medium"
                            style={{
                                color: Colors.GRAYF10
                            }}
                        />
                        <View
                            style={{
                                borderColor: Colors.DARK_ORANGE_15,
                                borderRadius: Mixins.scale(16),
                                borderWidth: 1,
                                color: Colors.BLACK,
                                fontSize: 16,
                                fontWeight: '400',
                                height: Mixins.scale(56),
                                marginTop: Mixins.scale(8),
                                paddingHorizontal: Mixins.scale(8),
                                flexDirection: 'row',
                                width: '100%',
                                alignItems: 'center'
                            }}>
                            <TextInput
                                value={this.state.title}
                                onChangeText={(text) => {
                                    this.setState({
                                        title: text
                                    });
                                }}
                                style={{ flex: 9, fontSize: 16 }}
                                placeholder={`${translate(
                                    'enter_ticket_name'
                                )}...`}
                                placeholderTextColor={Colors.GRAY_PLACEHODER}
                            />
                            {this.state.title !== '' && (
                                <TouchableOpacity
                                    style={{
                                        flex: 1,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}
                                    onPress={() => {
                                        this.setState({
                                            title: ''
                                        });
                                    }}>
                                    <Image
                                        source={{ uri: 'ic_delete' }}
                                        style={{
                                            height: Mixins.scale(24),
                                            width: Mixins.scale(24)
                                        }}
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>
                </TouchableWithoutFeedback>
                <KeyboardAvoidingView>
                    <TouchableOpacity
                        disabled={
                            this.state.title === '' ||
                            this.state.isLoading === true
                        }
                        onPress={this.handleButtonSave}
                        style={{
                            alignItems: 'center',
                            borderRadius: 12,
                            height: Mixins.scale(56),
                            justifyContent: 'center',
                            marginVertical: Mixins.scale(10),
                            marginHorizontal: Mixins.scale(16),
                            backgroundColor:
                                this.state.title === ''
                                    ? Colors.GRAYF6
                                    : Colors.DARK_BLUE_60
                        }}>
                        <MyText
                            text={translate('create')}
                            addSize={3}
                            typeFont="semiBold"
                            style={{ color: Colors.WHITE }}
                        />
                    </TouchableOpacity>
                </KeyboardAvoidingView>
                <Toast position="bottom" config={toastConfig} />
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        listPriority: state.ticketReducer.listPriority,
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listTicket: state.ticketReducer.listTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateSchedule);
