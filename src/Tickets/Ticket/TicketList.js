import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { PureComponent } from 'react';
import {
    FlatList,
    Image,
    StyleSheet,
    TouchableOpacity,
    View,
    RefreshControl
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import * as _actionTicket from './action';
import Toast from 'react-native-toast-message';
import ModalFilterTicket from '../../modal/ModalFilterTicket';
import { RenderItemList } from './Components/ComponentItemTicket';
import { GlobalStore } from 'redux-micro-frontend';
const { translate } = global.props.getTranslateConfig();
class TicketList extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            groupID: 0,
            nameTitle: '',
            iDisplayLength: 10,
            isShowModalFilter: false,
            isFetchingData: false,
            isRefreshing: false,
            isLoadMore: false,
            dataFilter: null,
            isGroundTask: false,
            isShowModalResson: false,
            itemSelected: null,
            itemStatus: null
        };
        this.globalStore = GlobalStore.Get();
    }

    async componentDidMount() {
        this.setState({
            isFetchingData: true
        });

        this.init();
        this.initLocationGeo();
        this.props.navigation.addListener('focus', () => {
            this.componentWillFocus();
        });
        this.props.navigation.addListener('blur', () => {
            this.setState({
                isShowModalFilter: false
            });
        });
    }

    componentWillFocus() {
        if (
            !helper.IsValidateObject(this.props.route.params) &&
            !helper.IsValidateObject(this.props.dataSerivce)
        ) {
            return null;
        } else {
            this.handleRenderTitle();
        }
    }
    initLocationGeo = (txt) => {
        const data = {
            supportServiceId: -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: txt || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
    };

    handleRenderTitle = () => {
        const { dataSerivce } = this.props;
        const { params } = this.props.route;

        if (
            this.props.route.params &&
            (helper.hasProperty(params, 'supportServiceName') ||
                helper.hasProperty(params, 'name'))
        ) {
            this.setState({
                groupID: params?.supportServiceId || params?.id,
                nameTitle:
                    this.props.route.params?.supportServiceName ??
                    this.props.route.params?.name
            });
        } else if (helper.IsValidateObject(dataSerivce)) {
            this.setState(
                {
                    groupID: dataSerivce?.id,
                    nameTitle: dataSerivce.name
                },
                () => {
                    this.props.actionTicket.getDataService(null);
                }
            );
        }
    };

    init = async () => {
        this.setState({
            isFetchingData: true
        });
        const { params } = this.props.route;
        const paramsXticket = await AsyncStorage.getItem('PARAMS_XTICKET');
        if (helper.IsValidateObject(paramsXticket)) {
            const parseResult = JSON.parse(paramsXticket);
            const data = {
                supportServiceId: parseResult?.id
            };
            const dataMember = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 100,
                    search: ''
                },
                requestId: '',
                supportServiceId: parseResult?.id
            };
            this.setState({
                isGroundTask:
                    parseResult?.serviceType === 'GROUND_TASK' ? true : false,
                groupID: parseResult?.id,
                nameTitle: parseResult?.name
            });
            this.props.actionTicket.getApproveTypeList();
            await this.props.actionTicket.getListTicket(data);
            const { listTicket } = this.props;
            if (listTicket.isSuccess) {
                AsyncStorage.removeItem('PARAMS_XTICKET');
            }
            this.props.actionHome.getListMemberGroup(dataMember);
            this.props.actionTicket.getAllStatusTicket(parseResult?.id);
        } else {
            this.setState({
                isGroundTask:
                    params?.serviceType === 'GROUND_TASK' ? true : false
            });
            this.props.actionTicket.getApproveTypeList();

            const data = {
                supportServiceId: params?.supportServiceId || params?.id
            };
            const dataMember = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 100,
                    search: ''
                },
                requestId: '',
                supportServiceId: params?.supportServiceId || params?.id
            };
            this.handleRenderTitle();
            await this.props.actionTicket.getListTicket(
                { supportServiceId: params?.supportServiceId || params?.id },
                this.props.navigation
            );
            await this.props.actionHome.getListMemberGroup(dataMember);
            await this.props.actionTicket.getAllStatusTicket(
                params?.supportServiceId || params?.id
            );
        }
        this.setState({
            isFetchingData: this.props.listTicket.isFetching,
            isRefreshing: false
        });
    };

    retryData = async () => {
        const data = {
            supportServiceId: this.state.groupID
        };
        this.setState({ isRefreshing: true });
        if (helper.IsValidateObject(this.state.dataFilter)) {
            await this.handleFilter(this.state.dataFilter, 10);
        } else {
            await this.props.actionTicket.getListTicket(data);
        }
        setTimeout(async () => {
            this.setState({ isRefreshing: false });
        }, 2000);
    };

    async componentDidUpdate(prevProps) {
        const { xworkData } = this.props;
        if (xworkData?.constants !== prevProps.xworkData?.constants) {
            this.init();
        }
    }
    handleLoadMore = async () => {
        const {
            txtSearch,
            iDisplayLength,
            isLoadMore,
            isShowFetching,
            isRefresh
        } = this.state;
        const { listTicket } = this.props;
        const body = {
            iDisplayStart: 0,
            iDisplayLength: iDisplayLength + 10,
            search: txtSearch,
            supportServiceId: this.state.groupID
        };
        if (listTicket.data?.length < iDisplayLength) {
            return null;
        } else {
            this.setState({
                isLoadMore: true
            });
            if (helper.IsValidateObject(this.state.dataFilter)) {
                await this.handleFilter(
                    this.state.dataFilter,
                    iDisplayLength + 10
                );
            } else {
                await this.props.actionTicket.getListTicket(body);
            }
            this.setState({
                iDisplayLength: iDisplayLength + 10
            });
            setTimeout(() => {
                this.setState({
                    isLoadMore: false
                });
            }, 1000);
        }

        if (
            listTicket.isFetching ||
            isLoadMore ||
            isRefresh ||
            isShowFetching
        ) {
            return null;
        }
    };
    renderLoadingcontent = () => {
        if (this.state.isLoadMore) {
            return (
                <View
                    style={{
                        alignItems: 'center',
                        marginTop: 10
                    }}>
                    <MyText
                        style={{
                            color: Colors.GRAY_PLACEHODER
                        }}
                        text={translate('loading_more')}
                    />
                </View>
            );
        }
        return null;
    };
    handleFetchingData = () => {
        const body = {
            iDisplayStart: 0,
            iDisplayLength: 10,
            search: '',
            supportServiceId: this.state.groupID
        };
        this.props.actionTicket.getListTicket(body);
        this.setState({ dataFilter: null });
    };

    handleChangeStatusTicket = (item) => {
        const { listTicket } = this.props;
        let newData = [...listTicket.data];

        let index = this.props.listTicket.data?.findIndex(
            (element) => element.ticket.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            this.props.actionTicket.stop_get_list_ticket({
                data: newData
            });
        }
    };

    renderListTicket = () => {
        return (
            <FlatList
                showsVerticalScrollIndicator={false}
                data={this.props.listTicket?.data}
                style={{ flex: 1 }}
                renderItem={this.renderItem}
                extraData={this.state || this.props}
                refreshControl={
                    <RefreshControl
                        tintColor={'#9FC6FF'}
                        colors={['#9FC6FF']}
                        onRefresh={this.retryData}
                        refreshing={this.state.isRefreshing}
                    />
                }
                onEndReached={this.handleLoadMore}
                scrollEventThrottle={10}
                initialNumToRender={10}
                windowSize={11}
                ListFooterComponent={this.renderLoadingcontent()}
                onEndReachedThreshold={0.1}
            />
        );
    };
    handleNewFeed = (item) => {
        const { listNewFeed } = this.props;
        let newData = [...listNewFeed.data];

        let index = this.props.listNewFeed.data?.findIndex(
            (element) => element?.ticket?.id === item?.ticket?.id
        );
        if (index !== -1) {
            newData[index] = {
                ...item,
                ticket: {
                    ...newData[index].ticket,
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            this.props.actionHome.stop_get_new_feed({
                data: newData
            });
        }

        // }
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    updateStatusTicket = async (body) => {
        const response = await this.props.actionTicket.updateStatusTicket(body);
        try {
            if (
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object)
            ) {
                setTimeout(() => {
                    Toast.show({
                        type: 'success',
                        text1: translate('change_status_success'),
                        position: 'bottom'
                    });
                }, 500);
                this.handleChangeStatusTicket(response.object);
                this.handleNewFeed(response.object);
                this.handleGlobalState();
            } else {
                setTimeout(() => {
                    Toast.show({
                        type: 'error',
                        text1: response ?? translate('change_status_fail'),
                        position: 'bottom'
                    });
                }, 500);
            }
        } catch (error) {
            setTimeout(() => {
                Toast.show({
                    type: 'error',
                    text1: translate('change_status_fail'),
                    position: 'bottom'
                });
            }, 500);
        }
    };
    onChangeStatus = async (itemStatus, item) => {
        let body = {
            id: item?.ticket.id,
            statusId: itemStatus.id
        };
        if (itemStatus?.isReason) {
            return this.setState({
                itemSelected: item,
                itemStatus: itemStatus,
                isShowModalResson: true
            });
        }
        this.updateStatusTicket(body);
    };
    renderItem = ({ item }) => {
        const { common } = this.props.xworkData;
        const { typeListData } = this.props;

        return (
            <RenderItemList
                item={item}
                common={common}
                typeListData={typeListData}
                isGroundTask={this.state.isGroundTask}
                navigation={this.props.navigation}
                onChangeStatus={this.onChangeStatus}
            />
        );
    };
    handleFilterTicket = (data) => {
        this.handleFilter(data);
        this.setState({
            iDisplayLength: 10
        });
    };
    saveDataFilter = (data) => {
        this.setState({ dataFilter: data });
    };
    handleFilter = (data, lengthList = 10) => {
        console.log(
            'handleFilterhandleFilter',
            JSON.stringify(data, null, 2),
            JSON.stringify(this.props.listMemberGroup, null, 2)
        );
        let dataSelectedMemberCreate = [];
        let dataSelectedMember = [];
        let dataSelectedFollow = [];
        let arrayStoreId = [];
        if (data?.storeSelected?.length > 0) {
            data?.storeSelected?.map((store) => {
                arrayStoreId.push(store.id);
            });
        }
        this.props.listMemberGroup.data?.filter((item) => {
            data.selectedMemberCreated?.map((member) => {
                if (item?.user?.username === member.user.username) {
                    dataSelectedMemberCreate.push(item?.user.id);
                }
            });
            data.selectedMember?.map((member1) => {
                if (item?.user?.username === member1.user.username) {
                    dataSelectedMember.push(item?.user.id);
                }
            });
            data.selectedMemberFollower?.map((member2) => {
                if (item?.user?.username === member2.user.username) {
                    dataSelectedFollow.push(item?.user.id);
                }
            });
        });
        const body = {
            supportServiceId: this.state.groupID || -1,
            arrayIssueStatusId: helper.IsValidateObject(data?.selectedStatus)
                ? data?.selectedStatus
                : [],
            arrayCreateUserId: dataSelectedMemberCreate,
            arrayStoreId: arrayStoreId,
            arrayAssigneeUserId: dataSelectedMember,
            arrayWatcherId: dataSelectedFollow,
            asTemplate: false,
            timeFrom: Date.parse(data.startDate) || -1,
            timeTo: Date.parse(data.endDate) || -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: lengthList,
                search: data.textValue || ''
            },
            supportServiceName: data.supportServiceName,
            selectedFilterFromDay: data.selectedFilterFromDay,
            requestId: -1,
            requestAction: -1,
            lastId: -1
        };
        this.props.actionTicket.filterTicket(body);

        this.setState({ iDisplayLength: lengthList });
    };
    onPressSendReason = async (text) => {
        const { itemSelected, itemStatus } = this.state;
        let body = {
            id: itemSelected?.ticket.id,
            statusId: itemStatus.id,
            reasonStatus: text
        };

        this.updateStatusTicket(body);
    };
    render() {
        const { xworkData, listTicket } = this.props;
        const { component, screenName } = xworkData;
        const { isShowModalFilter } = this.state;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket, ModalReason } = component;
        const { params } = this.props.route;

        return (
            <View style={style.container}>
                <WrapperContainerTicket
                    navigation={this.props.navigation}
                    nameTitle={this.state.nameTitle}
                    centerAlign={false}
                    colorBackButton={Colors.DARK_BLUE_50}
                    onPressBack={() => {
                        if (this.props.listTicket.isFetching) {
                            return null;
                        }
                        if (screenName === 'CreateTicket') {
                            this.props.navigation.navigate('Xticket');
                        } else {
                            this.props.navigation.goBack();
                        }
                    }}
                    buttonRightIn={() => {
                        this.setState({
                            isShowModalFilter: true
                        });
                    }}
                    imgButtonIn="ic_filter"
                    isLoading={
                        this.props.listTicket.isFetching &&
                        !this.state.isRefreshing &&
                        !this.state.isLoadMore
                    }
                    colorTitle
                    actionRetry={this.init}
                    messageEmpty={translate('no_job_ticket')}
                    isEmpty={listTicket?.data?.length === 0}
                    isSuccess={!listTicket?.isError}
                    messageLoading={translate('getting_list_job_ticket')}
                    messageError={translate('something_wrong_server')}
                    isError={listTicket?.isError}>
                    <View
                        style={{
                            flex: 1,
                            paddingHorizontal: 16
                        }}>
                        {this.renderListTicket()}
                    </View>
                </WrapperContainerTicket>

                <TouchableOpacity
                    onPress={() => {
                        this.props.navigation.navigate(
                            this.state.isGroundTask
                                ? 'CreateSchedule'
                                : 'CreateTicket',
                            this.state.groupID
                        );
                    }}
                    style={style.btnBottom}>
                    <Image style={style.imgPlus} source={{ uri: 'ic_plus' }} />
                </TouchableOpacity>
                {isShowModalFilter && (
                    <ModalFilterTicket
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalFilter: false
                            });
                        }}
                        xworkData={this.props.xworkData}
                        dataFilter={this.state.dataFilter}
                        saveDataFilter={this.saveDataFilter}
                        handleRefreshData={this.handleFetchingData}
                        isVisible={isShowModalFilter}
                        listStatusTicket={this.props.listAllStatusTicket.data}
                        listMember={this.props.listMemberGroup.data}
                        handleFilter={this.handleFilterTicket}
                    />
                )}
                {this.state.isShowModalResson && (
                    <ModalReason
                        isVisible={this.state.isShowModalResson}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalResson: false
                            });
                        }}
                        onPressSendReason={this.onPressSendReason}
                    />
                )}
            </View>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listTicket: state.ticketReducer.listTicket,
        listAllStatusTicket: state.ticketReducer.listAllStatusTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        typeListData: state.ticketReducer.typeListData,
        dataSerivce: state.ticketReducer.dataSerivce,
        listNewFeed: state.groupTicketReducer.listNewFeed
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(TicketList);

const style = StyleSheet.create({
    btnBottom: {
        alignItems: 'center',
        backgroundColor: Colors.DARK_BLUE_60,
        borderRadius: Mixins.scale(28),
        bottom: Mixins.scale(80),
        height: Mixins.scale(56),
        justifyContent: 'center',
        position: 'absolute',
        right: Mixins.scale(16),
        width: Mixins.scale(56)
    },
    container: {
        flex: 1
    },
    imgPlus: {
        height: Mixins.scale(30),
        tintColor: Colors.WHITE,
        width: Mixins.scale(30)
    }
});
