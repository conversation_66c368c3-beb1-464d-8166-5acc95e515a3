import * as _action from './action';
import * as _state from '../state';

const ticketReducer = function (state = _state.ticketState, action) {
    switch (action.type) {
        case _action.ticketAction.START_GET_LIST_TICKET:
            return {
                ...state,
                listTicket: {
                    ...state.listTicket,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.ticketAction.STOP_GET_LIST_TICKET:
            return {
                ...state,
                listTicket: {
                    ...state.listTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_FILTER_TICKET:
            return {
                ...state,
                listTicket: {
                    ...state.listTicket,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.ticketAction.STOP_FILTER_TICKET:
            return {
                ...state,
                listTicket: {
                    ...state.listTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_DETAIL_TICKET:
            return {
                ...state,
                detailTicket: {
                    ...state.detailTicket,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.ticketAction.STOP_GET_DETAIL_TICKET:
            return {
                ...state,
                detailTicket: {
                    ...state.detailTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                },
                approveData: {
                    ...state.approveData,
                    isSuccess: false,
                    isError: false
                }
            };
        case _action.ticketAction.START_GET_LIST_LOCATIONGEO:
            return {
                ...state,
                listLocationGeo: {
                    ...state.listLocationGeo,
                    isFetching: true,
                    isSuccess: false,
                    isError: false,
                    msgError: ''
                }
            };
        case _action.ticketAction.STOP_GET_LIST_LOCATIONGEO:
            return {
                ...state,
                listLocationGeo: {
                    ...state.listLocationGeo,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_LIST_COMMENT:
            return {
                ...state,
                listComment: {
                    ...state.listComment,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_COMMENT:
            return {
                ...state,
                listComment: {
                    ...state.listComment,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.STOP_GET_SUGGEST_COMMENT:
            return {
                ...state,
                suggestComment: {
                    ...state.suggestComment,
                    data: action.payload.data,
                    isSuccess: action.payload.isSuccess,
                    isError: !action.payload.isSuccess,
                    isFetching: false
                }
            };
        case _action.ticketAction.GET_SUGGEST_COMMENT:
            return {
                ...state,
                suggestComment: {
                    ...state.suggestComment,
                    data: [],
                    isSuccess: false,
                    isError: false,
                    isFetching: true
                }
            };
        case _action.ticketAction.START_GET_STREAM_TOKEN:
            return {
                ...state,
                streamToken: {
                    ...state.streamToken,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_STREAM_TOKEN:
            return {
                ...state,
                streamToken: {
                    ...state.streamToken,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                }
            };
        case _action.ticketAction.START_GET_LIST_PRIORITY:
            return {
                ...state,
                listPriority: {
                    ...state.listPriority,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_PRIORITY:
            return {
                ...state,
                listPriority: {
                    ...state.listPriority,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_LIST_FILE:
            return {
                ...state,
                listFileTicket: {
                    ...state.listFileTicket,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_FILE:
            return {
                ...state,
                listFileTicket: {
                    ...state.listFileTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                }
            };
        case _action.ticketAction.START_GET_LIST_ACTIVITY:
            return {
                ...state,
                listActivity: {
                    ...state.listActivity,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_ACTIVITY:
            return {
                ...state,
                listActivity: {
                    ...state.listActivity,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_SLA:
            return {
                ...state,
                slaTicketDetail: {
                    ...state.slaTicketDetail,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_SLA:
            return {
                ...state,
                slaTicketDetail: {
                    ...state.slaTicketDetail,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: action.data
                }
            };
        case _action.ticketAction.START_GET_ALL_STATUS_TICKET:
            return {
                ...state,
                listAllStatusTicket: {
                    ...state.listAllStatusTicket,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_ALL_STATUS_TICKET:
            return {
                ...state,
                listAllStatusTicket: {
                    ...state.listAllStatusTicket,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_LIST_TASK:
            return {
                ...state,
                listTask: {
                    ...state.listTask,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_TASK:
            return {
                ...state,
                listTask: {
                    ...state.listTask,
                    percentDone: action.percentDone,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.START_GET_LIST_TEMPLATE_TASK:
            return {
                ...state,
                listTemplateTask: {
                    ...state.listTemplateTask,
                    isFetching: true
                }
            };
        case _action.ticketAction.STOP_GET_LIST_TEMPLATE_TASK:
            return {
                ...state,
                listTemplateTask: {
                    ...state.listTemplateTask,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.APPROVE_SUCCESS:
            return {
                ...state,
                approveData: {
                    ...state.approveData,
                    isSuccess: true
                }
            };
        case _action.ticketAction.APPROVE_FAIL:
            return {
                ...state,
                approveData: {
                    ...state.approveData,
                    isError: true
                }
            };
        case 'DATA_SERVICE':
            return {
                ...state,
                dataSerivce: action.data
            };
        case 'RESET_PROPS':
            return {
                ...state,
                approveData: {
                    ...state.approveData,
                    isError: false,
                    isSuccess: false
                }
            };
        case _action.ticketAction.GET_TYPE_LIST_SUCCESS:
            return {
                ...state,
                typeListData: {
                    ...state.typeListData,
                    data: action.data
                }
            };
        case _action.ticketAction.GET_TYPE_LIST_FAIL:
            return {
                ...state,
                typeListData: {
                    ...state.typeListData,
                    msgError: action.msgError
                }
            };
        case _action.ticketAction.START_GET_HASHTAG:
            return {
                ...state,
                listHashtag: {
                    ...state.listHashtag,
                    isFetching: true,
                    msgError: ''
                }
            };
        case _action.ticketAction.STOP_GET_HASHTAG:
            return {
                ...state,
                listHashtag: {
                    ...state.listHashtag,
                    isFetching: action.isFetching,
                    isSuccess: action.isSuccess,
                    isError: !action.isSuccess,
                    msgError: action.msgError ?? '',
                    data: [...action.data]
                }
            };
        case _action.ticketAction.GET_RATING_INFO:
            return {
                ...state,
                ratingInfo: action.data
            };
        default:
            return state;
    }
};

export { ticketReducer };
