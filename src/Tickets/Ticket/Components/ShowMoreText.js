import React from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
const { translate } = global.props.getTranslateConfig();

export default class ShowMoreText extends React.PureComponent {
    static defaultProps = {
        maxLine: 4,
        lineHeight: 14,
        style: null,
        onPress: () => {},
        content: ''
    };

    constructor(props) {
        super(props);

        this.state = {
            showMore: false,
            maxLine: props.maxLine,
            isShowButtuon: false,
            textContent: ''
        };
        this.maxHeight = props.maxLine * props.lineHeight + 0.1;
    }

    onPressShowMore = () => {
        const { showMore } = this.state;
        const { onPress } = this.props;
        onPress();
        this.setState({
            showMore: !showMore,
            maxLine: !showMore ? this.props.maxLine : 0
        });
    };
    componentDidUpdate(prevProps) {
        const { maxLine } = this.state;

        if (this.props.content !== prevProps.content) {
            if (maxLine > 0 && this.state.showMore) {
                this.setState({ showMore: false, isShowButtuon: false });
            }
        }
    }
    financial = (x) => {
        return parseInt(x);
    };

    onLayout = (event) => {
        const { height } = event.nativeEvent.layout;

        const { maxLine } = this.state;

        if (
            maxLine > 0 &&
            this.financial(height) > this.financial(this.maxHeight)
        ) {
            this.setState({ showMore: true, isShowButtuon: true, height });
        }
    };

    render() {
        const { content, maxLine } = this.props;
        const { showMore } = this.state;

        return (
            <View style={this.props.style}>
                <MyText
                    style={[styles.text, this.props.styleText]}
                    numberOfLines={showMore ? maxLine : 0}
                    ellipsizeMode="tail"
                    onLayout={(event) => this.onLayout(event)}
                    isParseText={true}
                    text={content}
                />
                {this.state.isShowButtuon && (
                    <TouchableOpacity
                        style={styles.buttonShowMore}
                        hitSlop={{ bottom: 5 }}
                        onPress={this.onPressShowMore}>
                        <MyText
                            style={styles.textShowMore}
                            onPress={this.onPressShowMore}
                            text={
                                showMore
                                    ? translate('more')
                                    : translate('collapse')
                            }
                        />
                    </TouchableOpacity>
                )}
            </View>
        );
    }
}

const styles = StyleSheet.create({
    buttonShowMore: {
        alignSelf: 'flex-end'
    },
    text: {
        flex: 1
    },
    textShowMore: {
        color: Colors.DARK_BLUE_60,
        fontSize: 12,
        marginTop: 4
    }
});
