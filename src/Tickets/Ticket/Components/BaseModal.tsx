import { Modal, StyleSheet, ModalProps, Animated } from 'react-native';
import React, { useEffect, useRef } from 'react';

interface Props extends ModalProps {
    children?: React.ReactNode;
    isVisible: boolean;
    onClose?: () => void;
    isBackdrop?: boolean;
    animation?: 'fade' | 'slide' | 'none';
}

const BaseModal = ({
    children,
    isVisible,
    onClose,
    isBackdrop = true,
    animation = 'fade',
    ...props
}: Props) => {
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const scaleAnim = useRef(new Animated.Value(0.3)).current;

    useEffect(() => {
        if (isVisible) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true
                }),
                Animated.spring(scaleAnim, {
                    toValue: 1,
                    tension: 65,
                    friction: 7,
                    useNativeDriver: true
                })
            ]).start();
        } else {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 200,
                    useNativeDriver: true
                }),
                Animated.spring(scaleAnim, {
                    toValue: 0.3,
                    tension: 65,
                    friction: 7,
                    useNativeDriver: true
                })
            ]).start();
        }
    }, [isVisible]);

    const handleClose = () => {
        onClose?.();
    };

    const handleOverlayPress = () => {
        handleClose();
    };

    return (
        <Modal
            visible={isVisible}
            transparent
            animationType="none"
            onRequestClose={handleClose}
            {...props}>
            <Animated.View
                style={[
                    styles.overlay,
                    {
                        opacity: fadeAnim
                    }
                ]}
                onTouchEnd={isBackdrop ? handleOverlayPress : undefined}>
                <Animated.View
                    style={{
                        transform: [{ scale: scaleAnim }]
                    }}
                    onTouchEnd={(e) => e.stopPropagation()}>
                    {children}
                </Animated.View>
            </Animated.View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export default BaseModal;
