import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText, DropdownComponent } from '@mwg-kits/components';
import { stylesTicket } from '../stylesTicket';
import { Image, Platform, TouchableOpacity, View } from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { CONST_API } from '../../../constant';
import { defaultTextPriority } from '../../../utility';
const { translate } = global.props.getTranslateConfig();

const RenderPriority = (props) => {
    return (
        <View
            style={{
                marginTop: Mixins.scale(32)
            }}>
            <MyText
                text={translate('priority')}
                addSize={1}
                style={{
                    color: Colors.BLACK_HEADER_TITLE
                }}
                typeFont="medium"
            />
            <DropdownComponent
                selectedTextProps={{
                    numberOfLines: 1
                }}
                iconStyle={stylesTicket.iconStyle}
                selectedTextStyle={stylesTicket.txtLabel}
                showsVerticalScrollIndicator={false}
                placeholder={props.placeholder}
                data={props.listPriority}
                search={false}
                keyboardAvoiding={false}
                isKeyboardShow={false}
                style={[
                    stylesTicket.btnDropdownPriority,
                    {
                        backgroundColor: defaultTextPriority(
                            props.prioritySelected?.priority
                        )
                    }
                ]}
                onChange={props.onChangeComponent}
                value={props.prioritySelected}
                dropdownPosition={'top'}
                placeholderStyle={stylesTicket.txtLabel}
                minHeight={Mixins.scale(120)}
                itemContainerStyle={{
                    justifyContent: 'center'
                }}
                itemTextStyle={{
                    fontSize: 14,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    backgroundColor: Colors.WHITE,
                    borderRadius: Mixins.scale(12)
                }}
                labelField="name"
                valueField="id"
            />
        </View>
    );
};
const RenderUserAssginee = (props) => {
    return (
        <View style={{ marginTop: Mixins.scale(28) }}>
            <View style={stylesTicket.viewUserAssignee}>
                <MyText
                    text={translate('person_in_charge')}
                    addSize={1}
                    style={{
                        color: Colors.BLACK_HEADER_TITLE
                    }}
                    typeFont="medium"
                />
                <DropdownComponent
                    data={[
                        { name: translate('receiver'), id: 0 },
                        { name: translate('watcher'), id: 1 }
                    ]}
                    activeColor={Colors.WHITE}
                    showsVerticalScrollIndicator={false}
                    itemTextStyle={{
                        fontSize: 13,
                        color: Colors.BLACK
                    }}
                    maxHeight={Mixins.scale(140)}
                    itemContainerStyle={{
                        justifyContent: 'center'
                    }}
                    renderItem={(item1) => {
                        return (
                            <View style={stylesTicket.viewItemDropdown}>
                                <MyText
                                    numberOfLines={1}
                                    style={{ color: Colors.BLACK }}
                                    text={item1.name}
                                />
                            </View>
                        );
                    }}
                    containerStyle={{
                        backgroundColor: Colors.WHITE,
                        borderRadius: Mixins.scale(12)
                    }}
                    style={stylesTicket.styleDropdownComponent}
                    placeholder=""
                    renderRightIcon={() => {
                        return (
                            <View
                                style={{
                                    justifyContent: 'flex-end'
                                }}>
                                <Image
                                    style={stylesTicket.imgPlus}
                                    resizeMode="contain"
                                    source={{ uri: 'ic_plus_square' }}
                                />
                            </View>
                        );
                    }}
                    valueField="id"
                    onChange={props.handleShowModal}
                />
            </View>
            {helper.IsValidateObject(props.userReceiver) && (
                <View style={stylesTicket.viewRowTop_16}>
                    <Image
                        style={stylesTicket.icLeftTicket}
                        source={{ uri: 'ic_profile' }}
                    />
                    <MyText
                        text={translate('receiver')}
                        addSize={1}
                        style={{
                            color: Colors.GRAYF8,
                            marginHorizontal: Mixins.scale(8)
                        }}
                    />
                    {helper.IsValidateObject(props.userReceiver) && (
                        <Image
                            style={stylesTicket.imgUserReceiver}
                            source={{
                                uri: `${CONST_API.baseAvatarURI}${props.userReceiver?.user?.profile?.image}`
                            }}
                        />
                    )}
                </View>
            )}
            {!helper.IsEmptyArray(props.groupUserFollowers) && (
                <View
                    style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        marginTop: Mixins.scale(20)
                    }}>
                    <Image
                        style={stylesTicket.icLeftTicket}
                        source={{ uri: 'ic_add_group' }}
                    />
                    <MyText
                        text={translate('watcher')}
                        addSize={1}
                        style={{
                            color: Colors.GRAYF8,
                            marginHorizontal: Mixins.scale(8),
                            marginRight: Mixins.scale(12)
                        }}
                    />
                    <View style={stylesTicket.viewRowAlignItems}>
                        {!helper.IsEmptyArray(props.groupUserFollowers) &&
                            props.groupUserFollowers?.map((item, index) => {
                                if (index < 3) {
                                    return (
                                        <Image
                                            key={index.toString}
                                            style={{
                                                zIndex: index,
                                                position: 'absolute',
                                                left:
                                                    index === 0
                                                        ? 0
                                                        : Mixins.scale(
                                                              20 * index
                                                          ),
                                                height: Mixins.scale(28),
                                                width: Mixins.scale(28),
                                                borderRadius: Mixins.scale(14)
                                            }}
                                            source={{
                                                uri: helper.IsValidateObject(
                                                    item.user
                                                )
                                                    ? `${CONST_API.baseAvatarURI}${item?.user?.profile?.image}`
                                                    : `${CONST_API.baseAvatarURI}${item?.userImage}`
                                            }}
                                        />
                                    );
                                }
                            })}
                        {props.groupUserFollowers?.length > 3 && (
                            <View
                                style={{
                                    zIndex: 100,
                                    position: 'absolute',
                                    height: Mixins.scale(28),
                                    width: Mixins.scale(28),
                                    borderRadius: Mixins.scale(14),
                                    left: Mixins.scale(20 * 3),
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: Colors.GRAYF6
                                }}>
                                <MyText
                                    text={`+${
                                        parseInt(
                                            props.groupUserFollowers?.length
                                        ) - 3
                                    }`}
                                    addSize={-2}
                                    style={{
                                        color: Colors.WHITE
                                    }}
                                />
                            </View>
                        )}
                    </View>
                </View>
            )}
        </View>
    );
};
const RenderLocationGeo = (props) => {
    return (
        <View style={{ marginTop: Mixins.scale(32) }}>
            <View style={stylesTicket.viewRowAlignItems}>
                <Image
                    style={stylesTicket.imgLocationTicket}
                    source={{ uri: 'ic_location_ticket' }}
                />
                <MyText
                    text={translate('location')}
                    addSize={1}
                    style={{
                        color: Colors.BLACK_HEADER_TITLE,
                        marginHorizontal: Mixins.scale(8)
                    }}
                    typeFont="medium"
                />
            </View>
            <TouchableOpacity
                onPress={props.onPressShowModal}
                style={stylesTicket.btnLocation}>
                <MyText
                    numberOfLines={1}
                    text={props.locationSelected}
                    addSize={1}
                    style={{
                        color: Colors.BLACK
                    }}
                />
                <AntDesign name="down" size={16} color={Colors.BLACK} />
            </TouchableOpacity>
        </View>
    );
};

const RenderStatus = (props) => {
    return (
        <View style={{ marginTop: Mixins.scale(32) }}>
            <MyText
                text={translate('status')}
                addSize={1}
                style={{
                    color: Colors.BLACK_HEADER_TITLE
                }}
                typeFont="medium"
            />
            <DropdownComponent
                selectedTextProps={{
                    numberOfLines: 1
                }}
                dropdownPosition={'top'}
                iconStyle={stylesTicket.iconStyle}
                selectedTextStyle={stylesTicket.txtLabel}
                showsVerticalScrollIndicator={false}
                placeholder={props.ticket.statusName}
                data={props.allowedStatus}
                search={false}
                keyboardAvoiding={false}
                isKeyboardShow={false}
                style={[
                    stylesTicket.btnDropdownPriority,
                    {
                        backgroundColor: helper.IsValidateObject(
                            props.statusSelected
                        )
                            ? props.statusSelected.color
                            : props.ticket.statusColor
                    }
                ]}
                onChange={(item) => {
                    props.onChangeItem(item);
                }}
                placeholderStyle={stylesTicket.txtLabel}
                maxHeight={Mixins.scale(140)}
                itemContainerStyle={{
                    justifyContent: 'center'
                }}
                value={props.statusSelected}
                itemTextStyle={{
                    fontSize: 14,
                    color: Colors.BLACK
                }}
                containerStyle={{
                    marginBottom:
                        Platform.OS === 'android' ? Mixins.scale(-40) : 0,
                    backgroundColor: Colors.WHITE,
                    borderRadius: Mixins.scale(12)
                }}
                labelField="name"
                valueField="id"
            />
        </View>
    );
};
export { RenderPriority, RenderUserAssginee, RenderLocationGeo, RenderStatus };
