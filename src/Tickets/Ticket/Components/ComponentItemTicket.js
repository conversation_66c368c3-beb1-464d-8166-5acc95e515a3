import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import { stylesTicket } from '../stylesTicket';
import {
    Image,
    Platform,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';
import { CONST_API } from '../../../constant';
import { DropdownComponent } from '@mwg-kits/components';
import { TooltipCopyPaste } from '../../GroupTicket/CompoentTooltip';
import { openSetting, requestPermission } from '@mwg-kits/core';
import ImagePicker from 'react-native-image-crop-picker';
import DocumentPicker, { types } from 'react-native-document-picker';
import { defaultTextPriority } from '../../../utility';

const { translate } = global.props.getTranslateConfig();

const RenderTagTicket = (props) => {
    const { item, onChangeStatus } = props;
    let checkQuickTicket =
        (helper.hasProperty(item?.ticket, 'asSocial') &&
            item?.ticket?.asSocial) ||
        (helper.hasProperty(item, 'ticketType') &&
            item?.ticketType === 'social');
    return (
        <View style={stylesTicket.styleViewIconItem}>
            <Image style={stylesTicket.viewImg} source={{ uri: 'tag' }} />
            {helper.hasProperty(item.ticket, 'statusName') &&
                helper.IsValidateObject(item?.ticket.statusName) && (
                    <DropdownComponent
                        selectedTextProps={{
                            numberOfLines: 1
                        }}
                        selectedTextStyle={{
                            color: Colors.WHITE,
                            fontSize: 13,
                            textAlign: 'center'
                        }}
                        disable={
                            !helper.hasProperty(item, 'allowedStatus') ||
                            item?.allowedStatus?.length === 0
                        }
                        minHeight={50}
                        showsVerticalScrollIndicator={false}
                        placeholder={item?.ticket?.statusName}
                        data={item?.allowedStatus || []}
                        search={false}
                        keyboardAvoiding={false}
                        isKeyboardShow={false}
                        style={{
                            backgroundColor: item.ticket.statusColor,
                            paddingHorizontal: Mixins.scale(8),
                            marginLeft: Mixins.scale(4),
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: Mixins.scale(150),
                            height: Mixins.scale(30),
                            borderRadius: Mixins.scale(12)
                        }}
                        onChangeStatus={(itemStatus) => {
                            onChangeStatus(itemStatus, item);
                        }}
                        renderRightIcon={() => {
                            return <View></View>;
                        }}
                        placeholderStyle={{
                            color: Colors.WHITE,
                            fontSize: 13,
                            textAlign: 'center'
                        }}
                        dropdownPosition={'top'}
                        maxHeight={Mixins.scale(150)}
                        itemContainerStyle={{
                            justifyContent: 'center'
                        }}
                        renderItem={(item1) => {
                            return (
                                <View
                                    style={{
                                        height: Mixins.scale(40),
                                        paddingHorizontal: Mixins.scale(8),
                                        justifyContent: 'center'
                                    }}>
                                    <MyText
                                        numberOfLines={1}
                                        style={{ color: Colors.BLACK }}
                                        addSize={-1}
                                        text={item1.name}
                                    />
                                </View>
                            );
                        }}
                        itemTextStyle={{
                            fontSize: 14,
                            color: Colors.BLACK
                        }}
                        containerStyle={{
                            paddingVertical: Mixins.scale(4),
                            marginBottom:
                                Platform.OS === 'android'
                                    ? Mixins.scale(-40)
                                    : 0,
                            backgroundColor: Colors.WHITE,
                            borderRadius: Mixins.scale(12)
                        }}
                        statusBarIsTranslucent={true}
                        value={item?.ticket?.statusName}
                        labelField="name"
                        valueField="id"
                    />
                )}
            {helper.hasProperty(item?.ticket, 'priorityName') &&
                item?.ticket?.supportServiceType !== 'GROUND_TASK' && (
                    <View
                        style={{
                            backgroundColor: defaultTextPriority(
                                item.ticket.priorityId
                            ),
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                            borderRadius: 12,
                            marginLeft: 8
                        }}>
                        <MyText
                            numberOfLines={1}
                            text={item?.ticket?.priorityName}
                            addSize={-1}
                            style={{
                                color: Colors.WHITE
                            }}
                        />
                    </View>
                )}
            {helper.IsValidateObject(item?.ticket?.issueStatus) &&
                helper.hasProperty(
                    item?.ticket?.issueStatus,
                    'issueStatusName'
                ) && (
                    <View
                        style={{
                            backgroundColor:
                                item.ticket.issueStatus?.issueStatusColor,
                            paddingHorizontal: 12,
                            paddingVertical: 6,
                            borderRadius: 12,
                            marginLeft: 8,
                            width: checkQuickTicket
                                ? Mixins.scale(150)
                                : 'auto',
                            alignItems: 'center'
                        }}>
                        <MyText
                            numberOfLines={1}
                            text={item.ticket.issueStatus?.issueStatusName}
                            addSize={-1}
                            style={{
                                color: Colors.WHITE
                            }}
                        />
                    </View>
                )}
        </View>
    );
};
const RenderBottomItemTicket = (props) => {
    const { item, typeListData, navigation, isGroundTask } = props;
    let checkType =
        helper.hasProperty(item.ticket, 'supportServiceType') &&
        typeListData.data.includes(item?.ticket?.supportServiceType);
    if (isGroundTask) {
        return null;
    }
    return (
        <View style={stylesTicket.viewRowBetween_16}>
            <View style={{ flex: 1 }}>
                {!checkType && (
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            flex: 1
                        }}>
                        <TouchableOpacity
                            style={{ padding: 3 }}
                            hitSlop={{
                                left: 2,
                                right: 2,
                                top: 2,
                                bottom: 2
                            }}
                            onPress={() =>
                                navigation.navigate('DetailTicket', {
                                    id: item?.ticket?.id,
                                    indexHeader: 1
                                })
                            }>
                            <Image
                                style={stylesTicket.imgIcChat}
                                source={{ uri: 'ic_chat' }}
                            />
                        </TouchableOpacity>
                        <View style={{ height: 10, width: 4 }} />
                        <TouchableOpacity
                            hitSlop={{
                                left: 2,
                                right: 2,
                                top: 2,
                                bottom: 2
                            }}
                            style={{ padding: 5 }}
                            onPress={() =>
                                navigation.navigate('DetailTicket', {
                                    id: item?.ticket?.id,
                                    indexHeader: 2
                                })
                            }>
                            <Image
                                style={stylesTicket.imgIcNote}
                                resizeMode="contain"
                                source={{ uri: 'ic_note' }}
                            />
                        </TouchableOpacity>
                    </View>
                )}
            </View>

            {helper.hasProperty(item?.ticket, 'assigneeImage') &&
                helper.IsValidateObject(item?.ticket?.assigneeImage) && (
                    <Image
                        style={stylesTicket.assigneeImage}
                        source={{
                            uri: `${CONST_API.baseAvatarURI}${item?.ticket?.assigneeImage}`
                        }}
                    />
                )}
        </View>
    );
};
const renderMentionContent = (content) => {
    let contentMention = content;
    let indexContent = contentMention.indexOf('@mentionuser');
    if (indexContent !== -1) {
        contentMention = content?.slice(0, indexContent);
        return contentMention;
    }
    return content;
};

const RenderItemList = (props) => {
    const {
        item,
        typeListData,
        isGroundTask,
        common,
        navigation,
        onChangeStatus
    } = props;
    const onPressTicket = (indexHeader = 0) => {
        navigation.navigate('DetailTicket', {
            id: item.ticket.id,
            indexHeader: indexHeader
        });
    };

    let checkQuickTicket =
        (helper.hasProperty(item.ticket, 'asSocial') && item.ticket.asSocial) ||
        (helper.hasProperty(item, 'ticketType') &&
            item.ticketType === 'social');
    let contentHashtag = '';
    contentHashtag = item?.ticket?.content?.replace(/#hashtag:{(.*?)}/g, ' ');
    let contentItem = checkQuickTicket
        ? renderMentionContent(contentHashtag)
        : item?.ticket?.content;
    return (
        <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
                onPressTicket(0);
            }}
            style={style.styleItem}>
            <View style={{ flex: 1, width: '100%', flexDirection: 'row' }}>
                <TooltipCopyPaste
                    onPressPressable={() => {
                        onPressTicket(0);
                    }}
                    dataCopy={item?.ticket?.subject}
                    children={() => {
                        return (
                            <MyText
                                numberOfLines={1}
                                typeFont="bold"
                                text={item?.ticket?.subject}
                                addSize={2}
                                style={{
                                    color: Colors.BLACK
                                }}
                            />
                        );
                    }}
                />
            </View>
            <View style={{ flex: 1, flexDirection: 'row', marginTop: 8 }}>
                <TooltipCopyPaste
                    onPressPressable={() => {
                        onPressTicket(0);
                    }}
                    dataCopy={contentItem.trim()}
                    children={() => {
                        return (
                            <MyText
                                numberOfLines={1}
                                text={contentItem.trim()}
                                style={{
                                    color: Colors.GRAYF9
                                }}
                            />
                        );
                    }}
                />
            </View>
            {!isGroundTask && (
                <View style={style.styleViewIconItem}>
                    <Image style={style.viewImg} source={{ uri: 'ic_users' }} />
                    <TooltipCopyPaste
                        onPressPressable={() => {
                            onPressTicket(0);
                        }}
                        dataCopy={item?.ticket?.supportServiceName}
                        children={() => {
                            return (
                                <MyText
                                    numberOfLines={1}
                                    text={item?.ticket?.supportServiceName}
                                    addSize={-1}
                                    style={{
                                        marginLeft: 8,
                                        color: Colors.BLACK
                                    }}
                                />
                            );
                        }}
                    />
                </View>
            )}
            <View style={style.styleViewIconItem}>
                <Image style={style.viewImg} source={{ uri: 'calendar' }} />
                <MyText
                    numberOfLines={1}
                    text={`${common.helper.getFullDate(
                        item?.ticket?.startDateLong
                    )} - ${common.helper.getFullDate(
                        item?.ticket?.dueDateLong
                    )}`}
                    addSize={-1}
                    style={{
                        marginLeft: 8,
                        color: Colors.BLACK
                    }}
                />
            </View>

            <View style={style.styleViewIconItem}>
                <Image style={style.viewImg} source={{ uri: 'ic_user_blue' }} />
                <TooltipCopyPaste
                    onPressPressable={() => {
                        onPressTicket(0);
                    }}
                    dataCopy={
                        item?.ticket?.creatorLastName +
                        ' ' +
                        item?.ticket?.creatorFirstName
                    }
                    children={() => {
                        let fullName = `${item?.ticket?.creatorUserName} - ${item?.ticket?.creatorLastName} ${item?.ticket?.creatorFirstName}`;
                        return (
                            <MyText
                                numberOfLines={1}
                                text={fullName}
                                addSize={-1}
                                style={{
                                    marginHorizontal: 8,
                                    color: Colors.BLACK
                                }}
                            />
                        );
                    }}
                />
                <Image
                    style={{
                        borderRadius: Mixins.scale(12),
                        height: Mixins.scale(24),
                        width: Mixins.scale(24)
                    }}
                    source={{
                        uri: `${CONST_API.baseAvatarURI}${item?.ticket?.creatorImage}`
                    }}
                />
            </View>
            <RenderTagTicket item={item} onChangeStatus={onChangeStatus} />
            <RenderBottomItemTicket
                item={item}
                typeListData={typeListData}
                isGroundTask={isGroundTask}
                navigation={navigation}
            />
        </TouchableOpacity>
    );
};
const UploadFileOrPhoto = async (item) => {
    const upLoadFile = async () => {
        try {
            const response = await DocumentPicker.pick({
                allowMultiSelection: true,
                presentationStyle: 'fullScreen',
                type: [
                    types.pdf,
                    types.doc,
                    types.xls,
                    types.pptx,
                    types.ppt,
                    types.docx,
                    types.xlsx
                ]
            });
            return response;
        } catch (e) {
            console.log(e);
        }
    };
    const onShowPicker = async () => {
        try {
            const images = await ImagePicker.openPicker({
                maxFiles: 5,
                multiple: true,
                waitAnimationEnd: false,
                forceJpg: true,
                compressImageMaxWidth: 1000,
                compressImageMaxHeight: 1000,
                maximumVideoDuration: 60000,
                compressVideoPreset: 'HighestQuality'
            });
            return images;
        } catch (e) {
            console.log(e, '12312312321');
        }
    };

    try {
        await requestPermission('photo');
        if (item === 0) {
            const response = await onShowPicker();
            return response;
        } else {
            const response = await upLoadFile();
            return response;
        }
    } catch (e) {
        return global.props.alert({
            show: true,
            title: translate('notify'),
            message: translate('access_library'),
            confirmText: translate('close'),
            cancelText: translate('setting'),

            onConfirmPressed: () => {
                global.props.alert({
                    show: false
                });
            },
            onCancelPressed: () => {
                global.props.alert({
                    show: false
                });
                openSetting();
            }
        });
    }
};
export {
    RenderTagTicket,
    RenderBottomItemTicket,
    RenderItemList,
    UploadFileOrPhoto
};
const style = StyleSheet.create({
    styleItem: {
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: 20,
        borderWidth: 0.5,
        marginTop: Mixins.scale(16),
        minHeight: Mixins.scale(74),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(18),
        width: '100%'
    },
    styleViewIconItem: {
        alignItems: 'center',
        flexDirection: 'row',
        marginTop: Mixins.scale(8)
    },
    viewImg: {
        height: Mixins.scale(22),
        tintColor: Colors.GRAYF7,
        width: Mixins.scale(22)
    }
});
