import React from 'react';
import { StyleSheet, FlatList, View } from 'react-native';

import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { XSquareLoading } from '@mwg-kits/components';

export const LoadingListTicket = () => {
    const dataLoading = [1, 2, 3];

    return (
        <FlatList
            data={dataLoading}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            style={{ marginTop: Mixins.scale(16) }}
            keyExtractor={(item) => item}
            renderItem={() => {
                return (
                    <View style={style.listContainer}>
                        <XSquareLoading style={style.titleWrapper} />
                        <XSquareLoading style={style.titleWrapper} />
                        <View
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center'
                            }}>
                            <XSquareLoading
                                style={{
                                    height: 15,
                                    width: 15,
                                    marginRight: 10,
                                    backgroundColor: Colors.GRAY_BODERRADIUS
                                }}
                            />
                            <XSquareLoading style={style.titleWrapper} />
                        </View>
                    </View>
                );
            }}
        />
    );
};
export const LoadingListTask = () => {
    const dataLoading = [1, 2, 3, 4, 5];

    return (
        <FlatList
            data={dataLoading}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
            style={{ marginTop: Mixins.scale(16) }}
            keyExtractor={(item) => item}
            renderItem={() => {
                return (
                    <XSquareLoading duration={1000} style={style.viewTask} />
                );
            }}
        />
    );
};
const style = StyleSheet.create({
    viewTask: {
        height: Mixins.scale(70),
        borderRadius: 16,
        marginTop: 16,
        width: '100%',
        backgroundColor: Colors.GRAY_BODERRADIUS
    },
    listContainer: {
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: 20,
        borderWidth: 0.5,
        marginTop: Mixins.scale(16),
        minHeight: Mixins.scale(74),
        paddingHorizontal: Mixins.scale(16),
        paddingVertical: Mixins.scale(18),
        width: '100%'
    },
    titleWrapper: {
        alignItems: 'center',
        backgroundColor: Colors.GRAY_BODERRADIUS,
        borderRadius: Mixins.scale(12),
        height: Mixins.scale(15),
        marginTop: 8,
        width: '100%'
    }
});
