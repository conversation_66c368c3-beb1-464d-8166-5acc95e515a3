import React from 'react';
import { TypingAnimation } from 'react-native-typing-animation';
import { MyText } from '@mwg-kits/components';
import { View } from 'react-native';
const { translate } = global.props.getTranslateConfig();
export default class TypingIndicator extends React.Component {
    render() {
        return (
            <View style={{ flexDirection: 'row', marginTop: 5 }}>
                <MyText text={translate('typing_language')} />
                <TypingAnimation
                    dotColor="gray"
                    dotMargin={3}
                    dotAmplitude={3}
                    dotSpeed={0.15}
                    dotRadius={2.5}
                    dotX={12}
                    dotY={6}
                />
            </View>
        );
    }
}
