import CryptoJS from 'crypto-js';

interface DecryptAESProps {
    encryptedText: string;
    aesKey?: string;
}

interface DecryptedData {
    username: string;
    [key: string]: string;
}

export const DecryptAES = ({
    encryptedText,
    aesKey = '28F4C'
}: DecryptAESProps): DecryptedData | null => {
    try {
        const bytes = CryptoJS.AES.decrypt(encryptedText, aesKey);
        const plaintext = bytes.toString(CryptoJS.enc.Utf8);

        const data = plaintext.split('-');
        const result: DecryptedData = {
            username: data[0]
        };

        // Thêm các phần tử khác vào object
        for (let i = 1; i < data.length; i++) {
            result[`field${i}`] = data[i];
        }

        return result;
    } catch (error) {
        console.error('Decryption Error:', error);
        return null;
    }
};
