import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiBase, METHOD } from '@mwg-kits/core';
import Toast from 'react-native-toast-message';

import { helper } from '@mwg-kits/common';
import { CONST_API } from './../../constant';
import * as actionHome from '../../Tickets/action';

const START_GET_LIST_TICKET = 'START_GET_LIST_TICKET';
const STOP_GET_LIST_TICKET = 'STOP_GET_LIST_TICKET';
const START_GET_DETAIL_TICKET = 'START_GET_DETAIL_TICKET';
const STOP_GET_DETAIL_TICKET = 'STOP_GET_DETAIL_TICKET';
const START_GET_LIST_LOCATIONGEO = 'START_GET_LIST_LOCATIONGEO';
const STOP_GET_LIST_LOCATIONGEO = 'STOP_GET_LIST_LOCATIONGEO';
const START_GET_LIST_COMMENT = 'START_GET_LIST_COMMENT';
const STOP_GET_LIST_COMMENT = 'STOP_GET_LIST_COMMENT';
const START_GET_STREAM_TOKEN = 'START_GET_STREAM_TOKEN';
const STOP_GET_STREAM_TOKEN = 'STOP_GET_STREAM_TOKEN';
const START_GET_LIST_PRIORITY = 'START_GET_LIST_PRIORITY';
const STOP_GET_LIST_PRIORITY = 'STOP_GET_LIST_PRIORITY';
const START_GET_LIST_FILE = 'START_GET_LIST_FILE';
const STOP_GET_LIST_FILE = 'STOP_GET_LIST_FILE';
const START_GET_LIST_ACTIVITY = 'START_GET_LIST_ACTIVITY';
const STOP_GET_LIST_ACTIVITY = 'STOP_GET_LIST_ACTIVITY';
const START_GET_SLA = 'START_GET_SLA';
const STOP_GET_SLA = 'STOP_GET_SLA';
const START_GET_ALL_STATUS_TICKET = 'START_GET_ALL_STATUS_TICKET';
const STOP_GET_ALL_STATUS_TICKET = 'STOP_GET_ALL_STATUS_TICKET';
const START_FILTER_TICKET = 'START_FILTER_TICKET';
const STOP_FILTER_TICKET = 'STOP_FILTER_TICKET';
const START_GET_LIST_TASK = 'START_GET_LIST_TASK';
const STOP_GET_LIST_TASK = 'STOP_GET_LIST_TASK';
const START_GET_LIST_TEMPLATE_TASK = 'START_GET_LIST_TEMPLATE_TASK';
const STOP_GET_LIST_TEMPLATE_TASK = 'STOP_GET_LIST_TEMPLATE_TASK';
const APPROVE_SUCCESS = 'APPROVE_SUCCESS';
const APPROVE_FAIL = 'APPROVE_FAIL';
const GET_TYPE_LIST_SUCCESS = 'GET_TYPE_LIST_SUCCESS';
const GET_TYPE_LIST_FAIL = 'GET_TYPE_LIST_FAIL';
const GET_SUGGEST_COMMENT = 'GET_SUGGEST_COMMENT';
const STOP_GET_SUGGEST_COMMENT = 'STOP_GET_SUGGEST_COMMENT';
const CREATE_UPDATE_SUGGEST_COMMENT = 'CREATE_UPDATE_COMMENT';
const STOP_CREATE_UPDATE_SUGGEST_COMMENT = 'STOP_CREATE_UPDATE_COMMENT';
const DELETE_SUGGEST_COMMENT = 'DELETE_SUGGEST_COMMENT';
const START_GET_HASHTAG = 'START_GET_HASHTAG';
const STOP_GET_HASHTAG = 'STOP_GET_HASHTAG';
const GET_RATING_INFO = 'GET_RATING_INFO';
export const ticketAction = {
    START_GET_HASHTAG,
    STOP_GET_HASHTAG,
    START_GET_LIST_TASK,
    STOP_GET_LIST_TASK,
    START_GET_LIST_TICKET,
    STOP_GET_LIST_TICKET,
    START_GET_DETAIL_TICKET,
    STOP_GET_DETAIL_TICKET,
    START_GET_LIST_LOCATIONGEO,
    STOP_GET_LIST_LOCATIONGEO,
    START_GET_LIST_COMMENT,
    STOP_GET_LIST_COMMENT,
    START_GET_STREAM_TOKEN,
    STOP_GET_STREAM_TOKEN,
    START_GET_LIST_PRIORITY,
    STOP_GET_LIST_PRIORITY,
    START_GET_LIST_FILE,
    STOP_GET_LIST_FILE,
    START_GET_LIST_ACTIVITY,
    STOP_GET_LIST_ACTIVITY,
    START_GET_SLA,
    STOP_GET_SLA,
    START_GET_ALL_STATUS_TICKET,
    STOP_GET_ALL_STATUS_TICKET,
    START_FILTER_TICKET,
    STOP_FILTER_TICKET,
    START_GET_LIST_TEMPLATE_TASK,
    STOP_GET_LIST_TEMPLATE_TASK,
    APPROVE_SUCCESS,
    APPROVE_FAIL,
    GET_TYPE_LIST_SUCCESS,
    GET_TYPE_LIST_FAIL,
    GET_SUGGEST_COMMENT,
    STOP_GET_SUGGEST_COMMENT,
    DELETE_SUGGEST_COMMENT,
    STOP_CREATE_UPDATE_SUGGEST_COMMENT,
    CREATE_UPDATE_SUGGEST_COMMENT,
    GET_RATING_INFO
};

const start_get_list_ticket = () => {
    return {
        type: START_GET_LIST_TICKET,
        isFetching: true
    };
};
export const stop_get_list_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getToken = (key) => AsyncStorage.getItem(key);
export const getListTicket = (data, navigation) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_ticket());
            let body = {
                arrayAssigneeUserId: [],
                arrayCreateUserId: [],
                arrayIssueStatusId: [],
                arrayPrioritiesId: [],
                arrayWatcherId: [],
                arrayStoreId: [],
                asTemplate: false,
                pageRequest: {
                    iDisplayStart: data?.iDisplayStart || 0,
                    iDisplayLength: data?.iDisplayLength || 10,
                    search: ''
                },
                arraySupportServiceIds: [],
                supportServiceId: data?.supportServiceId || -1,
                supportServiceName: null,
                quickFilterId: -1,
                chooseDate: 1,
                timeFrom:
                    new Date(new Date().getTime() - 86400000 * 7).getTime() ||
                    data.timeFrom,
                timeTo:
                    new Date(new Date().getTime()).getTime() || data.timeFrom,
                selectedFilterFromDay: true
            };

            const response = await apiBase(
                CONST_API.API_GET_LIST_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_ticket({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
                console.log('data?.supportServiceId', data);
                dispatch(actionHome.saveGroupId(data?.supportServiceId));
                dispatch(
                    actionHome.getDetailGroupTicket(
                        data?.supportServiceId,
                        navigation
                    )
                );
            } else {
                dispatch(
                    stop_get_list_ticket({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_ticket({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

/// Detail Ticket

const start_get_detail_ticket = () => {
    return {
        type: START_GET_DETAIL_TICKET,
        isFetching: true
    };
};
const stop_get_detail_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_DETAIL_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getDetailTicket = (ticketId) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_detail_ticket());
            const body = {
                ticketId: ticketId
            };

            const response = await apiBase(
                CONST_API.API_GET_DETAIL_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    _timeout: 60000,
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_detail_ticket({
                        isSuccess: true,
                        data: response?.object
                    })
                );
            } else {
                dispatch(
                    stop_get_detail_ticket({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_detail_ticket({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError:
                        error?.errorReason ||
                        'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_get_list_locationgeo = () => {
    return {
        type: START_GET_LIST_LOCATIONGEO,
        isFetching: true
    };
};
const stop_get_list_locationgeo = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_LOCATIONGEO,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListLocationgeo = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_locationgeo());
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_LIST_LOCATIONGEO,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_locationgeo({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_locationgeo({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_locationgeo({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

const start_get_list_comment = () => {
    return {
        type: START_GET_LIST_COMMENT,
        isFetching: true
    };
};
export const stop_get_list_comment = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_COMMENT,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListCommentTicket = (
    data,
    checkSeen = true,
    currentUserName = -1
) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_comment());
            let body = data;
            let token = await getToken('TOKEN_ACCESS');
            const response = await apiBase(
                CONST_API.API_LIST_COMMENT,
                METHOD.POST,
                body,
                {
                    access_token: token,
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_comment({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
                if (
                    checkSeen &&
                    response?.object?.data &&
                    response?.object?.data.length > 0
                ) {
                    let isCheckSeen = true;
                    //check handle seen when refresh
                    if (currentUserName !== -1) {
                        const index = response?.object?.data.findIndex(
                            (ele) => !ele.isUserCurrent
                        );
                        if (index != -1) {
                            const indexSeenUser = response?.object?.data[
                                index
                            ].seenUsers.findIndex(
                                (ele) => ele.userName === currentUserName
                            );
                            if (indexSeenUser !== -1) {
                                isCheckSeen = false;
                            }
                        } else {
                            isCheckSeen = false;
                        }
                    }
                    if (isCheckSeen) {
                        const idsComment = response?.object?.data.map((ele) => {
                            if (helper.hasProperty(ele, 'id')) {
                                return ele.id;
                            }
                        });
                        let variables = {
                            arrayCommentIds: idsComment,
                            ticketId: body.ticketId
                        };

                        apiBase(
                            CONST_API.SEEN_COMMENT,
                            METHOD.POST,
                            variables,
                            {
                                access_token: token,
                                enableLogger: true
                            }
                        )
                            .then((ress) => {
                                console.log(
                                    'RESPONSE SEEN COMMENT 388877',
                                    ress
                                );
                            })
                            .catch((e) => {
                                console.log(e);
                            });
                    }
                }
            } else {
                dispatch(
                    stop_get_list_comment({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_comment({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

const start_get_stream_token = () => {
    return {
        type: START_GET_STREAM_TOKEN,
        isFetching: true
    };
};
const stop_get_stream_token = ({
    data = {},
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_STREAM_TOKEN,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getStreamToken = (id) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_stream_token());
            let body = {
                ticketId: id
            };
            const response = await apiBase(
                CONST_API.API_GET_STREAM_TOKEN,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_stream_token({
                        isSuccess: true,
                        data: response?.object[0]
                    })
                );
            } else {
                dispatch(
                    stop_get_stream_token({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_stream_token({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

export const sendComment = (data, mentionMember = []) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let body = data;
                let parseString = '';
                mentionMember.forEach((item) => {
                    const userData = {
                        userId: item.user.id,
                        userName: item.user.username,
                        userFirstName: item.user.profile.firstName,
                        userLastName: item.user.profile.lastName,
                        userImage: item.user.profile.image
                    };

                    parseString += `@mention:${JSON.stringify(userData)} `;
                });
                body = {
                    ...body,
                    content: parseString + body.content
                };

                const response = await apiBase(
                    CONST_API.SEND_COMMENT,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );
                if (response.error) {
                    reject(response.errorReason);
                }
                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response?.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                reject(error);
            }
        });
    };
};
export const createTicket = (data) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let body = data;
                const response = await apiBase(
                    CONST_API.API_CREATE_TICKET,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (response.error) {
                    const msg = {
                        code: -1,
                        errorReason: 'shipaddress id rỗng'
                    };
                    throw msg;
                }
                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response?.object);
                } else {
                    throw response.errorReason;
                }
            } catch (error) {
                reject(error);
            }
        });
    };
};
// API_GET_LIST_PRIORITY

const start_get_list_priority = () => {
    return {
        type: START_GET_LIST_PRIORITY,
        isFetching: true
    };
};
const stop_get_list_priority = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_PRIORITY,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListPriority = () => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_priority());
            let body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10
                }
            };
            const response = await apiBase(
                CONST_API.API_GET_LIST_PRIORITY,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_priority({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_priority({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_priority({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
export function isArray(obj) {
    return obj !== undefined && obj !== null && obj.constructor === Array;
}

export const uploadImageComment = (params) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                global.props.showLoader();
                let filename = '';
                const data = new FormData();

                if (isArray(params.files)) {
                    data.append(
                        'ticketId',
                        encodeURIComponent(params?.ticketId)
                    );
                    data.append('comment', encodeURIComponent(params?.comment));
                    params?.files.map((item) => {
                        if (item.mime.includes('video')) {
                            // video
                            filename = `XWORK_FILE_${new Date().getTime()}_${
                                item.filename
                            }.mp4`;
                        } else {
                            // image
                            filename = `XWORK_FILE_${new Date().getTime()}_${
                                item.filename
                            }.jpg`;
                        }
                        data.append('file', {
                            uri: item.path,
                            name: encodeURIComponent(filename),
                            fileName: encodeURIComponent(filename),
                            type: item.mime
                        });
                    });
                } else {
                    if (params.files.mime.includes('video')) {
                        // video
                        filename = `XWORK_FILE_${new Date().getTime()}.mp4`;
                    } else {
                        // image
                        filename = `XWORK_FILE_${new Date().getTime()}.jpg`;
                    }
                    data.append(
                        'ticketId',
                        encodeURIComponent(params?.ticketId)
                    );
                    data.append('comment', encodeURIComponent(params?.comment));
                    data.append('file', {
                        uri: params.files.path,
                        name: encodeURIComponent(filename),
                        fileName: encodeURIComponent(filename),
                        type: params.files.mime
                    });
                }

                let token = await getToken('TOKEN_ACCESS');
                const response = await apiBase(
                    CONST_API.API_IMAGE_COMMENT,
                    METHOD.POST,
                    data,
                    {
                        _oauthToken: `Bearer ${token}`,
                        isCustomToken: true,
                        _timeout: 120000,
                        isUpload: true,
                        enableLogger: true
                    }
                );

                global.props.hideLoader();

                if (
                    !response.error ||
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response.object)
                ) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                global.props.hideLoader();
                reject(error);
            }
        });
    };
};
export const uploadImageGroundTask = (params, isCheckIn) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                global.props.showLoader();
                let filename = '';
                const data = new FormData();
                // image
                filename = `XWORK_FILE_${new Date().getTime()}.jpeg`;
                data.append('ticketID', encodeURIComponent(params?.ticketId));
                if (isCheckIn)
                    data.append('checkinImage', {
                        uri: params?.files?.path,
                        name: encodeURIComponent(filename),
                        //fileName: encodeURIComponent(filename),
                        type: params?.files?.mime
                    });
                else {
                    data.append('checkoutImage', {
                        uri: params?.files?.path,
                        name: encodeURIComponent(filename),
                        fileName: encodeURIComponent(filename),
                        type: params?.files?.mime
                    });
                }
                let token = await getToken('TOKEN_ACCESS');
                const response = await apiBase(
                    CONST_API.API_UPLOAD_FILE_GROUND,
                    METHOD.POST,
                    data,
                    {
                        _oauthToken: `Bearer ${token}`,
                        isCustomToken: true,
                        _timeout: 120000,
                        isUpload: true,
                        enableLogger: true
                    }
                );
                global.props.hideLoader();
                if (
                    !response.error ||
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response.object)
                ) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                global.props.hideLoader();
                reject(error);
            }
        });
    };
};

const start_get_list_file = () => {
    return {
        type: START_GET_LIST_FILE,
        isFetching: true
    };
};
export const stop_get_list_file = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_FILE,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListFile = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_file());
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_LIST_FILE_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_file({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_file({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_file({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

const start_get_list_activity = () => {
    return {
        type: START_GET_LIST_ACTIVITY,
        isFetching: true
    };
};
const stop_get_list_activity = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_ACTIVITY,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListActivity = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_activity());
            let body = {
                ticketId: data,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 30,
                    search: ''
                }
            };

            const response = await apiBase(
                CONST_API.API_GET_LIST_ACTIVITY_HISTORY,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_activity({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_activity({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_activity({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

export const updateStatusTicket = (data) => {
    return async () => {
        try {
            let body = data;
            const response = await apiBase(
                CONST_API.API_UPDATE_STATUS_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                return response;
            }
        } catch (error) {
            return error.errorReason;
        }
    };
};

////// getListProgressTicket
const start_get_list_progress = () => {
    return {
        type: START_GET_LIST_FILE,
        isFetching: true
    };
};
const stop_get_list_progress = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_FILE,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListProgressTicket = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_progress());
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_LIST_FILE_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_progress({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_progress({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_file({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

////// start_get_sla
const start_get_sla = () => {
    return {
        type: START_GET_SLA,
        isFetching: true
    };
};
const stop_get_sla = ({
    data = {},
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_SLA,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getSlaTicket = (ticketId) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_sla());
            let body = {
                ticketId: ticketId
            };
            const response = await apiBase(
                CONST_API.API_GET_SLA,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_sla({
                        isSuccess: true,
                        data: response?.object
                    })
                );
            } else {
                dispatch(
                    stop_get_sla({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_sla({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
const start_filer_ticket = () => {
    return {
        type: START_FILTER_TICKET,
        isFetching: true
    };
};
const stop_filer_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_FILTER_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
const start_get_all_status_ticket = () => {
    return {
        type: START_GET_ALL_STATUS_TICKET,
        isFetching: true
    };
};
const stop_get_all_status_ticket = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_ALL_STATUS_TICKET,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getAllStatusTicket = (id) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_all_status_ticket());
            let body = {
                supportServiceId: id || -1,
                pageRequest: {
                    iDisplayLength: 500,
                    iDisplayStart: 0,
                    search: ''
                }
            };
            const response = await apiBase(
                CONST_API.API_GET_ALL_STATUS_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_all_status_ticket({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_get_all_status_ticket({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_all_status_ticket({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

export const filterTicket = (data, filterNewFeed = false) => {
    return async (dispatch) => {
        console.log(
            'responseresponseresponseresponse',
            JSON.stringify(data, null, 2),
            CONST_API.API_GET_LIST_TICKET,
            await getToken('TOKEN_ACCESS')
        );
        try {
            dispatch(start_filer_ticket());
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_LIST_TICKET,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                if (filterNewFeed) {
                    dispatch({
                        type: 'GET_TICKET_IN_FEED_SUCCESS',
                        data: response?.object?.data
                    });
                    return;
                }
                dispatch(
                    stop_filer_ticket({
                        isSuccess: true,
                        data: response?.object?.data
                    })
                );
            } else {
                dispatch(
                    stop_filer_ticket({
                        isError: true,
                        isSuccess: false,
                        data: {},
                        msgError: response?.errorReason
                    })
                );
            }
            console.log(
                'responseresponseresponseresponse',
                JSON.stringify(response.data, null, 2)
            );
            return response;
        } catch (error) {
            dispatch(
                stop_filer_ticket({
                    isError: true,
                    isSuccess: false,
                    data: {},
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
            return error;
        }
    };
};
///// GET TASK
const start_get_list_task = () => {
    return {
        type: START_GET_LIST_TASK,
        isFetching: true
    };
};
const stop_get_list_task = ({
    data = {},
    isSuccess = true,
    msgError = '',
    isFetching = false,
    percentDone = 0
}) => {
    return {
        type: STOP_GET_LIST_TASK,
        data,
        isSuccess,
        isFetching,
        msgError,
        percentDone
    };
};

export const getListTask = (data, isFetching = true) => {
    return async (dispatch, getState) => {
        try {
            const { id, supportServiceType, statusId } =
                getState().ticketReducer.detailTicket.data.ticket;
            if (isFetching) {
                dispatch(start_get_list_task());
            }
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_LIST_TASK,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                if (
                    response?.object.percentDone === 1 &&
                    supportServiceType === 'GROUND_TASK' &&
                    statusId === 2814758
                ) {
                    const bodyUpdate = {
                        id: id,
                        statusId: 2814759
                    };
                    dispatch(updateStatusTicket(bodyUpdate));
                    dispatch(getDetailTicket(id));
                }
                dispatch(
                    stop_get_list_task({
                        isSuccess: true,
                        data: response?.object.data,
                        percentDone: response?.object.percentDone
                    })
                );
            } else {
                dispatch(
                    stop_get_list_task({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
        } catch (error) {
            dispatch(
                stop_get_list_task({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};

const start_get_list_template_task = () => {
    return {
        type: START_GET_LIST_TEMPLATE_TASK,
        isFetching: true
    };
};
const stop_get_list_template_task = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_LIST_TEMPLATE_TASK,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};

export const getListTemplateTask = (data) => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_template_task());
            let body = data;
            const response = await apiBase(
                CONST_API.API_GET_TEMPLATE_LIST,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch(
                    stop_get_list_template_task({
                        isSuccess: true,
                        data: response?.object.data
                    })
                );
            } else {
                dispatch(
                    stop_get_list_template_task({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
            return response;
        } catch (error) {
            dispatch(
                stop_get_list_template_task({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
            return error;
        }
    };
};

export const createWork = (data) => {
    return async () => {
        try {
            let body = data;
            const response = await apiBase(
                CONST_API.API_CREATE_WORK,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            return response;
        } catch (error) {
            return error;
        }
    };
};
export const removeFile = (data) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                let body = data;
                const response = await apiBase(
                    CONST_API.API_REMOVE_FILE,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );

                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    resolve(response.object);
                }
            } catch (error) {
                let msg = 'Xoá thất bại';

                if (helper.IsValidateObject(error.message)) {
                    msg = error.message;
                }
                reject(msg);
            }
        });
    };
};
export const actionCheckTask = (data) => {
    return async () => {
        try {
            let body = data;
            const response = await apiBase(
                CONST_API.API_UPDATE_TASK,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                return response.object;
            }
        } catch (error) {
            return error;
        }
    };
};

export const actionApproveTicket = (data) => {
    return async (dispatch) => {
        return new Promise(async (resolve, reject) => {
            global.props.showLoader();
            let body = data;
            try {
                const response = await apiBase(
                    CONST_API.API_APPROVE_TICKET,
                    METHOD.POST,
                    body,
                    {
                        access_token: await getToken('TOKEN_ACCESS'),
                        enableLogger: true
                    }
                );
                setTimeout(() => {}, 200);
                global.props.hideLoader();

                if (
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response?.object) ||
                    !response?.error
                ) {
                    dispatch({
                        type: APPROVE_SUCCESS
                    });
                    dispatch(getDetailTicket(data.ticketId));
                    resolve(response);
                } else {
                    resolve(false);
                }
            } catch (error) {
                setTimeout(() => {}, 200);
                global.props.hideLoader();
                reject(error.errorReason);
            }
        });
    };
};
export const removeComments = (body) => {
    return async () => {
        try {
            const response = await apiBase(
                CONST_API.API_REMOVE_COMMENT,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            return response;
        } catch (error) {
            return error;
        }
    };
};
export const uploadFile = (params) => {
    return async () => {
        return new Promise(async (resolve, reject) => {
            try {
                global.props.showLoader();

                const data = new FormData();
                params?.files.map((item) => {
                    data.append('file', {
                        uri: item.uri,
                        name: encodeURIComponent(item.name),
                        fileName: encodeURIComponent(item.name),
                        type: item.type
                    });
                });

                data.append('ticketId', encodeURIComponent(params?.ticketId));
                data.append('comment', encodeURIComponent(params?.comment));

                const res = await fetch(CONST_API.API_IMAGE_COMMENT, {
                    method: 'post',
                    body: data,
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: `Bearer ${await getToken(
                            'TOKEN_ACCESS'
                        )}`
                    }
                });
                global.props.hideLoader();

                const response = await res.json();

                if (
                    !response.error ||
                    helper.IsValidateObject(response) ||
                    helper.hasProperty(response, 'object') ||
                    helper.IsValidateObject(response.object)
                ) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                global.props.hideLoader();

                reject(error);
            }
        });
    };
};

export const reset_props = () => {
    return (dispatch) => {
        dispatch({
            type: 'RESET_PROPS'
        });
    };
};

export const getApproveTypeList = () => {
    return async (dispatch) => {
        const body = {
            pageRequest: {
                iDisplayLength: 10,
                iDisplayStart: 0,
                search: ''
            }
        };

        try {
            const response = await apiBase(
                CONST_API.GET_APPROVE_TYPE_LIST,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                let newTypeList = [];
                response.data.map((item) => {
                    newTypeList.push(item.code);
                    return item;
                });
                dispatch({
                    type: GET_TYPE_LIST_SUCCESS,
                    data: newTypeList
                });
            }
        } catch (error) {
            dispatch({
                type: GET_TYPE_LIST_FAIL,
                msgError: error.errorReason
            });
        }
    };
};
export const getDataService = (data) => {
    return (dispatch) => {
        dispatch({
            type: 'DATA_SERVICE',
            data
        });
    };
};

export const getCommentSuggest = (isRefresh = false) => {
    return async (dispatch) => {
        try {
            if (!isRefresh) {
                dispatch({ type: GET_SUGGEST_COMMENT });
            }
            const body = {
                type: 'TICKET'
            };

            const response = await apiBase(
                CONST_API.GET_SUGGEST_COMMENT,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) &&
                helper.hasProperty(response, 'object') &&
                helper.hasProperty(response.object, 'suggestcoments')
            ) {
                dispatch({
                    type: STOP_GET_SUGGEST_COMMENT,
                    payload: {
                        data: response.object.suggestcoments,
                        isSuccess: true
                    }
                });
            } else {
                const msg = {
                    code: -1,
                    errorReason: 'Lấy danh sách lỗi'
                };
                throw msg;
            }
        } catch (e) {
            dispatch({
                type: STOP_GET_SUGGEST_COMMENT,
                payload: { data: [], isSuccess: false }
            });
        }
    };
};
export const deleteCommentSuggest = (id) => {
    return async () => {
        try {
            const body = {
                id
            };

            const response = await apiBase(
                CONST_API.DELETE_SUGGEST_COMMENT,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            return response;
        } catch (e) {
            return e;
        }
    };
};
export const createUpdateCommentSuggest = (body) => {
    return async () => {
        try {
            const response = await apiBase(
                CONST_API.CREATE_UPDATE_SUGGEST_COMMENT,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );

            return response;
        } catch (e) {
            return e;
        }
    };
};

export const actionRatingUser = (body) => {
    return async () => {
        let data = body;
        try {
            const response = await apiBase(
                CONST_API.API_RATING_USER,
                METHOD.POST,
                data,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            return response;
        } catch (e) {
            return e;
        }
    };
};
const start_get_list_hashtag = () => {
    return {
        type: START_GET_HASHTAG,
        isFetching: true
    };
};
const stop_get_list_hashtag = ({
    data = [],
    isSuccess = true,
    msgError = '',
    isFetching = false
}) => {
    return {
        type: STOP_GET_HASHTAG,
        data,
        isSuccess,
        isFetching,
        msgError
    };
};
export const getListHashtag = () => {
    return async (dispatch) => {
        try {
            dispatch(start_get_list_hashtag());
            let body = { keyword: '', pageIndex: 0, pageSize: 10 };

            const response = await apiBase(
                CONST_API.API_GET_HASHTAG,
                METHOD.POST,
                body,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                !helper.IsValidateObject(response) ||
                !helper.hasProperty(response, 'object') ||
                !helper.IsValidateObject(response) ||
                response?.error
            ) {
                dispatch(
                    stop_get_list_hashtag({
                        isError: true,
                        isSuccess: false,
                        data: [],
                        msgError: response?.errorReason
                    })
                );
            }
            dispatch(
                stop_get_list_hashtag({
                    isSuccess: true,
                    data: response?.object
                })
            );
        } catch (error) {
            dispatch(
                stop_get_list_hashtag({
                    isError: true,
                    isSuccess: false,
                    data: [],
                    msgError: 'Đã có lỗi xảy ra. Vui lòng thử lại'
                })
            );
        }
    };
};
export const actionCopyToGroup = (body) => {
    return async () => {
        let data = body;
        try {
            const response = await apiBase(
                CONST_API.API_COPY_TO_GROUP,
                METHOD.POST,
                data,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            return response;
        } catch (e) {
            return e;
        }
    };
};

export const actionRatingGetInfo = (body) => {
    return async (dispatch) => {
        let data = body;
        try {
            const response = await apiBase(
                CONST_API.API_RATING_USER_GETINFO,
                METHOD.POST,
                data,
                {
                    access_token: await getToken('TOKEN_ACCESS'),
                    enableLogger: true
                }
            );
            if (
                helper.IsValidateObject(response) ||
                helper.hasProperty(response, 'object') ||
                helper.IsValidateObject(response?.object) ||
                !response?.error
            ) {
                dispatch({
                    type: GET_RATING_INFO,
                    data: response
                });
            }
        } catch (e) {
            return e;
        }
    };
};

export const uploadImage = (params) => {
    return async () => {
        console.log(params, '12321312312321312');
        return new Promise(async (resolve, reject) => {
            try {
                global.props.showLoader();
                const data = new FormData();

                params?.map((item) => {
                    if (item.mime.includes('video')) {
                        // video
                        filename = `XWORK_FILE_${new Date().getTime()}_${
                            item.filename
                        }.mp4`;
                    } else {
                        // image
                        filename = `XWORK_FILE_${new Date().getTime()}_${
                            item.filename
                        }.jpg`;
                    }
                    data.append('file', {
                        uri: item.path,
                        name: encodeURIComponent(filename),
                        fileName: encodeURIComponent(filename),
                        type: item.mime
                    });
                });
                let token = await getToken('TOKEN_ACCESS');
                const response = await apiBase(
                    'https://erpapp.tgdd.vn/mwg-app-media-service/api/media/upload',
                    METHOD.POST,
                    data,
                    {
                        _oauthToken: `Bearer ${token}`,
                        isCustomToken: true,
                        _timeout: 20000,
                        isUpload: true,
                        enableLogger: true
                    }
                );

                if (
                    !response.error &&
                    helper.IsValidateObject(response) &&
                    helper.hasProperty(response, 'object') &&
                    helper.IsValidateObject(response.object)
                ) {
                    resolve(response.object);
                } else {
                    reject(response.errorReason);
                }
            } catch (error) {
                reject(error);
            }
        });
    };
};

export const actionConfirmBotComment = (data, callback) => {
    return async () => {
        const token = await getToken('TOKEN_ACCESS');

        apiBase(CONST_API.API_CONFIRM_BOT_COMMENT, METHOD.POST, data, {
            access_token: token,
            enableLogger: true
        })
            .then((res) => {
                console.log('firstLog res', res);
                Toast.show({
                    text1: 'Cảm ơn bạn đã đánh giá',
                    type: 'success'
                });
                setTimeout(() => {
                    Toast.hide();
                }, 2000);
                callback();
            })
            .catch((error) => {
                console.log('firstLog error', error);
                Toast.show({
                    text1: 'Đã có lỗi xảy ra. Vui lòng thử lại',
                    type: 'error'
                });
                setTimeout(() => {
                    Toast.hide();
                }, 2000);
            });
    };
};
