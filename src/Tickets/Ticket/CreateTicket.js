import React, { PureComponent } from 'react';
import {
    Image,
    ScrollView,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { helper } from '@mwg-kits/common';
import { MyText } from '@mwg-kits/components';
import * as _actionHome from '../action';
import * as _actionTicket from './action';
import Toast from 'react-native-toast-message';
import { stylesTicket } from './stylesTicket';
import {
    RenderLocationGeo,
    RenderPriority,
    RenderUserAssginee
} from './Components/ComponentCreateTicket';
import ModalAddFollowers from '../../modal/ModalAddFollowers';
import * as _actionGroundTask from './Details/GroundTask/action';
import * as _actionDetailTicket from './Details/action';

import { RenderTime } from '../../modal/ComponentModal';
import { convertUTCDateToLocalDate } from './EditTicket';
import { GlobalStore } from 'redux-micro-frontend';
import { ModalListTrouble } from '../../modal/ModalListTrouble';
const { translate } = global.props.getTranslateConfig();
class CreateTicket extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            title: '',
            descpriction: '',
            locationSelected: null,
            selectedGroup: null,
            groupUserFollowers: [],
            userReceiver: null,
            startDate: new Date(),
            endDate: new Date(),
            prioritySelected: null,
            iDisplayLength: 10,
            solution: '',
            dataLengthGeoLocation: 20,
            dataLengthTrouble: 20,
            isLengthUser: 10,
            allUser: false,
            showModalUser: false,
            showModalAddReceiver: false,
            isShowModalCalendar: false,
            isShowModalListService: false,
            isShowModalGroup: false,
            showModalListTrouble: false,
            chooseDate: '',
            listTroubleData: []
        };
        this.globalStore = GlobalStore.Get();
    }

    async componentDidMount() {
        if (helper.IsEmptyArray(this.props.listGroupTicket.data)) {
            await this.props.actionHome.getListGroupTicket();
        }
        if (this.props.listGroupTicket?.isSuccess) {
            this.checkGroupSelect();
        }
        this.props.navigation.addListener('blur', () => {
            this.setState({
                isShowModalListService: false,
                isShowModalGroup: false,
                showModalAddReceiver: false,
                showModalUser: false
            });
        });
        this.initLocationGeo();
        this.initListTrouble();
        await this.props.actionTicket.getListPriority();
        const { listPriority } = this.props;

        if (listPriority?.isSuccess) {
            this.setState({
                prioritySelected: listPriority?.data[0]
            });
        }
    }
    checkGroupSelect = () => {
        const { params } = this.props.route;
        if (params) {
            let itemGroup = null;
            this.props.listGroupTicket.data.forEach((element) => {
                if (element.id === params) {
                    itemGroup = element;
                }
                return;
            });
            this.setState(
                {
                    selectedGroup: itemGroup
                },
                () => {
                    this.initMemberGroup();
                }
            );
        }
    };

    async componentDidUpdate(prevProps) {
        const { xworkData } = this.props;
        if (xworkData?.constants !== prevProps.xworkData?.constants) {
            if (helper.IsEmptyArray(this.props.listGroupTicket.data)) {
                await this.props.actionHome.getListGroupTicket();
            }

            if (this.props.listGroupTicket?.isSuccess) {
                this.checkGroupSelect();
            }
            this.initLocationGeo();
            this.props.actionTicket.getListPriority();
            const { listPriority } = this.props;
            if (listPriority?.isSuccess) {
                this.setState({
                    prioritySelected: listPriority?.data[0]
                });
            }
        }
    }
    initLocationGeo = (txt) => {
        const data = {
            supportServiceId: -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: txt || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
    };
    handeleGroupSelected = () => {
        const { params } = this.props.route;
        let itemSelected = null;
        itemSelected = this.props.listGroupTicket?.data?.filter(
            (item) => item?.id === params
        );
        this.setState({
            selectedGroup: itemSelected
        });
    };

    initMemberGroup = () => {
        const data = {
            supportServiceId: this.state.selectedGroup?.id || -1,
            pageRequest: { iDisplayStart: 0, iDisplayLength: 20, search: '' },
            requestId: ''
        };
        this.props.actionHome.getListMemberGroup(data);
        this.props.actionTicket.getListLocationgeo(data);
        this.setState({
            groupUserFollowers: [],
            userReceiver: null
        });
    };

    renderHeader = () => {
        console.log(this.props.listTrouble, '123132131232131321');
        const { selectedGroup } = this.state;
        return (
            <View style={{ paddingHorizontal: Mixins.scale(18) }}>
                <MyText
                    text={`${translate('ticket_name')}: `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>
                <TextInput
                    value={this.state.title}
                    onChangeText={(text) => {
                        this.setState({
                            title: text
                        });
                    }}
                    style={stylesTicket.inputTitle}
                    placeholder={`${translate('enter_ticket_name')}...`}
                    placeholderTextColor={Colors.GRAY_PLACEHODER}
                />
                <MyText
                    text={`${translate('in_group')}: `}
                    addSize={2}
                    typeFont="medium"
                    style={{
                        color: Colors.GRAYF10,
                        marginTop: Mixins.scale(32)
                    }}>
                    <MyText
                        text="* "
                        addSize={2}
                        typeFont="medium"
                        style={stylesTicket.txtStart}
                    />
                </MyText>

                <TouchableOpacity
                    onPress={() => {
                        this.setState({
                            isShowModalGroup: true
                        });
                    }}
                    style={stylesTicket.viewSelecGroup}>
                    <MyText
                        numberOfLines={1}
                        addSize={1}
                        style={{
                            color: !helper.IsValidateObject(selectedGroup)
                                ? Colors.GRAYF8
                                : Colors.BLACK
                        }}
                        text={
                            helper.IsValidateObject(selectedGroup)
                                ? this.state.selectedGroup.name
                                : translate('select_group')
                        }
                    />
                    <View style={stylesTicket.viewIconDown}>
                        <AntDesign name="down" size={12} color={Colors.BLACK} />
                    </View>
                </TouchableOpacity>
                {selectedGroup?.serviceType !== 'GROUND_TASK' && (
                    <View>
                        <MyText
                            text={`${translate('detail_descrip')}: `}
                            addSize={2}
                            typeFont="medium"
                            style={{
                                color: Colors.GRAYF10,
                                marginTop: Mixins.scale(32)
                            }}>
                            <MyText
                                text="* "
                                addSize={2}
                                typeFont="medium"
                                style={stylesTicket.txtStart}
                            />
                        </MyText>

                        <TextInput
                            onChangeText={(text) => {
                                this.setState({
                                    descpriction: text
                                });
                            }}
                            value={this.state.descpriction}
                            multiline
                            textAlignVertical="top"
                            style={[
                                stylesTicket.txtDescription,
                                { marginTop: 16 }
                            ]}
                            placeholder={`${translate('enter_content')}...`}
                            placeholderTextColor={Colors.GRAY_PLACEHODER}
                        />
                        {/* <MyText
                            text={`${translate('solution')}: `}
                            addSize={2}
                            typeFont="medium"
                            style={{
                                color: Colors.GRAYF10,
                                marginTop: Mixins.scale(32)
                            }}></MyText>

                        <TextInput
                            onChangeText={(text) => {
                                this.setState({
                                    solution: text
                                });
                            }}
                            value={this.state.solution}
                            multiline
                            textAlignVertical="top"
                            style={stylesTicket.txtDescription}
                            placeholder={`${translate('enter_solution')}...`}
                            placeholderTextColor={Colors.GRAY_PLACEHODER}
                        /> */}
                    </View>
                )}
            </View>
        );
    };

    renderContent = () => {
        const { userReceiver } = this.state;

        return (
            <View
                style={{
                    marginHorizontal: Mixins.scale(16),
                    marginTop: Mixins.scale(16)
                }}>
                {/* <TouchableOpacity
                    onPress={() => {
                        this.setState({
                            showModalListTrouble: true
                        });
                    }}
                    style={{
                        backgroundColor: Colors.WHITE,
                        borderColor: Colors.GRAYF6,
                        borderRadius: Mixins.scale(12),
                        borderWidth: 0.5,
                        height: Mixins.scale(44),
                        justifyContent: 'center',
                        marginVertical: Mixins.scale(16),
                        paddingHorizontal: Mixins.scale(12),
                        width: '100%'
                    }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}>
                        <MyText style={{}} text={translate('workaround')} />
                        <AntDesign
                            name="down"
                            color={Colors.GRAYF9}
                            size={16}
                            style={{
                                marginRight: Mixins.scale(4)
                            }}
                        />
                    </View>
                </TouchableOpacity> */}
                <View style={stylesTicket.viewRowBetween_16}>
                    <View style={stylesTicket.viewRowAlignItems}>
                        <Image
                            style={stylesTicket.imgBlue_20}
                            source={{ uri: 'ic_calendar' }}
                        />
                        <MyText
                            text={translate('date')}
                            addSize={1}
                            style={{
                                color: Colors.BLACK_HEADER_TITLE,
                                marginLeft: Mixins.scale(8)
                            }}
                            typeFont="medium"
                        />
                    </View>
                </View>

                <RenderTime
                    startDate={this.state.startDate}
                    hideTitle={true}
                    endDate={this.state.endDate}
                    onPressShowModal={(item) => {
                        this.setState({
                            chooseDate: item,
                            isShowModalCalendar: true
                        });
                    }}
                />
                {/* ///// Sử dụng chung với edit nên tách component ra */}
                <RenderUserAssginee
                    handleShowModal={this.handleShowModal}
                    userReceiver={userReceiver}
                    groupUserFollowers={this.state.groupUserFollowers}
                />

                <RenderLocationGeo
                    onPressShowModal={() => {
                        this.setState({
                            isShowModalListService: true
                        });
                    }}
                    locationSelected={
                        helper.IsValidateObject(this.state.locationSelected)
                            ? this.state.locationSelected?.name
                            : translate('please_choose_supermarket')
                    }
                />
                {/* //// render Độ ưu tiên */}
                <RenderPriority
                    prioritySelected={this.state.prioritySelected}
                    listPriority={this.props.listPriority.data}
                    onChangeComponent={this.onChangePriority}
                    placeholder={translate('low')}
                />
            </View>
        );
    };
    onChangePriority = (item) => {
        this.setState({
            prioritySelected: item
        });
    };
    handleGlobalState = () => {
        let action = {
            type: 'GET_LIST_TICKET',
            payload: { getListTicket: true }
        };
        this.globalStore.DispatchAction('XworkStore', action);
    };
    handleNewFeed = (item) => {
        const { listNewFeed } = this.props;
        let newData = [...listNewFeed.data];

        if (this.props.dataLocal?.isFilter) {
            this.props.actionHome.getNewFeed();
            this.props.actionHome.getLocal({ isRefresh: true });
        } else {
            let newItem = {
                ...item,
                ticket: {
                    ...item?.ticket,
                    ...item?.ticketView
                }
            };
            newData = [newItem, ...listNewFeed.data];
            this.props.actionHome.stop_get_new_feed({
                data: newData
            });
        }
    };
    handleButtonSave = async () => {
        const { xworkData } = this.props;
        try {
            const {
                title,
                descpriction,
                selectedGroup,
                userReceiver,
                groupUserFollowers,
                locationSelected,
                startDate,
                endDate,
                prioritySelected,
                solution
            } = this.state;
            let params = {};
            global.props.showLoader();
            if (selectedGroup.serviceType === 'GROUND_TASK') {
                const getManager = await this.props.actionGroundTask.getManager(
                    xworkData?.fullProfile.id
                );
                if (getManager) {
                    params = {
                        supportServiceId: selectedGroup?.id,
                        subject: this.state.title,
                        content: 'Service Ground Task',
                        startTime: new Date().getTime(),
                        source: 'phone',
                        createdUserId: -1,
                        statusId: -1,
                        watcherUserId: [
                            getManager.userinfo.manager.id,
                            getManager.userinfo.leader.id
                        ],
                        assigneeUserId: xworkData?.fullProfile.id || -1
                    };
                }
            } else {
                params = {
                    source: 'phone',
                    id: -1,
                    supportServiceId: selectedGroup?.id,
                    subject: title,
                    solution: solution,
                    content: descpriction,
                    startTime: new Date(startDate).getTime(),
                    dueTime: new Date(endDate).getTime(),
                    createdUserId: -1,
                    assigneeUserId: userReceiver?.user?.id || -1,
                    watcherUserId:
                        groupUserFollowers?.map((e) => e.user?.id) || -1,
                    statusId: -1,
                    priorityId: prioritySelected?.priority,
                    locationGeoId: locationSelected?.id || -1,
                    approver: [],
                    isOrder: true
                };
            }
            const reponse = await this.props.actionTicket.createTicket(params);
            if (reponse) {
                this.handleGlobalState();
                this.handleNewFeed(reponse);
                const data = {
                    supportServiceId: this.state.selectedGroup?.id
                };
                this.props.actionTicket.getListTicket(data);
                global.props.hideLoader();
                Toast.show({
                    type: 'success',
                    text1: translate('create_ticket_success'),
                    position: 'bottom'
                });
                setTimeout(() => {
                    Toast.hide();
                    this.props.actionTicket.getDataService(
                        this.state.selectedGroup
                    );
                    this.props.navigation.goBack();
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate(
                    error?.errorReason?.includes('leader')
                        ? 'not_author_create'
                        : 'create_ticket_fail'
                ),
                position: 'bottom'
            });
        }
    };

    renderButtonSave = () => {
        const { title, descpriction, selectedGroup } = this.state;
        const checkShowButton =
            title?.length === 0 ||
            (selectedGroup?.serviceType !== 'GROUND_TASK' &&
                descpriction?.length === 0) ||
            !helper.IsValidateObject(selectedGroup);

        // if (checkShowButton) {
        //     return null;
        // }
        return (
            <TouchableOpacity
                disabled={checkShowButton}
                onPress={this.handleButtonSave}
                style={[
                    stylesTicket.btnSave,
                    {
                        backgroundColor: checkShowButton
                            ? Colors.GRAYF6
                            : Colors.DARK_BLUE_60
                    }
                ]}>
                <MyText
                    text={translate('create')}
                    addSize={3}
                    typeFont="semiBold"
                    style={{ color: Colors.WHITE }}
                />
            </TouchableOpacity>
        );
    };

    onPressSaveModal = (item) => {
        this.setState({
            showModalUser: false,
            groupUserFollowers: item
        });
        this.handleSearchUser('');
    };

    handleLoadMoreUser = async (txt, allUser) => {
        const { isLengthUser } = this.state;

        if (allUser) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: isLengthUser + 10,
                    search: txt || ''
                }
            };
            await this.props.actionHome.searchAllUser(body, true);
        } else {
            const data = {
                supportServiceId: this.state.selectedGroup?.id,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: isLengthUser + 10,
                    search: txt || ''
                },
                requestId: ''
            };
            this.props.actionHome.getListMemberGroup(data);
        }
        this.setState({
            isLengthUser: isLengthUser + 10
        });
    };
    handleSearchUser = async (txt, allUser) => {
        this.setState({
            allUser: allUser
        });
        if (allUser) {
            const body = {
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: txt || ''
                }
            };
            this.props.actionHome.searchAllUser(body, true);
        } else {
            const data = {
                supportServiceId: this.state.selectedGroup?.id,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: txt || ''
                },
                requestId: ''
            };
            this.props.actionHome.getListMemberGroup(data);
        }
    };
    onPressSaveReceiver = (item) => {
        this.setState({
            userReceiver: item,
            showModalAddReceiver: false
        });
        this.handleSearchUser('');
    };

    handleShowModal = (item) => {
        const { selectedGroup } = this.state;
        if (!helper.IsValidateObject(selectedGroup)) {
            return global.props.alert({
                title: translate('notify'),
                show: true,
                message: translate('please_select_group'),
                type: 'info',
                confirmText: translate('close'),
                onConfirmPressed: () => {
                    global.props.alert({ show: false });
                }
            });
        } else {
            if (item?.id === 0) {
                return this.setState({
                    showModalAddReceiver: true
                });
            }
            if (item?.id === 1) {
                return this.setState({
                    showModalUser: true
                });
            }
        }
    };

    onChangeSelectedDate = (startDate, endDate) => {
        if (startDate && endDate) {
            this.setState({
                endDate: endDate,
                startDate: startDate
            });
        }
        this.onPressDimissModalCalendar();
    };

    onPressDimissModalCalendar = () => {
        this.setState({
            isShowModalCalendar: false
        });
    };
    searchGroup = (txtSearch) => {
        const body = {
            iDisplayStart: 0,
            iDisplayLength: this.state.iDisplayLength,
            search: txtSearch || ''
        };
        this.props.actionHome.getListGroupTicket(body, true);
    };
    hideModalGroup = () => {
        this.setState({
            isShowModalGroup: false
        });
        const body = {
            iDisplayStart: 0,
            iDisplayLength: 10,
            search: ''
        };
        this.props.actionHome.getListGroupTicket(body);
    };
    hideModalLocation = () => {
        this.setState({
            isShowModalListService: false
        });
        this.initLocationGeo('');
    };
    loadMoreLocation = (text) => {
        const { dataLengthGeoLocation } = this.state;

        const data = {
            supportServiceId: this.state.selectedGroup?.id || -1,
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: dataLengthGeoLocation + 10,
                search: text || ''
            },
            requestId: ''
        };
        this.props.actionTicket.getListLocationgeo(data);
        this.setState({
            dataLengthGeoLocation: dataLengthGeoLocation + 10
        });
    };
    loadMoreGroup = (text) => {
        const { iDisplayLength } = this.state;
        const body = {
            iDisplayStart: 0,
            iDisplayLength: iDisplayLength + 10,
            search: text ?? ''
        };
        this.props.actionHome.getListGroupTicket(body);
        this.setState({
            iDisplayLength: iDisplayLength + 10
        });
    };
    initListTrouble = (text) => {
        const data = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 20,
                search: text || ''
            }
        };
        this.props.actionDetailTicket.getListTrouble(data);
    };
    loadMoreTrouble = (text) => {
        const { dataLengthTrouble } = this.state;

        const data = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: dataLengthTrouble + 10,
                search: text || ''
            }
        };
        this.props.actionDetailTicket.getListTrouble(data);
        this.setState({
            dataLengthGeoLocation: dataLengthTrouble + 10
        });
    };
    handleAddStore = (listStore = []) => {
        const { solution } = this.state;
        this.setState({
            listTroubleData: listStore,
            showModalListTrouble: false
        });

        if (listStore?.length > 0) {
            let textSolution = '';
            textSolution = listStore.map((item) => item?.name);
            let newSolution = solution + textSolution;
            this.setState({
                solution: newSolution
            });
            console.log(textSolution, 'listTroubleDatalistTroubleData');
        }
    };
    render() {
        const { xworkData, listLocationGeo, listGroupTicket } = this.props;
        const { component } = xworkData;
        if (component === undefined) {
            return null;
        }
        const selectedStartDate = convertUTCDateToLocalDate(
            new Date(this.state.startDate)
        );
        const selectedEndDate = convertUTCDateToLocalDate(
            new Date(this.state.endDate)
        );
        const {
            WrapperContainerTicket,
            ModalAddReceiver,
            ModalCalendarNew,
            ModalListServiceV2
        } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('create_ticket')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    const { params } = this.props.route;
                    if (params) {
                        this.props.navigation.navigate('TicketList', params);
                    } else {
                        this.props.navigation.goBack();
                    }
                }}
                colorTitle>
                <ScrollView
                    style={{
                        marginTop: Mixins.scale(12)
                    }}>
                    {this.renderHeader()}
                    {this.state.selectedGroup?.serviceType !== 'GROUND_TASK' &&
                        this.renderContent()}
                </ScrollView>

                {this.renderButtonSave()}
                <ModalAddReceiver
                    isVisible={this.state.showModalAddReceiver}
                    titleModal={translate('add_user')}
                    onPressDimiss={() => {
                        this.setState({ showModalAddReceiver: false });
                    }}
                    handleSearchUser={this.handleSearchUser}
                    onPressSave={this.onPressSaveReceiver}
                    userReceiver={this.state.userReceiver}
                    listUser={this.props.listMemberGroup.data}
                />
                {this.state.showModalUser && (
                    <ModalAddFollowers
                        xworkData={this.props.xworkData}
                        isVisible={this.state.showModalUser}
                        titleModal={translate('add_watcher')}
                        onPressDimiss={() => {
                            this.setState({ showModalUser: false });
                        }}
                        handleSearchUser={this.handleSearchUser}
                        onPressSave={this.onPressSaveModal}
                        groupUserFollowers={this.state.groupUserFollowers}
                        listUser={
                            this.state.allUser
                                ? this.props.listUser.data
                                : this.props.listMemberGroup.data
                        }
                        allUser={this.state.allUser}
                        handleLoadMoreUser={this.handleLoadMoreUser}
                    />
                )}
                {this.state.isShowModalCalendar && (
                    <ModalCalendarNew
                        isVisible={this.state.isShowModalCalendar}
                        endDate={selectedEndDate}
                        startDate={selectedStartDate}
                        chooseDate={this.state.chooseDate}
                        minDate={new Date()}
                        onChangeSelectedDate={this.onChangeSelectedDate}
                        onPressDimiss={this.onPressDimissModalCalendar}
                    />
                )}

                {this.state.isShowModalListService && (
                    <ModalListServiceV2
                        isVisible={this.state.isShowModalListService}
                        titleModal={translate('choose_supermarket')}
                        isShowCodeService={true}
                        listData={listLocationGeo?.data}
                        serviceSelected={this.state.locationSelected}
                        hideModalSynch={this.hideModalLocation}
                        onPressSave={(item) =>
                            this.setState({
                                isShowModalListService: false,
                                locationSelected: item
                            })
                        }
                        onEndReached={this.loadMoreLocation}
                        actionSearch={this.initLocationGeo}
                    />
                )}
                {this.state.isShowModalGroup && (
                    <ModalListServiceV2
                        isVisible={this.state.isShowModalGroup}
                        titleModal={translate('select_group')}
                        showSearch
                        listData={listGroupTicket.data}
                        serviceSelected={this.state.selectedGroup}
                        hideModalSynch={this.hideModalGroup}
                        onPressSave={(item) =>
                            this.setState(
                                {
                                    isShowModalGroup: false,
                                    selectedGroup: item
                                },
                                () => {
                                    this.initMemberGroup(item);
                                }
                            )
                        }
                        onEndReached={this.loadMoreGroup}
                        actionSearch={this.searchGroup}
                    />
                )}

                {this.state.showModalListTrouble && (
                    <ModalListTrouble
                        isVisible={this.state.showModalListTrouble}
                        titleModal={translate('workaround')}
                        onPressDimiss={() => {
                            this.setState({
                                showModalListTrouble: false
                            });
                        }}
                        handleLoadMoreUser={this.loadMoreTrouble}
                        handleSearchStore={this.initListTrouble}
                        listStore={this.props.listTrouble.data}
                        storeSelected={this.state.listTroubleData}
                        xworkData={this.props.xworkData}
                        handleAddStore={this.handleAddStore}
                    />
                )}
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        listPriority: state.ticketReducer.listPriority,
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listLocationGeo: state.ticketReducer.listLocationGeo,
        listMemberGroup: state.groupTicketReducer.listMemberGroup,
        listTicket: state.ticketReducer.listTicket,
        detailGroupTicket: state.groupTicketReducer.detailGroupTicket,
        listNewFeed: state.groupTicketReducer.listNewFeed,
        dataLocal: state.groupTicketReducer.dataLocal,
        listUser: state.groupTicketReducer.listUser,
        listTrouble: state.detailTicketReducer.listTrouble
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionTicket: bindActionCreators(_actionTicket, dispatch),
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionGroundTask: bindActionCreators(_actionGroundTask, dispatch),
        actionDetailTicket: bindActionCreators(_actionDetailTicket, dispatch)
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateTicket);
