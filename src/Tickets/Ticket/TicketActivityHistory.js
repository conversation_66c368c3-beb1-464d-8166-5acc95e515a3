import React, { PureComponent } from 'react';
import { View, FlatList, Image } from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from '../action';
import * as _actionTicket from './action';
import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import { stylesTicket } from './stylesTicket';
import { CONST_API } from '../../constant';
import { formatRelativeTime } from '../../utility';

const { translate } = global.props.getTranslateConfig();

class TicketHistoryActivity extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {};
    }

    async componentDidMount() {
        this.initData();
    }
    initData = () => {
        const { detailTicket } = this.props;
        this.props.actionTicket.getListActivity(
            detailTicket.data.ticket.id,
            true
        );
    };
    renderItem = ({ item, index }) => {
        return (
            <View
                key={index}
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginTop: Mixins.scale(24)
                }}>
                <Image
                    style={{
                        height: Mixins.scale(32),
                        width: Mixins.scale(32),
                        borderRadius: Mixins.scale(16)
                    }}
                    source={{
                        uri: `${CONST_API.baseAvatarURI}${item?.user.profile?.image}`
                    }}
                />
                <View style={{ marginLeft: 16, flex: 1 }}>
                    <View style={stylesTicket.viewRowAlignItems}>
                        <MyText
                            numberOfLines={1}
                            style={{
                                color: Colors.BLACK,
                                flex: 1
                            }}
                            text={`${item?.user?.username}-${item?.user?.profile?.lastName} ${item?.user?.profile?.firstName}`}
                        />
                        <MyText
                            style={{
                                textAlign: 'right',
                                color: Colors.GRAYF7
                            }}
                            numberOfLines={-2}
                            text={formatRelativeTime(item?.logTime)}
                        />
                    </View>
                    <MyText numberOfLines={2} text={item?.description} />
                </View>
            </View>
        );
    };
    render() {
        const { xworkData, listActivity } = this.props;
        const { component } = xworkData;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket } = component;

        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    this.props.navigation.goBack();
                }}
                actionRetry={this.initData}
                isSuccess={!listActivity?.isError}
                nameTitle={translate('activity')}
                messageLoading={translate('getting_activity_list')}
                messageError={translate('something_wrong_server')}
                isError={listActivity.isError}
                isLoading={listActivity.isFetching}>
                <View
                    style={{
                        flex: 1,
                        paddingHorizontal: 16
                    }}>
                    <FlatList
                        data={listActivity.data}
                        renderItem={this.renderItem}
                        extraData={this.props || this.state}
                    />
                </View>
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.ticketReducer.listGroupTicket,
        detailTicket: state.ticketReducer.detailTicket,
        listFileTicket: state.ticketReducer.listFileTicket,
        listActivity: state.ticketReducer.listActivity
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch),
        actionTicket: bindActionCreators(_actionTicket, dispatch)
    };
};
export default connect(
    mapStateToProps,
    mapDispatchToProps
)(TicketHistoryActivity);
