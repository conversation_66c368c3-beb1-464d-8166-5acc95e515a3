import { Mixins, XworkColor as Colors } from '@mwg-sdk/styles';
import { MyText } from '@mwg-kits/components';
import React, { PureComponent } from 'react';
import {
    FlatList,
    Image,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as _actionHome from './action';
export const COLOR_LOADING = '#9FC6FF';
import Toast from 'react-native-toast-message';

import { GlobalStore } from 'redux-micro-frontend';
const { translate } = global.props.getTranslateConfig();
class Tickets extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            txtSearch: '',
            iDisplayLength: 10,
            isLoadMore: false,
            isRefresh: false,
            isShowFetching: false,
            isShowModalSelectedGroup: false,
            itemGroupSelected: null,
            owner: false
        };
        this.timeOut = -1;
        this.globalStore = GlobalStore.Get();
    }

    componentDidMount() {
        this.props.actionHome.getListGroupTicket();
        this.props.navigation.addListener('blur', () => {
            this.setState({
                isShowModalSelectedGroup: false
            });
        });
        this.setState({ isLoadMore: false });
    }

    async componentDidUpdate(prevProps) {
        const { xworkData, listGroupTicket } = this.props;
        if (xworkData?.constants !== prevProps.xworkData?.constants) {
            const data = {
                owner: this.state.owner ? 1 : -1
            };
            this.props.actionHome.getListGroupTicket(data);
        }
        if (
            listGroupTicket.data !== prevProps.listGroupTicket?.data &&
            listGroupTicket?.data?.length === 10
        ) {
            this.setState({
                iDisplayLength: 10
            });
        }
    }

    initData = () => {
        this.setState(
            {
                isShowFetching: true
            },
            () => {
                const body = {
                    owner: this.props.owner ? 1 : -1,
                    iDisplayStart: 0,
                    iDisplayLength: 10,
                    search: ''
                };
                this.props.actionHome.getListGroupTicket(body);
            }
        );
        this.setState({
            iDisplayLength: 10,
            isShowFetching: false
        });
    };

    _renderItem = ({ item }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    this.props.navigation.navigate('TicketList', item);
                }}
                style={style.btnItem}>
                <Image
                    style={style.imgGroup}
                    source={{ uri: item?.avatar || 'ic_image_create' }}
                />
                <View style={{ marginLeft: Mixins.scale(15), flex: 1 }}>
                    <MyText
                        numberOfLines={1}
                        text={item?.name}
                        addSize={1}
                        typeFont="medium"
                        style={{
                            color: Colors.BLACK
                        }}
                    />
                    <MyText
                        numberOfLines={1}
                        text={`${translate('job')}: ${item?.serviceName}`}
                        addSize={-1}
                        style={{
                            marginTop: Mixins.scale(4),
                            color: Colors.GRAYF9
                        }}
                    />
                </View>
                <TouchableOpacity
                    hitSlop={{
                        top: 10,
                        bottom: 10,
                        left: 10,
                        right: 10
                    }}
                    style={style.btnMore}
                    onPress={async () => {
                        try {
                            const response =
                                await this.props.actionHome.setPriorityGroup(
                                    item.id,
                                    !item.priority
                                );
                            if (!response) {
                                this.setState({
                                    isShowFetching: true,
                                    iDisplayLength: 10
                                });
                                let action = {
                                    type: 'GET_LIST_GROUP',
                                    payload: { getListGroup: true }
                                };
                                this.globalStore.DispatchAction(
                                    'XworkStore',
                                    action
                                );
                                const data = {
                                    owner: this.state.owner ? 1 : -1,
                                    search: this.state.txtSearch
                                        ? this.state.txtSearch
                                        : ''
                                };
                                this.props.actionHome.getListGroupTicket(
                                    data,
                                    true
                                );
                            }
                        } catch (e) {
                            Toast.show({
                                type: 'error',
                                text1: 'Lỗi yêu thích nhóm.'
                            });
                        }
                    }}>
                    <Image
                        style={{
                            height: Mixins.scale(20),
                            width: Mixins.scale(20)
                        }}
                        resizeMode="contain"
                        source={{
                            uri:
                                item.priority === 1
                                    ? 'ic_heart_red'
                                    : 'ic_heart'
                        }}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    hitSlop={{
                        top: 10,
                        bottom: 10,
                        left: 10,
                        right: 10
                    }}
                    style={style.btnMore}
                    onPress={() => {
                        if (!item.isDelete && !item.isLeave) {
                            this.props.navigation.navigate('DetailGroup', item);
                            return;
                        }
                        this.setState({
                            isShowModalSelectedGroup: true,
                            itemGroupSelected: item
                        });
                    }}>
                    <Image
                        style={{
                            height: Mixins.scale(20),
                            width: Mixins.scale(20),
                            tintColor: Colors.BLACK
                        }}
                        resizeMode="contain"
                        source={{ uri: 'ic_more_vertical' }}
                    />
                </TouchableOpacity>
            </TouchableOpacity>
        );
    };

    renderListGroup = () => {
        return (
            <View style={{ flex: 1 }}>
                <FlatList
                    style={{ flex: 1 }}
                    data={this.props.listGroupTicket?.data}
                    ListHeaderComponent={this.renderHeader}
                    ListEmptyComponent={this.renderEmpty}
                    renderItem={this._renderItem}
                    contentContainerStyle={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        paddingBottom: 35
                    }}
                    showsVerticalScrollIndicator={false}
                    onEndReached={this.handleLoadMore}
                    ListFooterComponent={() => {
                        if (
                            this.state.isLoadMore &&
                            this.props.listGroupTicket.isFetching
                        ) {
                            return (
                                <View
                                    style={{
                                        alignItems: 'center',
                                        marginTop: 10
                                    }}>
                                    <MyText
                                        style={{
                                            color: Colors.GRAY_PLACEHODER
                                        }}
                                        text={translate('loading_more')}
                                    />
                                </View>
                            );
                        }
                        return null;
                    }}
                    scrollEventThrottle={10}
                    initialNumToRender={10}
                    windowSize={11}
                    onEndReachedThreshold={0.1}
                    bounces
                    refreshControl={
                        <RefreshControl
                            progressViewOffset={0}
                            refreshing={this.state.isRefresh}
                            tintColor={COLOR_LOADING}
                            colors={[COLOR_LOADING]}
                            onRefresh={this.handleResfresh}
                        />
                    }
                    extraData={this.state || this.props}
                />
            </View>
        );
    };

    handleLoadMore = () => {
        const { txtSearch, iDisplayLength, owner } = this.state;
        const { listGroupTicket } = this.props;
        const body = {
            owner: owner ? 1 : -1,
            iDisplayStart: 0,
            iDisplayLength: iDisplayLength + 10,
            search: txtSearch
        };
        if (
            body.iDisplayLength - listGroupTicket.data.length <= 10 ||
            body.iDisplayLength - listGroupTicket.data.length < 20
        ) {
            this.setState(
                {
                    isLoadMore: true
                },
                async () => {
                    await this.props.actionHome.getListGroupTicket(body, true);
                    this.setState({
                        isLoadMore: false,
                        iDisplayLength: body.iDisplayLength
                    });
                    return;
                }
            );
        }
    };

    onChangeText = (txtSearch) => {
        const data = {
            owner: this.state.owner ? 1 : -1,
            search: txtSearch
        };
        this.setState(
            { txtSearch: txtSearch, isShowFetching: true, iDisplayLength: 10 },
            () => {
                if (
                    this.timeOut !== undefined &&
                    this.timeOut !== null &&
                    this.timeOut >= 0
                ) {
                    clearTimeout(this.timeOut);
                }
                this.timeOut = setTimeout(async () => {
                    this.props.actionHome.getListGroupTicket(data);
                }, 500);
            },
            () => {
                this.setState({ isShowFetching: false });
            }
        );
    };

    renderFilterGroup = () => {
        const { owner, txtSearch } = this.state;
        return (
            <View style={{ flexDirection: 'row' }}>
                <TouchableOpacity
                    style={[
                        style.btFilterGroup,
                        {
                            backgroundColor: !owner
                                ? Colors.DARK_BLUE_10
                                : Colors.GRAYF5,
                            borderColor: !owner
                                ? Colors.DARK_BLUE_60
                                : Colors.GRAYF7
                        }
                    ]}
                    onPress={() => {
                        const body = {
                            iDisplayStart: 0,
                            iDisplayLength: 10,
                            search: txtSearch
                        };
                        this.props.actionHome.getListGroupTicket(body);
                        this.setState({
                            owner: false,
                            iDisplayLength: 10,
                            isShowFetching: true
                        });
                    }}>
                    <MyText
                        text={translate('all')}
                        style={{
                            color: !owner ? Colors.DARK_BLUE_60 : Colors.GRAYF7
                        }}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        style.btFilterGroup,
                        {
                            backgroundColor: owner
                                ? Colors.DARK_BLUE_10
                                : Colors.GRAYF5,
                            borderColor: owner
                                ? Colors.DARK_BLUE_60
                                : Colors.GRAYF7
                        }
                    ]}
                    onPress={() => {
                        this.setState({
                            owner: true,
                            iDisplayLength: 10,
                            isShowFetching: true
                        });
                        const body = {
                            owner: 1,
                            iDisplayStart: 0,
                            iDisplayLength: 10,
                            search: txtSearch
                        };
                        this.props.actionHome.getListGroupTicket(body);
                    }}>
                    <MyText
                        text={translate('created_group')}
                        style={{
                            color: owner ? Colors.DARK_BLUE_60 : Colors.GRAYF7
                        }}
                    />
                </TouchableOpacity>
            </View>
        );
    };

    renderHeader = () => {
        return (
            <View
                style={{
                    width: '90%'
                }}>
                <View style={style.input}>
                    <Image
                        style={style.icon}
                        resizeMode="contain"
                        source={{ uri: 'ic_search_ticket' }}
                    />
                    <TextInput
                        value={this.state.txtSearch}
                        onChangeText={this.onChangeText}
                        style={style.stl_input}
                        placeholder={translate('search')}
                        placeholderTextColor={Colors.GRAY_PLACEHODER}
                    />
                    {this.state.txtSearch &&
                        this.state.txtSearch.length > 0 && (
                            <TouchableOpacity
                                onPress={() => {
                                    this.setState({
                                        txtSearch: ''
                                    });
                                    this.props.actionHome.getListGroupTicket();
                                }}
                                hitSlop={{
                                    top: 10,
                                    bottom: 10,
                                    right: 10,
                                    left: 10
                                }}
                                style={{}}>
                                <Image
                                    style={{
                                        height: Mixins.scale(20),
                                        width: Mixins.scale(20)
                                    }}
                                    source={{
                                        uri: 'ic_error_ticket'
                                    }}
                                />
                            </TouchableOpacity>
                        )}
                </View>
                {this.renderFilterGroup()}
            </View>
        );
    };

    handleListMember = async (item) => {
        const parmas = {
            pageRequest: {
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: ''
            },
            requestId: '',
            supportServiceId: item?.id
        };
        this.props.actionHome.getListMemberGroup(parmas);
    };
    handleResfresh = () => {
        this.setState({
            isRefresh: true,
            txtSearch: '',
            iDisplayStart: 0,
            iDisplayLength: 10
        });
        setTimeout(async () => {
            const body = {
                owner: this.state.owner ? 1 : -1,
                iDisplayStart: 0,
                iDisplayLength: 10,
                search: ''
            };
            this.props.actionHome.getListGroupTicket(body);
            this.setState({ isRefresh: false });
        }, 250);
    };

    renderEmpty = () => {
        const { xworkData } = this.props;
        const { component } = xworkData;
        const { BaseViewWithLoading } = component;

        return (
            <ScrollView
                showsVerticalScrollIndicator={false}
                style={{
                    alignSelf: 'center'
                }}
                contentContainerStyle={{
                    flexGrow: 1,
                    marginTop: Mixins.scale(150),
                    justifyContent: 'center',
                    alignItems: 'center'
                }}>
                <BaseViewWithLoading
                    isLoading={false}
                    isSuccess
                    isEmpty
                    contentText={
                        this.state.txtSearch?.length > 0
                            ? translate('no_group_found')
                            : translate('no_group')
                    }
                />
            </ScrollView>
        );
    };
    handleRemove = async () => {
        try {
            global.props.showLoader();

            const data = {
                supportServiceId: this.state.itemGroupSelected?.id,
                action: 'DELETE'
            };
            const successRemove = await this.props.actionHome.deleteGroup(data);
            global.props.hideLoader();
            if (successRemove) {
                this.initData();
                Toast.show({
                    type: 'success',
                    text1: translate('success_delete_group')
                });
                setTimeout(() => {
                    Toast.hide();
                }, 1000);
            }
        } catch (error) {
            global.props.hideLoader();
            Toast.show({
                type: 'error',
                text1: translate('fail_delete_group')
            });
        }
    };

    onPressButtonNewFeed = () => {
        this.props.navigation.navigate('NewFeed');
    };
    handleOutGroup = async () => {
        this.setState({
            isShowModalSelectedGroup: false
        });
        global.props.alert({
            show: true,
            title: translate('leave_group'),
            titleColor: { color: Colors.DARK_RED_30 },
            message: translate('confirm_leave_group'),
            confirmText: translate('leave_group'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },
            onConfirmPressed: () => {
                global.props.alert({ show: false });
                this.confirmOutGroup();
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };
    confirmOutGroup = async () => {
        try {
            const { fullProfile } = this.props.xworkData;

            const body = {
                supportServiceId: this.state.itemGroupSelected.id,
                userId: fullProfile.id
            };
            const responeRemoveMember =
                await this.props.actionHome.removeMemberGroup(body);
            this.initData();
            if (!responeRemoveMember.error) {
                this.setState({
                    iDisplayLength: 10
                });
                Toast.show({
                    type: 'success',
                    text1: translate('success_leave_group')
                });
                setTimeout(() => {
                    Toast.hide();
                }, 1000);
            }
        } catch (error) {
            Toast.show({
                type: 'error',
                text1: translate('fail_leave_group')
            });
        }
    };

    removeGroup = () => {
        this.setState({
            isShowModalSelectedGroup: false
        });
        global.props.alert({
            show: true,
            title: translate('delete_group'),
            titleColor: { color: Colors.DARK_RED_30 },
            message: translate('confirm_delete_group'),
            confirmText: translate('delete'),
            confirmButtonTextStyle: {
                color: Colors.DARK_RED_30
            },
            cancelText: translate('cancel'),
            cancelButtonTextStyle: {
                color: Colors.GRAYF7
            },

            onConfirmPressed: () => {
                global.props.alert({ show: false });
                this.handleRemove();
            },
            onCancelPressed: () => {
                global.props.alert({ show: false });
            }
        });
    };
    //
    render() {
        const { xworkData, listGroupTicket } = this.props;
        const { component, screenName } = xworkData;
        if (component === undefined) {
            return null;
        }
        const { WrapperContainerTicket, ModalSelectedGroup } = component;
        return (
            <WrapperContainerTicket
                navigation={this.props.navigation}
                nameTitle={translate('your_group')}
                centerAlign={false}
                colorBackButton={Colors.DARK_BLUE_50}
                onPressBack={() => {
                    if (screenName === 'CreateTicket') {
                        this.props.navigation.navigate('Home');
                    } else {
                        this.props.navigation.goBack();
                    }
                }}
                buttonRightOutsize={() => {
                    this.props.navigation.navigate('CreateGroupXticket');
                }}
                actionRetry={this.initData}
                imgButtonOutSize="ic_plus"
                isSuccess={!listGroupTicket?.isError}
                messageLoading={translate('getting_group_list')}
                messageError={translate('something_wrong_server')}
                isError={listGroupTicket.isError}
                isLoading={
                    listGroupTicket.isFetching &&
                    !this.state.isLoadMore &&
                    !this.state.isRefresh &&
                    !this.state.isShowFetching
                }>
                <View
                    style={{
                        flex: 1,
                        marginTop: Mixins.scale(12)
                    }}>
                    {/* {this.renderHeader()} */}
                    {this.renderListGroup()}
                </View>
                {this.state.isShowModalSelectedGroup && (
                    <ModalSelectedGroup
                        isVisible={this.state.isShowModalSelectedGroup}
                        itemGroupSelected={this.state.itemGroupSelected}
                        onPressDetailGroup={() => {
                            this.setState(
                                {
                                    isShowModalSelectedGroup: false
                                },
                                () => {
                                    this.props.navigation.navigate(
                                        'DetailGroup',
                                        this.state.itemGroupSelected
                                    );
                                }
                            );
                        }}
                        onPressLeaveGroup={this.handleOutGroup}
                        onPressRemoveGroup={this.removeGroup}
                        onPressDimiss={() => {
                            this.setState({
                                isShowModalSelectedGroup: false
                            });
                        }}
                    />
                )}
            </WrapperContainerTicket>
        );
    }
}

const mapStateToProps = function (state) {
    return {
        xworkData: state.groupTicketReducer.xworkData,
        listGroupTicket: state.groupTicketReducer.listGroupTicket,
        listMemberGroup: state.groupTicketReducer.listMemberGroup
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        actionHome: bindActionCreators(_actionHome, dispatch)
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(Tickets);
const style = StyleSheet.create({
    btFilterGroup: {
        alignItems: 'center',
        borderRadius: Mixins.scale(22),
        borderWidth: 1,
        heigth: Mixins.scale(40),
        justifyContent: 'center',
        marginRight: Mixins.scale(16),
        marginTop: Mixins.scale(24),
        paddingHorizontal: Mixins.scale(8),
        paddingVertical: Mixins.scale(10),
        width: Mixins.scale(135)
    },
    btnItem: {
        alignItems: 'center',
        borderColor: Colors.DARK_ORANGE_15,
        borderRadius: Mixins.scale(20),
        borderWidth: 0.5,
        flexDirection: 'row',
        height: Mixins.scale(74),
        justifyContent: 'space-between',
        marginTop: Mixins.scale(16),
        paddingHorizontal: Mixins.scale(10),
        width: '90%'
    },
    btnMore: {
        alignItems: 'center',
        height: Mixins.scale(40),
        justifyContent: 'center',
        marginVertical: Mixins.scale(10),
        width: Mixins.scale(40)
    },
    icon: {
        height: Mixins.scale(16),
        marginLeft: Mixins.scale(4),
        marginRight: Mixins.scale(12),
        width: Mixins.scale(16)
    },

    imgGroup: {
        borderRadius: Mixins.scale(8),
        height: Mixins.scale(44),
        width: Mixins.scale(44)
    },

    input: {
        alignItems: 'center',
        backgroundColor: Colors.GRAYF5,
        borderRadius: Mixins.scale(12),
        flex: 1,
        flexDirection: 'row',
        height: Mixins.scale(56),
        marginLeft: 'auto',
        marginRight: 'auto',
        paddingHorizontal: Mixins.scale(10)
    },
    stl_input: {
        color: Colors.BLACK,
        flex: 1
    }
});
