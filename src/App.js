import { enableScreens } from 'react-native-screens';
import React, { Component } from 'react';

import { NavigationContainer } from '@react-navigation/native';
import {
    SafeAreaProvider,
    SafeAreaInsetsContext
} from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import MainNavigator from './navigator/mainNavigator';
import { store } from './store/index';
import Toast from 'react-native-toast-message';
import { toastConfig } from './Tickets/GroupTicket/MemberGroupTicket';
enableScreens();
export default class App extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    render() {
        return (
            <SafeAreaProvider>
                <Provider store={store}>
                    <NavigationContainer>
                        <SafeAreaInsetsContext.Consumer>
                            {(insets) => {
                                return (
                                    <MainNavigator
                                        {...insets}
                                        {...this.props}
                                    />
                                );
                            }}
                        </SafeAreaInsetsContext.Consumer>
                    </NavigationContainer>
                </Provider>
                <Toast position="bottom" config={toastConfig} />
            </SafeAreaProvider>
        );
    }
}
