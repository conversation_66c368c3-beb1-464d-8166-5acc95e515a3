import AsyncStorage from '@react-native-async-storage/async-storage';
import { helper } from '@mwg-kits/common';
import Config from 'react-native-config';
// const HOSTNAME = 'wss://chatdev.tgdd.vn/chat';

const { EventEmitter } = require('eventemitter3');
export const makeid = (length = 4) => {
    let result = '';
    const characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        );
    }
    return result;
};

export const generateID = (clientID, currentTime = new Date().getTime()) =>
    `${clientID}_${currentTime}_${makeid(4)}`;

// Exponential back-off configuration for auto-reconnect
const RECONNECT_VALUE_MIN = 3000; // 1 second
const RECONNECT_VALUE_MAX = 1000 * 60; // 1 minute
const RECONNECT_VALUE_FACTOR = 1.4;
let reconnectValue = RECONNECT_VALUE_MIN;
let timeoutId = null;
export default class WSService {
    constructor() {
        if (WSService._instance) {
            return WSService._instance;
        }
        WSService._instance = this;

        // this.url = 'wss://chat.tgdd.vn/chat';
        this.url = `${Config.WS}${Config.WS_PRE}`;
        this.connection = null;
        this.messageCount = 0;
        this.callbacks = {};
        this.id = null;
        this.clientID = null;
        this.events = {};
        this.rooms = [];
        this.state = 'disconnected';
        this.stageAuthentication = 'INITIAL';
        this.request = [];
        this._opened = false;
        this.autoReconnect = true;
        this.isReconnecting = false;
        this._internalEvent = new EventEmitter();
        this.messageQueues = [];
        return WSService._instance;
    }

    async connect(callback) {
        try {
            if (timeoutId > 0) {
                clearTimeout(timeoutId);
                timeoutId = -1;
            }
            console.log(
                `WEBSOCKET CONNECT AT ${new Date()} WITH STATE ${this.state}`
            );

            // store.dispatch(resquestRegister(this.isReconnecting));

            const subToken = await AsyncStorage.getItem(
                'SUBTOKEN_NOTIFICATION'
            );
            const token_access = await AsyncStorage.getItem('TOKEN_ACCESS');
            if (!helper.IsValidateObject(token_access)) {
                if (helper.IsValidateObject(this.connection)) {
                    // close connect here....
                    // this.stageAuthentication = 'UNLEASED';
                    reconnectValue = RECONNECT_VALUE_MIN;
                }
                return;
            }
            console.log(`WEBSOCKET STATE IS ${subToken}`);

            if (!helper.IsValidateObject(subToken)) {
                reconnectValue = RECONNECT_VALUE_MIN;
                return;
            }
            console.log(`WEBSOCKET SUBTOKEN IS ${subToken}`);

            //! This trick here because Service BackEnd response with "" (doubleQuotes)
            // const withoutQuotesSubtoken = subToken?.replaceAll('"', '');
            const lastCharacter = subToken.length - 1;
            const withoutQuotesSubtoken = subToken?.slice(1, lastCharacter);
            this.connection = new WebSocket(this.url, [
                '0',
                utf8ToHex(withoutQuotesSubtoken)
            ]);
            this.connection.onopen = (arg) => this.onOpen(arg);
            this.connection.onmessage = (e) => {
                try {
                    if (e) {
                        const msg = JSON.parse(e?.data);
                        this.onMessage(msg);
                        if (callback) {
                            callback?.onmessage && callback.onmessage(msg);
                        }
                    }
                } catch (error) {
                    console.log('WEBSOCKET ONMESSSAGE ERROR:', error);
                }
            };

            this.connection.onerror = (e) => this.onError(e);

            this.connection.onclose = (e) => this.onClose(e);
        } catch (err) {
            console.log('====================================');
            console.log(err, 'ERROR WHEN CONNECTING WS');
            console.log('====================================');
        }
    }
    updateState(state) {
        this.stageAuthentication = state;
    }

    isConnecting() {
        return (
            this.connection &&
            this.connection.readyState ===
                this.connection.constructor.CONNECTING
        );
    }

    isConnected() {
        return (
            this.connection &&
            this.connection.readyState === this.connection.constructor.OPEN
        );
    }

    onOpen = () => {
        this.stageAuthentication === 'INITIAL';
        console.log(`WEBSOCKET ON OPEN AT ${new Date()}}: `);
        this._opened = true;
    };

    onMessage = async (msg) => {
        this._internalEvent.emit('onMessage', msg);
        const { type } = msg;
        if (!type || type === undefined || type === null) {
            return;
        }

        console.log(
            `WEBSOCKET ONMESSAGE WITH TYPE ${type.toUpperCase()} <<::::::::::::>>\n ${JSON.stringify(
                msg
            )}`
        );
        switch (type) {
            case 'CONNECTED': {
                this.clientID = msg.id;
                this.state = 'connected';
                this.isReconnecting = false;
                reconnectValue = RECONNECT_VALUE_MIN;

                break;
            }

            case 'success': {
                // thông báo khi register 1 channel, group hay join group thành công.
                if (msg === undefined || msg === null) {
                    return;
                }

                break;
            }

            case 'text': {
                // handle msg text duplicate..

                break;
            }
            case 'ack': {
                // this.receiverText();
                break;
            }
            case 'seen':
                break;
            case 'ping':
                await this.sendWS({ type: 'pong' });
                break;
            default:
                // type error here...
                console.log('TYPE DEFAULT', msg);
                break;
        }
    };

    onClose = async (e) => {
        console.log(
            `WEBSOCKET CLOSE AT ${new Date()}: `,
            JSON.stringify(e),
            reconnectValue,
            this.stageAuthentication
        );
        this._internalEvent.emit('onClose', e);
        const wasOpen = this._opened;
        this._opened = false;
        this.state = 'closed';
        const subToken = await AsyncStorage.getItem('SUBTOKEN_NOTIFICATION');
        if (helper.IsValidateObject(subToken)) {
            if (this.stageAuthentication === 'UNLEASED') {
                console.log(
                    'User has been logout, no nedd to retry connection to websocket'
                );
                // MIN_BACKOFF = 3000;
                reconnectValue = RECONNECT_VALUE_MIN;
                return;
            }
            if (this.autoReconnect && (timeoutId === null || timeoutId !== 1)) {
                this.reConnection(wasOpen);
            }
        }
    };

    onError = (e) => {
        try {
            const errString = JSON.stringify(e);
            console.log(`WEBSOCKET ERROR AT ${new Date()}:`, errString);
            this.state = 'error';
            this._internalEvent.emit('onError', e);
            if (this.connection) {
                this.connection.close();
            }
        } catch (error) {
            console.log(error);
        }
    };

    reConnection = (wasOpen) => {
        // Use exponential back-off to reconnect if `autoReconnect` is set
        if (timeoutId > 0) {
            clearTimeout(timeoutId);
            timeoutId = -1;
        }
        if (wasOpen) {
            reconnectValue = RECONNECT_VALUE_MIN;
        } else {
            reconnectValue = Math.min(
                reconnectValue * RECONNECT_VALUE_FACTOR,
                RECONNECT_VALUE_MAX
            );
        }
        const timeoutReconnect = getRandomArbitrary(1, 2) * reconnectValue;
        console.log(
            `WEBSOCKET RECONNECT START AT ${new Date()} with timeout reconnect ${timeoutReconnect}`
        );
        timeoutId = setTimeout(() => {
            console.log(
                `WEBSOCKET RECONNECT END AT ${new Date()} with timeout reconnect`
            );
            clearTimeout(timeoutId);
            timeoutId = -1;
            console.log(`WEBSOCKET RECONNECT END AT ${timeoutId}`);
            this.isReconnecting = true;
            // global.props.showFlashMessage('top', {
            //     message: `WEBSOCKET RECONNECT START AT ${new Date()} with timeout reconnect ${timeoutReconnect}`,
            //     type: 'danger'
            // });

            this.connect();
        }, timeoutReconnect);
        console.log(`WEBSOCKET RECONNECT START AT ${timeoutId}`);
    };

    sendText({
        id = new Date().getTime().toString(),
        msg = '',
        nameRoom = ''
    }) {
        if (this.state !== 'connected') {
            return;
        }
        if (nameRoom === '') {
            return 'Lỗi không thể gửi được message';
        }

        const data = {
            id,
            type: 'text',
            payload: msg,
            source: '', // no need  to sent when send message to other
            destination: nameRoom
        };
        console.log('messega text ', data);
        this.connection.send(JSON.stringify(data));
    }

    registerChannel = (channelId) => {
        if (this.state !== 'connected') {
            return;
        }

        console.log('registerChannel ', this.state);
        const today = new Date();
        const channels = [];
        channels.push(channelId);
        const messageRegister = JSON.stringify({
            id: today.getTime().toString(),
            type: 'register',
            payload: channels,
            source: '',
            destination: ''
        });
        this.connection.send(messageRegister);
    };

    sendWS = (payload) => {
        try {
            // socket connection is required
            if (
                this.state !== 'connected' &&
                (timeoutId === null || timeoutId === -1)
            ) {
                // start connecting
                this.reConnection(true);
                // resolve with false
                return Promise.resolve(false);
            }
            const payloadMsg = JSON.stringify(payload);
            console.log(`WEBSOCKET SEND MS: ${payloadMsg}`);
            this.connection.send(payloadMsg);
            return Promise.resolve(true);
        } catch (error) {
            console.log(`WEBSOCKET SEND ERROR: ${error}`);
            return Promise.resolve(false);
        }
    };

    logout = () => {
        if (this.connection) {
            this.connection.close();
        }
        this.connection = null;
        this.messageCount = 0;
        this.callbacks = {};
        this.id = null;
        this.clientID = null;
        this.events = {};
        this.rooms = [];
        this.request = [];
        this._opened = false;
        this.autoReconnect = true;
        this.isReconnecting = false;
        this._internalEvent = new EventEmitter();
        this.messageQueues = [];
    };
}

export const utf8ToHex = (str) => {
    try {
        return Array.from(str)
            .map((c) =>
                c.charCodeAt(0) < 128
                    ? c.charCodeAt(0).toString(16)
                    : encodeURIComponent(c).replace(/\\%/g, '').toLowerCase()
            )
            .join('');
    } catch (error) {
        return error;
    }
};

const getRandomArbitrary = (min, max) => {
    return Math.random() * (max - min) + min;
};

/**
 * @description
 * Takes an Array<V>, and a grouping function,
 * and returns a Map of the array grouped by the grouping function.
 *
 * @param list An array of type V.
 * @param keyGetter A Function that takes the the Array type V as an input, and returns a value of type K.
 *                  K is generally intended to be a property key of V.
 *
 * @returns Map of the array grouped by the grouping function.
 */
// export function groupBy<K, V>(list: Array<V>, keyGetter: (input: V) => K): Map<K, Array<V>> {
//    const map = new Map<K, Array<V>>();
