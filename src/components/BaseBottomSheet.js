import React from 'react';
import {
    StyleSheet,
    Dimensions,
    View,
    TouchableOpacity,
    ScrollView,
    KeyboardAvoidingView,
    Platform
} from 'react-native';
import { Mixins, XworkColor } from '@mwg-sdk/styles';
import { Image, BaseModal, MyText } from '@mwg-kits/components';

const screenHeight = Dimensions.get('window').height;
const screenWidth = Dimensions.get('window').width;

const modalHeight = screenHeight * 0.27;
const modalWidth = screenWidth;

const BaseBottomSheet = ({
    isModalVisible,
    toggleModal,
    nameTitle,
    isShowHeader,
    isHeight,
    maxHeight,
    isSwipeDown = false,
    styleContainer,
    children
}) => {
    return (
        <BaseModal
            isVisible={isModalVisible}
            onBackButtonPress={toggleModal}
            onBackdropPress={toggleModal}
            style={styles.modalContainer}
            swipeDirection={isSwipeDown ? null : ['down']}
            onSwipeComplete={toggleModal}
            propagateSwipe
            transparent
            hideModalContentWhileAnimating
            animationInTiming={600}
            animationOutTiming={600}
            backdropTransitionInTiming={200}
            backdropTransitionOutTiming={200}>
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' && 'padding'}
                style={[
                    styles.modalContent,
                    {
                        minHeight: isHeight
                            ? screenHeight * isHeight
                            : modalHeight,
                        maxHeight: maxHeight
                            ? screenHeight * 0.9
                            : screenHeight * 0.5,
                        height: 'auto',
                        width: modalWidth,
                        zIndex: -1
                    }
                ]}>
                <View
                    style={{
                        width: Mixins.scaleSize(40),
                        height: Mixins.scaleSize(4),
                        backgroundColor: XworkColor.GRAYF6,
                        borderRadius: Mixins.scaleSize(2)
                    }}
                />
                {isShowHeader && (
                    <View
                        style={{
                            width: '100%',
                            height: Mixins.scaleSize(40),
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'flex-start',
                            marginVertical: Mixins.scaleSize(8)
                        }}>
                        <TouchableOpacity
                            onPress={toggleModal}
                            style={{ padding: 6, marginRight: 8 }}>
                            <Image
                                isLocal
                                source={{ uri: 'ic_close' }}
                                style={[Mixins.scaleImage(12, 12)]}
                                resizeMode="contain"
                            />
                        </TouchableOpacity>
                        <MyText
                            text={nameTitle}
                            style={{ color: XworkColor.GRAYF10 }}
                            addSize={3}
                            typeFont={'medium'}
                        />
                    </View>
                )}
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    style={[{ width: '100%' }, styleContainer]}>
                    {children}
                </ScrollView>
            </KeyboardAvoidingView>
        </BaseModal>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        alignItems: 'center',
        justifyContent: 'flex-end',
        margin: 0
    },
    modalContent: {
        backgroundColor: 'white',
        borderTopLeftRadius: Mixins.scaleSize(32),
        borderTopRightRadius: Mixins.scaleSize(32),
        paddingHorizontal: Mixins.scaleSize(22),
        paddingVertical: Mixins.scaleSize(20),
        justifyContent: 'flex-start',
        alignItems: 'center'
    }
});

export default BaseBottomSheet;
