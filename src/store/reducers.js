import { combineReducers } from 'redux';
import { groupTicketReducer } from '../Tickets/reducer';
import { ticketReducer } from '../Tickets/Ticket/reducer';
import { groundTaskReducer } from '../Tickets/Ticket/Details/GroundTask/reducer';
import { detailTicketReducer } from '../Tickets/Ticket/Details/reducer';

const rootReducer = combineReducers({
    groupTicketReducer,
    groundTaskReducer,
    ticketReducer,
    detailTicketReducer
});

export { rootReducer };
